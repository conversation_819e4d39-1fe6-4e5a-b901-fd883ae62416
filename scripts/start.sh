#!/bin/bash

# E-commerce Stack Startup Script
# This script ensures clean startup by stopping conflicting processes and restarting Docker services

set -e

# Optional args
REBUILD_FLAG="${1:-}"
REBUILD_TARGETS="${REBUILD_TARGETS:-}"

echo "🚀 Starting E-commerce Stack..."

# Function to kill processes on specific ports
kill_port() {
    local port=$1
    echo "🔍 Checking for processes on port $port..."
    
    # Find and kill processes using the port
    local pids=$(lsof -ti :$port 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        echo "⚠️  Found processes on port $port: $pids"
        echo "🔪 Killing processes: $pids"
        kill $pids 2>/dev/null || true
        sleep 2
        
        # Force kill if still running
        local remaining_pids=$(lsof -ti :$port 2>/dev/null || true)
        if [ ! -z "$remaining_pids" ]; then
            echo "🔨 Force killing remaining processes: $remaining_pids"
            kill -9 $remaining_pids 2>/dev/null || true
        fi
    else
        echo "✅ Port $port is free"
    fi
}

# Stop any existing Docker containers
echo "🛑 Stopping existing Docker containers..."
docker compose down 2>/dev/null || true

# Kill processes on commonly used ports
kill_port 3000  # Frontend
kill_port 3001  # Alternative frontend port
kill_port 8123  # Backend API
kill_port 5432  # Local PostgreSQL
kill_port 6379  # Local Redis
kill_port 6380  # Alternative Redis port

echo "🧹 Cleaning up..."
sleep 2

# Optional targeted rebuilds to speed up startup
if [ -n "$REBUILD_FLAG" ] || [ -n "$REBUILD_TARGETS" ]; then
    if [ -z "$REBUILD_TARGETS" ]; then
        # Default to rebuilding the most critical services for local dev
        REBUILD_TARGETS="api app"
    fi
    echo "🔁 Rebuilding images for: $REBUILD_TARGETS ..."
    docker compose build $REBUILD_TARGETS || true
fi

# Start the Docker stack (no rebuild by default for speed)
echo "🐳 Starting Docker Compose stack..."
docker compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🏥 Checking service health..."
docker compose ps

# Test API health
echo "🔍 Testing API health..."
if curl -s http://localhost:8123/health > /dev/null; then
    echo "✅ API is healthy"
else
    echo "⚠️  API health check failed"
fi

echo ""
echo "🎉 E-commerce Stack is ready!"
echo ""
echo "📱 Frontend:     http://localhost:3000"
echo "🔧 API:          http://localhost:8123"
echo "📚 API Docs:     http://localhost:8123/docs"
echo "🗄️  Database:     http://localhost:8081 (pgweb)"
echo "🌸 Flower:       http://localhost:5555"
echo "📧 MailHog:      http://localhost:8025"
echo "📊 Grafana:      http://localhost:3030"
echo "📈 Prometheus:   http://localhost:9090"
echo ""
echo "🛑 To stop: docker compose down"
echo "📋 To view logs: docker compose logs -f [service-name]"
