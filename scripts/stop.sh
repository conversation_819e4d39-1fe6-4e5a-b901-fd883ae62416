#!/bin/bash

# E-commerce Stack Stop Script
# This script cleanly stops all Docker services and any remaining processes

set -e

echo "🛑 Stopping E-commerce Stack..."

# Stop Docker containers
echo "🐳 Stopping Docker Compose stack..."
docker compose down

# Function to kill processes on specific ports
kill_port() {
    local port=$1
    echo "🔍 Checking for remaining processes on port $port..."
    
    # Find and kill processes using the port
    local pids=$(lsof -ti :$port 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        echo "🔪 Killing remaining processes on port $port: $pids"
        kill $pids 2>/dev/null || true
        sleep 1
        
        # Force kill if still running
        local remaining_pids=$(lsof -ti :$port 2>/dev/null || true)
        if [ ! -z "$remaining_pids" ]; then
            echo "🔨 Force killing processes: $remaining_pids"
            kill -9 $remaining_pids 2>/dev/null || true
        fi
    else
        echo "✅ Port $port is clean"
    fi
}

# Clean up any remaining processes
kill_port 3000  # Frontend
kill_port 3001  # Alternative frontend port
kill_port 8123  # Backend API
kill_port 5432  # Local PostgreSQL
kill_port 6379  # Local Redis
kill_port 6380  # Alternative Redis port

echo "✅ E-commerce Stack stopped successfully!"
echo ""
echo "🚀 To start again: ./start.sh"
