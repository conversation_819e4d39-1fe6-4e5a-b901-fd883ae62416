-- Create Airbyte database and user
CREATE DATABASE airbyte;
CREATE USER airbyte WITH ENCRYPTED PASSWORD 'airbyte';
GRANT ALL PRIVILEGES ON DATABASE airbyte TO airbyte;

-- Give schema privileges
\c airbyte app_user
GRANT USAGE, CREATE ON SCHEMA public TO airbyte;

-- Create Temporal database and user
CREATE DATABASE temporal;
CREATE USER temporal WITH ENCRYPTED PASSWORD 'temporal';
GRANT ALL PRIVILEGES ON DATABASE temporal TO temporal;
\c temporal app_user
GRANT USAGE, CREATE ON SCHEMA public TO temporal;

-- Create test_ecommerce_db database
CREATE DATABASE test_ecommerce_db;
GRANT ALL PRIVILEGES ON DATABASE test_ecommerce_db TO app_user;
\c test_ecommerce_db app_user
GRANT USAGE, CREATE ON SCHEMA public TO app_user;

-- Create nocodb database
CREATE DATABASE nocodb;
GRANT ALL PRIVILEGES ON DATABASE nocodb TO app_user;
\c nocodb app_user
GRANT USAGE, CREATE ON SCHEMA public TO app_user;