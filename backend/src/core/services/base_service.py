from typing import Any, Dict, Generic, List, Optional, Type, TypeVar

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")


class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """Base service class with common CRUD operations."""

    def __init__(self, model: Type[ModelType]):
        self.model = model

    # REMOVED: get(id) method - Use get_by_external_id instead
    # async def get(self, db: AsyncSession, id: Any) -> Optional[ModelType]:
    #     """DEPRECATED: Use get_by_external_id instead."""

    async def get_by_external_id(self, db: AsyncSession, external_id: str) -> Optional[ModelType]:
        """Get a single record by external_id."""
        from uuid import UUID

        try:
            external_uuid = UUID(external_id)
        except ValueError:
            return None

        # Check if model has external_id field
        if not hasattr(self.model, "external_id"):
            raise AttributeError(f"Model {self.model.__name__} does not have external_id field")

        result = await db.execute(select(self.model).where(self.model.external_id == external_uuid))
        return result.scalar_one_or_none()

    async def get_multi(self, db: AsyncSession, *, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """Get multiple records with pagination."""
        result = await db.execute(select(self.model).offset(skip).limit(limit))
        return result.scalars().all()

    async def create(self, db: AsyncSession, *, obj_in: CreateSchemaType) -> ModelType:
        """Create a new record."""
        if hasattr(obj_in, "model_dump"):
            obj_in_data = obj_in.model_dump()
        else:
            obj_in_data = obj_in.model_dump()

        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        return db_obj

    async def update(
        self, db: AsyncSession, *, db_obj: ModelType, obj_in: UpdateSchemaType | Dict[str, Any]
    ) -> ModelType:
        """Update an existing record."""
        if hasattr(obj_in, "model_dump"):
            update_data = obj_in.model_dump(exclude_unset=True)
        elif hasattr(obj_in, "dict"):
            update_data = obj_in.model_dump(exclude_unset=True)
        else:
            update_data = obj_in

        for field, value in update_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)

        await db.commit()
        return db_obj

    # REMOVED: remove(id) method - Use remove_by_external_id instead
    # async def remove(self, db: AsyncSession, *, id: int) -> ModelType:
    #     """DEPRECATED: Use remove_by_external_id instead."""

    async def remove_by_external_id(self, db: AsyncSession, *, external_id: str) -> Optional[ModelType]:
        """Delete a record by external_id."""
        from uuid import UUID

        try:
            external_uuid = UUID(external_id)
        except ValueError:
            return None

        # Check if model has external_id field
        if not hasattr(self.model, "external_id"):
            raise AttributeError(f"Model {self.model.__name__} does not have external_id field")

        result = await db.execute(select(self.model).where(self.model.external_id == external_uuid))
        obj = result.scalar_one_or_none()
        if obj:
            await db.delete(obj)
            await db.commit()
        return obj

    async def update_by_external_id(
        self, db: AsyncSession, *, external_id: str, obj_in: UpdateSchemaType | Dict[str, Any]
    ) -> Optional[ModelType]:
        """Update a record by external_id."""
        obj = await self.get_by_external_id(db, external_id)
        if obj:
            return await self.update(db, db_obj=obj, obj_in=obj_in)
        return None

    async def count(self, db: AsyncSession) -> int:
        """Count total records."""
        result = await db.execute(select(self.model).count())
        return result.scalar()
