import json
import logging
import time
import uuid

logger = logging.getLogger(__name__)


class RequestBodyLoggingMiddleware:
    """
    Unified ASGI middleware that logs all connection types (HTTP, WebSocket, etc.).
    Never blocks - collects metadata and logs summary after completion.
    Always logs full request/response bodies.
    """

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        """Unified logging for all ASGI connection types."""
        connection_type = scope["type"]
        connection_id = str(uuid.uuid4())[:8]
        start_time = time.time()

        # Collect connection metadata
        headers = dict((k.decode(), v.decode()) for k, v in scope.get("headers", []))

        # Track connection statistics
        stats = {
            "messages_received": 0,
            "messages_sent": 0,
            "bytes_received": 0,
            "bytes_sent": 0,
            "status_code": 200,  # HTTP status
            "response_headers": {},  # HTTP response headers
            "body_chunks": [] if connection_type == "http" else None,
            "websocket_messages": [] if connection_type == "websocket" else None  # Store all WS messages
        }

        # Non-blocking message interceptors
        async def receive_logging():
            message = await receive()
            # Track incoming messages
            stats["messages_received"] += 1

            # Collect WebSocket messages for logging
            if connection_type == "websocket" and stats["websocket_messages"] is not None:
                msg_data = {
                    "direction": "received",
                    "timestamp": time.time() - start_time
                }
                if "text" in message:
                    msg_data["type"] = "text"
                    msg_data["content"] = message["text"]
                    stats["bytes_received"] += len(message["text"])
                elif "bytes" in message:
                    msg_data["type"] = "binary"
                    msg_data["content"] = self._format_body(message["bytes"], "application/octet-stream")
                    stats["bytes_received"] += len(message["bytes"])
                stats["websocket_messages"].append(msg_data)
            else:
                # HTTP message tracking
                if "body" in message:
                    stats["bytes_received"] += len(message["body"])
                elif "bytes" in message:
                    stats["bytes_received"] += len(message["bytes"])
                elif "text" in message:
                    stats["bytes_received"] += len(message["text"])

            return message

        async def send_logging(message):
            # Track outgoing messages
            stats["messages_sent"] += 1

            # Collect WebSocket messages for logging
            if connection_type == "websocket" and stats["websocket_messages"] is not None:
                msg_data = {
                    "direction": "sent",
                    "timestamp": time.time() - start_time
                }
                if "text" in message:
                    msg_data["type"] = "text"
                    msg_data["content"] = message["text"]
                    stats["bytes_sent"] += len(message["text"])
                elif "bytes" in message:
                    msg_data["type"] = "binary"
                    msg_data["content"] = self._format_body(message["bytes"], "application/octet-stream")
                    stats["bytes_sent"] += len(message["bytes"])
                stats["websocket_messages"].append(msg_data)
            else:
                # HTTP response tracking
                if message["type"] == "http.response.start":
                    stats["status_code"] = message.get("status", 200)
                    # Collect response headers
                    response_headers = message.get("headers", [])
                    stats["response_headers"] = dict((k.decode(), v.decode()) for k, v in response_headers)

                # Collect HTTP response chunks for full logging
                if message["type"] == "http.response.body" and connection_type == "http":
                    chunk = message.get("body", b"")
                    stats["body_chunks"].append(chunk)
                    stats["bytes_sent"] += len(chunk)
                elif "body" in message:
                    stats["bytes_sent"] += len(message["body"])
                elif "bytes" in message:
                    stats["bytes_sent"] += len(message["bytes"])
                elif "text" in message:
                    stats["bytes_sent"] += len(message["text"])

            # Send immediately - NEVER BLOCK!
            await send(message)

        try:
            # Handle request body for HTTP (consume before app)
            request_body = b""
            if connection_type == "http" and scope.get("method") in ["POST", "PUT", "PATCH"]:
                request_body = await self._consume_request_body(receive)
                stats["bytes_received"] = len(request_body)

                # Create replay for consumed request body
                body_sent = False
                async def receive_replay():
                    nonlocal body_sent
                    if not body_sent:
                        body_sent = True
                        return {
                            "type": "http.request",
                            "body": request_body,
                            "more_body": False,
                        }
                    return {"type": "http.disconnect"}

                await self.app(scope, receive_replay, send_logging)
            else:
                await self.app(scope, receive_logging, send_logging)

        finally:
            # Log completion summary for all connection types
            process_time = time.time() - start_time

            if connection_type == "http":
                # Skip logging for health check and metrics endpoints
                path = scope.get("path")
                if path not in ["/health", "/metrics"]:
                    # HTTP-specific logging with complete request/response objects
                    response_body = b"".join(stats["body_chunks"]) if stats["body_chunks"] else b""
                    request_body_str = self._format_body(request_body, headers.get("content-type", ""))
                    response_body_str = self._format_body(response_body, "application/json")

                    # Clean request object - essential fields only
                    request_obj = {
                        "method": scope.get("method"),
                        "path": scope.get("path"),
                        "query_string": scope.get("query_string") or "",
                        "headers": headers,
                        "client": scope.get("client"),
                        "body": request_body_str
                    }

                    # Clean response object - essential fields only
                    response_obj = {
                        "status_code": stats["status_code"],
                        "headers": stats.get("response_headers", {}),
                        "body": response_body_str,
                        "process_time_seconds": round(process_time, 4)
                    }

                    logger.info(json.dumps(self._make_json_serializable({
                        "request": request_obj,
                        "response": response_obj
                    })))
            else:
                # WebSocket logging with complete message history
                websocket_data = {
                    "connection": {
                        "id": connection_id,
                        "type": connection_type,
                        "event": "completed",
                        "duration_seconds": f"{process_time:.4f}",
                        "messages_received": stats["messages_received"],
                        "messages_sent": stats["messages_sent"],
                        "bytes_received": stats["bytes_received"],
                        "bytes_sent": stats["bytes_sent"]
                    }
                }

                # Include complete message history
                if stats["websocket_messages"]:
                    websocket_data["messages"] = stats["websocket_messages"]

                logger.info(json.dumps(self._make_json_serializable(websocket_data)))

    async def _consume_request_body(self, receive):
        """Consume and return full request body for HTTP requests."""
        body_chunks = []

        while True:
            message = await receive()
            if message["type"] == "http.request":
                chunk = message.get("body", b"")
                body_chunks.append(chunk)

                if not message.get("more_body", False):
                    break

        return b"".join(body_chunks)

    def _make_json_serializable(self, obj):
        """Convert ASGI scope/data to JSON-serializable format."""
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return list(obj)  # Convert tuples to lists
        elif isinstance(obj, bytes):
            try:
                return obj.decode('utf-8')
            except UnicodeDecodeError:
                return f"<binary data: {len(obj)} bytes>"
        else:
            return obj

    def _format_body(self, body: bytes, content_type: str) -> str:
        """Format body for logging - always return full content."""
        if not body:
            return ""

        try:
            return body.decode()
        except UnicodeDecodeError:
            return f"<binary data: {len(body)} bytes>"



