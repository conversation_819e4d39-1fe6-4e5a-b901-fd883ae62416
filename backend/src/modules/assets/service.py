import json
import logging
from typing import List, Optional, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from core.config import get_settings

from core.services.base_service import BaseService
from modules.assets.models import Asset
from modules.assets.schemas import AssetCreate, AssetUpdate, MediaItem
from modules.products.models import Product
from modules.media.models import MediaJobStatus

logger = logging.getLogger(__name__)


class AssetService(BaseService[Asset, AssetCreate, AssetUpdate]):
    """Service for asset operations."""

    def __init__(self):
        super().__init__(Asset)

    async def parse_json_strings_recursively(self, obj: Any) -> Any:
        """Recursively parses all JSON strings in an object."""
        if obj is None or obj is None:
            return obj

        if isinstance(obj, str):
            try:
                parsed = json.loads(obj)
                if isinstance(parsed, (dict, list)):
                    return self.parse_json_strings_recursively(parsed)
                return parsed
            except json.JSONDecodeError:
                return obj

        if isinstance(obj, list):
            return [await self.parse_json_strings_recursively(item) for item in obj]

        if isinstance(obj, dict):
            result = {}
            for key, value in obj.items():
                result[key] = await self.parse_json_strings_recursively(value)
            return result

        return obj

    async def parse_featured_media(self, product_data: dict) -> Optional[dict]:
        """Helper function to parse featured media."""
        if not product_data.get("featured_media"):
            return None

        try:
            media_data = json.loads(product_data["featured_media"])
            return {
                "id": media_data.get("id"),
                "mediaContentType": media_data.get("media_content_type"),
                "preview": media_data.get("preview"),
                "alt": media_data.get("alt"),
                "status": media_data.get("status"),
            }
        except json.JSONDecodeError:
            logger.warning(f"Failed to parse featured_media for product {product_data.get('id')}")
            return None

    async def detect_media_in_json(self, obj: Any, path: str = "", parent_key: str = "") -> List[MediaItem]:
        """Function to detect media URLs in JSON data."""
        media_items = []

        if not obj:
            return media_items

        if isinstance(obj, str):
            url = obj.strip()

            # Check for image URLs
            if (
                url.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp', '.tiff')) or
                (url.startswith('https://cdn.shopify.com') and 'images' in path.lower())
            ):
                media_items.append(MediaItem(
                    type="detected_image",
                    id=f"detected_{path}_{url[-10:]}",
                    src=url,
                    alt=f"Detected image from {path}",
                    path=path,
                    key=parent_key,
                    data={"url": url, "path": path, "key": parent_key}
                ))

            # Check for video URLs
            if (
                url.endswith(('.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.m4v', '.mkv')) or
                (url.startswith('https://cdn.shopify.com') and 'video' in path.lower())
            ):
                media_items.append(MediaItem(
                    type="detected_video",
                    id=f"detected_{path}_{url[-10:]}",
                    src=url,
                    alt=f"Detected video from {path}",
                    path=path,
                    key=parent_key,
                    data={"url": url, "path": path, "key": parent_key}
                ))

            return media_items

        if isinstance(obj, list):
            for index, item in enumerate(obj):
                media_items.extend(await self.detect_media_in_json(item, f"{path}[{index}]", parent_key))
            return media_items

        if isinstance(obj, dict):
            for key, value in obj.items():
                new_path = f"{path}.{key}" if path else key
                media_items.extend(await self.detect_media_in_json(value, new_path, key))
            return media_items

        return media_items

    async def get_all_media_for_product(self, product: Product) -> List[MediaItem]:
        """Get all available media for a product."""
        media_items = []

        # Add regular images only (simplified approach)
        if product.images:
            for img in product.images:
                # Convert ProductImage model to dict to avoid serialization issues
                img_data = {
                    "id": img.id,
                    "external_id": img.external_id,
                    "product_id": img.product_id,
                    "variant_id": img.variant_id,
                    "src": img.src,
                    "alt": img.alt,
                    "width": img.width,
                    "height": img.height,
                    "position": img.position,
                    "full_json": img.full_json,
                    "metafields": img.metafields,
                    "created_at": img.created_at,
                    "updated_at": img.updated_at,
                    "source_updated_at": img.source_updated_at
                }
                media_items.append(MediaItem(
                    type="image",
                    id=str(img.id),
                    src=img.src,
                    alt=img.alt or product.title,
                    data=img_data,
                    source_type="product"
                ))

        return media_items

    async def get_assets_for_user(
        self,
        db: AsyncSession,
        user_id: int,
        page: int = 1,
        limit: int = 50,
        search: Optional[str] = None
    ) -> List[MediaItem]:
        """Get all assets (media) for a user's stores. NOTE: Uses database ID for internal relationships."""
        # Get user's stores
        from modules.stores.models import Store
        stores_result = await db.execute(
            select(Store).filter(Store.owner_id == user_id)
        )
        user_stores = stores_result.scalars().all()

        # If the user has no stores:
        # - In local dev, proceed with all stores to enable development workflows
        # - Otherwise, skip product-origin assets but still include AI-generated assets below
        if not user_stores:
            if get_settings().ENVIRONMENT == "local":
                from modules.stores.models import Store
                stores_result_all = await db.execute(select(Store))
                user_stores = stores_result_all.scalars().all()
            else:
                user_stores = []

        store_ids = [store.id for store in user_stores]

        # Get products with images and all related data
        query = select(Product).options(
            selectinload(Product.images),
            selectinload(Product.variants)
        ).filter(Product.store_id.in_(store_ids))

        if search:
            search_filter = f"%{search.lower()}%"
            query = query.filter(
                (Product.title.ilike(search_filter)) |
                (Product.description.ilike(search_filter)) |
                (Product.vendor.ilike(search_filter)) |
                (Product.product_type.ilike(search_filter)) |
                (Product.tags.ilike(search_filter))
            )

        products_result = await db.execute(query)
        products = products_result.scalars().all()

        # Extract media from all products
        all_media = []
        for product in products:
            media = await self.get_all_media_for_product(product)
            all_media.extend(media)

        # Also get AI-generated assets from MediaJob table
        from modules.media.models import MediaJob
        ai_assets_query = select(MediaJob).filter(
            MediaJob.user_id == user_id,
            MediaJob.status == MediaJobStatus.COMPLETED
        )

        if search:
            search_filter = f"%{search.lower()}%"
            ai_assets_query = ai_assets_query.filter(
                (MediaJob.full_payload.ilike(search_filter)) |
                (MediaJob.product_external_id.ilike(search_filter))
            )

        ai_assets_result = await db.execute(ai_assets_query)
        ai_assets = ai_assets_result.scalars().all()

        # Convert AI-generated assets to MediaItem format
        for ai_asset in ai_assets:
            # Extract media items from variants JSON
            if ai_asset.variants:
                for variant in ai_asset.variants:
                    # Skip variants without a valid URL
                    if not variant.get("url"):
                        continue
                    ai_media_item = MediaItem(
                        type=variant.get("type", ai_asset.media_type),
                        id=f"{ai_asset.id}_{variant.get('filename', 'variant')}",
                        src=variant.get("url"),
                        alt=f"AI-generated {ai_asset.media_type}",
                        path="ai_generated",
                        key="ai_generated",
                        data={
                            "product_external_id": str(ai_asset.product_external_id),
                            "media_type": ai_asset.media_type,
                            "provider": ai_asset.provider,
                            "full_payload": ai_asset.full_payload,
                            "variant_info": variant,
                            "source_type": "ai_generated"
                        },
                        source_type="ai_generated"
                    )
                    all_media.append(ai_media_item)

        # Apply pagination
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_media = all_media[start_idx:end_idx]

        return paginated_media

    async def get_total_assets_count(
        self,
        db: AsyncSession,
        user_id: int,
        search: Optional[str] = None
    ) -> int:
        """Get total count of assets for a user."""
        from modules.stores.models import Store
        from modules.products.models import ProductImage
        from modules.media.models import MediaJob
        from sqlalchemy import func

        stores_result = await db.execute(
            select(Store).filter(Store.owner_id == user_id)
        )
        user_stores = stores_result.scalars().all()

        if not user_stores:
            return 0

        store_ids = [store.id for store in user_stores]

        # Count product images
        product_images_query = select(func.count(ProductImage.id)).join(Product).filter(
            Product.store_id.in_(store_ids)
        )

        if search:
            search_filter = f"%{search.lower()}%"
            product_images_query = product_images_query.filter(
                (Product.title.ilike(search_filter)) |
                (Product.description.ilike(search_filter)) |
                (Product.vendor.ilike(search_filter)) |
                (Product.product_type.ilike(search_filter)) |
                (Product.tags.ilike(search_filter))
            )

        product_images_result = await db.execute(product_images_query)
        product_images_count = product_images_result.scalar() or 0

        # Count AI-generated assets from MediaJob
        ai_assets_query = select(func.count(MediaJob.id)).filter(
            MediaJob.user_id == user_id,
            MediaJob.status == MediaJobStatus.COMPLETED
        )

        if search:
            search_filter = f"%{search.lower()}%"
            ai_assets_query = ai_assets_query.filter(
                (MediaJob.full_payload.ilike(search_filter)) |
                (MediaJob.product_external_id.ilike(search_filter))
            )

        ai_assets_result = await db.execute(ai_assets_query)
        ai_assets_count = ai_assets_result.scalar() or 0

        return product_images_count + ai_assets_count

    async def get_or_create_asset(self, db: AsyncSession, asset_data: dict, source_type: str = "product") -> Asset:
        """Get existing asset by src or create new one if it doesn't exist."""
        # Check if asset with this src already exists
        existing_asset = await db.execute(
            select(Asset).filter(Asset.src == asset_data['src'])
        )
        existing_asset = existing_asset.scalar_one_or_none()

        if existing_asset:
            logger.info(f"Asset with src {asset_data['src']} already exists, returning existing")
            return existing_asset

        # Ensure source_type is set in asset_data
        if 'source_type' not in asset_data:
            asset_data['source_type'] = source_type

        # Create new asset
        new_asset = Asset(**asset_data)
        db.add(new_asset)
        logger.info(f"Created new asset with src {asset_data['src']} (source_type: {source_type})")
        return new_asset

    async def reassign_asset_to_product(self, db: AsyncSession, asset_id: str, new_product_id: str, user_id: int) -> bool:
        """Reassign an asset to a different product."""
        try:
            # First, verify the user has access to both the asset and the target product
            from modules.stores.models import Store
            from modules.products.models import Product

            # Get user's stores
            stores_result = await db.execute(
                select(Store).filter(Store.owner_id == user_id)
            )
            user_stores = stores_result.scalars().all()
            store_ids = [store.id for store in user_stores]

            if not store_ids:
                logger.warning(f"User {user_id} has no stores")
                return False

            # Check if the asset exists and belongs to user's stores
            # Handle both integer and string asset IDs
            try:
                # Try to convert to integer first (for database assets)
                numeric_asset_id = int(asset_id)
                asset_result = await db.execute(
                    select(Asset).filter(Asset.id == numeric_asset_id)
                )
                asset = asset_result.scalar_one_or_none()
            except ValueError:
                # If conversion fails, it might be a string ID (like "asset_123_0")
                # For now, we'll log this and return False, but we could extend this
                # to handle string-based asset IDs if needed
                logger.warning(f"Asset ID {asset_id} is not a valid integer")
                return False

            if not asset:
                logger.warning(f"Asset {asset_id} not found")
                return False

            # Verify asset belongs to user's stores by checking the product
            current_product_result = await db.execute(
                select(Product).filter(
                    Product.external_id == str(asset.product_id),
                    Product.store_id.in_(store_ids)
                )
            )
            current_product = current_product_result.scalar_one_or_none()

            if not current_product:
                logger.warning(f"Asset {asset_id} does not belong to user {user_id}'s products")
                return False

            # Check if the target product exists and belongs to user's stores
            target_product_result = await db.execute(
                select(Product).filter(
                    Product.external_id == new_product_id,
                    Product.store_id.in_(store_ids)
                )
            )
            target_product = target_product_result.scalar_one_or_none()

            if not target_product:
                logger.warning(f"Target product {new_product_id} not found or not accessible by user {user_id}")
                return False

            # Update the asset's product_id
            asset.product_id = target_product.id
            asset.product_external_id = new_product_id

            await db.commit()
            logger.info(f"Successfully reassigned asset {asset_id} from product {current_product.external_id} to product {new_product_id}")
            return True

        except Exception as e:
            logger.exception(f"Error reassigning asset {asset_id} to product {new_product_id}: {e}")
            await db.rollback()
            return False


# Create service instance
asset_service = AssetService()