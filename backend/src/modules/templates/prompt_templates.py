"""
Comprehensive prompt templates for media generation.
Contains templates for different media types, styles, and use cases.
"""

from typing import Dict, List, Optional, Any
from enum import Enum


class MediaType(str, Enum):
    """Media types for template categorization."""
    IMAGE = "image"
    VIDEO = "video"
    TEXT = "text"


class StyleCategory(str, Enum):
    """Style categories for templates."""
    PRODUCT_PHOTOGRAPHY = "product_photography"
    LIFESTYLE = "lifestyle"
    MINIMALIST = "minimalist"
    LUXURY = "luxury"
    SOCIAL_MEDIA = "social_media"
    COMMERCIAL = "commercial"
    EDITORIAL = "editorial"
    ARTISTIC = "artistic"


class PromptTemplate:
    """Base class for prompt templates."""
    
    def __init__(
        self,
        template_id: str,
        name: str,
        description: str,
        media_type: MediaType,
        style_category: StyleCategory,
        prompt_template: str,
        negative_prompt: str = "",
        config_overrides: Optional[Dict[str, Any]] = None,
        variables: Optional[List[str]] = None,
        examples: Optional[List[Dict[str, Any]]] = None
    ):
        self.template_id = template_id
        self.name = name
        self.description = description
        self.media_type = media_type
        self.style_category = style_category
        self.prompt_template = prompt_template
        self.negative_prompt = negative_prompt
        self.config_overrides = config_overrides or {}
        self.variables = variables or []
        self.examples = examples or []
    
    def render(self, **kwargs) -> str:
        """Render the template with provided variables."""
        return self.prompt_template.format(**kwargs)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert template to dictionary."""
        return {
            "template_id": self.template_id,
            "name": self.name,
            "description": self.description,
            "media_type": self.media_type.value,
            "style_category": self.style_category.value,
            "prompt_template": self.prompt_template,
            "negative_prompt": self.negative_prompt,
            "config_overrides": self.config_overrides,
            "variables": self.variables,
            "examples": self.examples
        }


# Image Generation Templates
IMAGE_TEMPLATES = [
    PromptTemplate(
        template_id="product_photography_clean",
        name="Clean Product Photography",
        description="Professional product photography with clean white background",
        media_type=MediaType.IMAGE,
        style_category=StyleCategory.PRODUCT_PHOTOGRAPHY,
        prompt_template="Professional product photography of {product_name}, clean white background, studio lighting, high resolution, commercial photography, centered composition, soft shadows, {product_description}",
        negative_prompt="cluttered background, poor lighting, blurry, low quality, amateur, busy background, distracting elements",
        config_overrides={
            "guidance_scale": 8.0,
            "num_inference_steps": 25,
            "style_preset": "product_photography"
        },
        variables=["product_name", "product_description"],
        examples=[
            {
                "product_name": "wireless headphones",
                "product_description": "sleek black design with premium materials",
                "expected_output": "Professional product photography of wireless headphones, clean white background, studio lighting, high resolution, commercial photography, centered composition, soft shadows, sleek black design with premium materials"
            }
        ]
    ),
    
    PromptTemplate(
        template_id="lifestyle_scene",
        name="Lifestyle Scene",
        description="Product integrated into natural lifestyle setting",
        media_type=MediaType.IMAGE,
        style_category=StyleCategory.LIFESTYLE,
        prompt_template="Lifestyle photography featuring {product_name} in {setting}, natural lighting, {mood} atmosphere, real-world context, {target_audience} using the product, authentic moment, {product_description}",
        negative_prompt="studio lighting, artificial, sterile, posed, fake, commercial studio setup",
        config_overrides={
            "guidance_scale": 7.5,
            "num_inference_steps": 20,
            "style_preset": "lifestyle"
        },
        variables=["product_name", "setting", "mood", "target_audience", "product_description"],
        examples=[
            {
                "product_name": "coffee mug",
                "setting": "cozy home office",
                "mood": "warm and inviting",
                "target_audience": "young professional",
                "product_description": "ceramic with minimalist design",
                "expected_output": "Lifestyle photography featuring coffee mug in cozy home office, natural lighting, warm and inviting atmosphere, real-world context, young professional using the product, authentic moment, ceramic with minimalist design"
            }
        ]
    ),
    
    PromptTemplate(
        template_id="luxury_premium",
        name="Luxury Premium",
        description="High-end luxury product presentation",
        media_type=MediaType.IMAGE,
        style_category=StyleCategory.LUXURY,
        prompt_template="Luxury product photography of {product_name}, premium materials, elegant composition, dramatic lighting, sophisticated background, high-end aesthetic, {product_description}, exclusivity, refined details",
        negative_prompt="cheap, low-end, plastic, amateur, poor quality, budget, mass market",
        config_overrides={
            "guidance_scale": 9.0,
            "num_inference_steps": 30,
            "style_preset": "luxury"
        },
        variables=["product_name", "product_description"],
        examples=[
            {
                "product_name": "diamond watch",
                "product_description": "Swiss craftsmanship with gold accents",
                "expected_output": "Luxury product photography of diamond watch, premium materials, elegant composition, dramatic lighting, sophisticated background, high-end aesthetic, Swiss craftsmanship with gold accents, exclusivity, refined details"
            }
        ]
    ),
    
    PromptTemplate(
        template_id="social_media_viral",
        name="Social Media Viral",
        description="Eye-catching social media optimized imagery",
        media_type=MediaType.IMAGE,
        style_category=StyleCategory.SOCIAL_MEDIA,
        prompt_template="Social media photography of {product_name}, vibrant colors, trendy aesthetic, {platform} optimized, engaging composition, {trend_style}, modern lifestyle, {product_description}, shareable content",
        negative_prompt="boring, outdated, corporate, formal, traditional, old-fashioned",
        config_overrides={
            "guidance_scale": 7.0,
            "num_inference_steps": 20,
            "aspect_ratio": "1:1"
        },
        variables=["product_name", "platform", "trend_style", "product_description"],
        examples=[
            {
                "product_name": "smartphone case",
                "platform": "Instagram",
                "trend_style": "aesthetic flat lay",
                "product_description": "colorful protective case with unique pattern",
                "expected_output": "Social media photography of smartphone case, vibrant colors, trendy aesthetic, Instagram optimized, engaging composition, aesthetic flat lay, modern lifestyle, colorful protective case with unique pattern, shareable content"
            }
        ]
    )
]

# Video Generation Templates
VIDEO_TEMPLATES = [
    PromptTemplate(
        template_id="product_showcase_360",
        name="360° Product Showcase",
        description="Rotating product showcase with smooth camera movement",
        media_type=MediaType.VIDEO,
        style_category=StyleCategory.PRODUCT_PHOTOGRAPHY,
        prompt_template="360-degree product showcase of {product_name}, smooth rotating camera movement, clean background, professional lighting, {product_description}, detailed view of all angles",
        negative_prompt="shaky camera, poor lighting, cluttered background, fast movement, blurry",
        config_overrides={
            "duration_seconds": 6,
            "motion_intensity": "medium",
            "camera_movement": "smooth rotation",
            "lighting_style": "studio"
        },
        variables=["product_name", "product_description"],
        examples=[
            {
                "product_name": "luxury perfume bottle",
                "product_description": "elegant glass design with gold accents",
                "expected_output": "360-degree product showcase of luxury perfume bottle, smooth rotating camera movement, clean background, professional lighting, elegant glass design with gold accents, detailed view of all angles"
            }
        ]
    ),
    
    PromptTemplate(
        template_id="lifestyle_video",
        name="Lifestyle Video",
        description="Product in natural lifestyle context with gentle movement",
        media_type=MediaType.VIDEO,
        style_category=StyleCategory.LIFESTYLE,
        prompt_template="Lifestyle video featuring {product_name} in {setting}, natural movement, {mood} atmosphere, real-world usage, {target_audience} interacting with product, {product_description}",
        negative_prompt="artificial, staged, commercial studio, fake interactions, overly promotional",
        config_overrides={
            "duration_seconds": 8,
            "motion_intensity": "low",
            "camera_movement": "handheld natural",
            "lighting_style": "natural"
        },
        variables=["product_name", "setting", "mood", "target_audience", "product_description"],
        examples=[
            {
                "product_name": "yoga mat",
                "setting": "peaceful home studio",
                "mood": "calm and focused",
                "target_audience": "wellness enthusiast",
                "product_description": "eco-friendly with non-slip surface",
                "expected_output": "Lifestyle video featuring yoga mat in peaceful home studio, natural movement, calm and focused atmosphere, real-world usage, wellness enthusiast interacting with product, eco-friendly with non-slip surface"
            }
        ]
    )
]

# Text Generation Templates
TEXT_TEMPLATES = [
    PromptTemplate(
        template_id="product_description_ecommerce",
        name="E-commerce Product Description",
        description="Compelling product description for online stores",
        media_type=MediaType.TEXT,
        style_category=StyleCategory.COMMERCIAL,
        prompt_template="Write a compelling e-commerce product description for {product_name}. Include key features: {key_features}. Target audience: {target_audience}. Tone: {tone}. Include benefits and call-to-action. Product details: {product_description}",
        negative_prompt="",
        config_overrides={
            "temperature": 0.7,
            "max_output_tokens": 500,
            "top_p": 0.9
        },
        variables=["product_name", "key_features", "target_audience", "tone", "product_description"],
        examples=[
            {
                "product_name": "wireless earbuds",
                "key_features": "noise cancellation, 24-hour battery, waterproof",
                "target_audience": "active professionals",
                "tone": "professional yet approachable",
                "product_description": "premium audio device for daily use",
                "expected_output": "Professional product description highlighting noise cancellation, battery life, and waterproof features for active professionals"
            }
        ]
    ),
    
    PromptTemplate(
        template_id="social_media_caption",
        name="Social Media Caption",
        description="Engaging social media captions with hashtags",
        media_type=MediaType.TEXT,
        style_category=StyleCategory.SOCIAL_MEDIA,
        prompt_template="Create an engaging {platform} caption for {product_name}. Style: {style}. Include relevant hashtags. Target audience: {target_audience}. Product context: {product_description}. Call-to-action: {cta}",
        negative_prompt="",
        config_overrides={
            "temperature": 0.8,
            "max_output_tokens": 200,
            "top_p": 0.95
        },
        variables=["platform", "product_name", "style", "target_audience", "product_description", "cta"],
        examples=[
            {
                "platform": "Instagram",
                "product_name": "skincare serum",
                "style": "friendly and informative",
                "target_audience": "skincare enthusiasts",
                "product_description": "vitamin C serum for glowing skin",
                "cta": "shop now",
                "expected_output": "Engaging Instagram caption about vitamin C serum benefits with relevant skincare hashtags and shop now CTA"
            }
        ]
    )
]

# Combined template registry
ALL_TEMPLATES = {
    MediaType.IMAGE: IMAGE_TEMPLATES,
    MediaType.VIDEO: VIDEO_TEMPLATES,
    MediaType.TEXT: TEXT_TEMPLATES
}


def get_templates_by_media_type(media_type: MediaType) -> List[PromptTemplate]:
    """Get all templates for a specific media type."""
    return ALL_TEMPLATES.get(media_type, [])


def get_template_by_id(template_id: str) -> Optional[PromptTemplate]:
    """Get a specific template by ID."""
    for templates in ALL_TEMPLATES.values():
        for template in templates:
            if template.template_id == template_id:
                return template
    return None


def get_templates_by_style(style_category: StyleCategory) -> List[PromptTemplate]:
    """Get all templates for a specific style category."""
    result = []
    for templates in ALL_TEMPLATES.values():
        for template in templates:
            if template.style_category == style_category:
                result.append(template)
    return result


def get_all_templates() -> List[PromptTemplate]:
    """Get all available templates."""
    result = []
    for templates in ALL_TEMPLATES.values():
        result.extend(templates)
    return result
