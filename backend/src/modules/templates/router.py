import logging
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.db.database import get_db
from modules.auth.models import User, Tenant
from modules.auth.router import get_current_user
from .schemas import MediaTemplateResponse, MediaTemplateListResponse
from .template_service import TemplateService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["templates"])

# Initialize template service
template_service = TemplateService()


@router.get("", response_model=MediaTemplateListResponse)
async def list_templates(
    category: Optional[str] = Query(None, description="Filter by category"),
    aspect_ratio: Optional[str] = Query(None, description="Filter by aspect ratio"),
    is_premium: Optional[bool] = Query(None, description="Filter by premium status"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List available media templates.

    Returns templates filtered by user's plan tier and optional criteria.
    """
    try:
        # Get user's tenant to determine plan tier
        result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found. Please complete onboarding."
            )
        
        # Get templates
        templates = await template_service.get_templates(
            db=db,
            user_plan_tier=tenant.plan_tier,
            category=category,
            aspect_ratio=aspect_ratio,
            is_premium=is_premium,
            page=page,
            per_page=per_page
        )
        
        return templates
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list templates: {str(e)}"
        )


@router.get("/{template_id}", response_model=MediaTemplateResponse)
async def get_template(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific media template by ID.

    Returns media template details if user has access based on their plan tier.
    """
    try:
        # Get user's tenant
        result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Get template
        template = await template_service.get_template_by_id(
            db=db,
            template_id=template_id,
            user_plan_tier=tenant.plan_tier
        )
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found or not accessible with your plan"
            )
        
        return template
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template: {str(e)}"
        )


@router.get("/categories/list")
async def list_template_categories(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List all available media template categories with counts.

    Returns categories with media template counts and descriptions.
    """
    try:
        categories = await template_service.get_template_categories(db)
        
        return {
            "categories": categories,
            "total_categories": len(categories)
        }
        
    except Exception as e:
        logger.exception(f"Failed to list template categories: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list template categories: {str(e)}"
        )


@router.get("/recommendations/get")
async def get_recommended_templates(
    product_category: Optional[str] = Query(None, description="Product category for recommendations"),
    target_platform: Optional[str] = Query(None, description="Target platform (instagram, facebook, etc.)"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get recommended media templates based on product category and target platform.

    Returns personalized media template recommendations.
    """
    try:
        # Get user's tenant
        result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Get recommendations
        recommendations = await template_service.get_recommended_templates(
            db=db,
            user_plan_tier=tenant.plan_tier,
            product_category=product_category,
            target_platform=target_platform
        )
        
        return {
            "recommendations": recommendations,
            "total_recommendations": len(recommendations),
            "criteria": {
                "product_category": product_category,
                "target_platform": target_platform,
                "user_plan": tenant.plan_tier
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get template recommendations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendations: {str(e)}"
        )


@router.get("/{template_id}/customization")
async def get_template_customization_options(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get customization options for a specific media template.

    Returns available customization parameters and their default values.
    """
    try:
        # Get user's tenant
        result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Verify user has access to template
        template = await template_service.get_template_by_id(
            db=db,
            template_id=template_id,
            user_plan_tier=tenant.plan_tier
        )
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found or not accessible"
            )
        
        # Get customization options
        options = template_service.get_template_customization_options(template_id)
        
        return {
            "template_id": template_id,
            "template_name": template.name,
            "customization_options": options
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get customization options for {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get customization options: {str(e)}"
        )


@router.post("/initialize-defaults")
async def initialize_default_templates(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Initialize default templates in the database.
    
    This endpoint is typically called during system setup or by admins.
    """
    try:
        # Add admin role check
        from core.config import get_settings
        settings = get_settings()
        admin_emails = getattr(settings, 'ADMIN_EMAILS', ['<EMAIL>', '<EMAIL>'])
        if current_user.email not in admin_emails and 'admin' not in current_user.email.lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )

        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        await template_service.initialize_default_templates(db)
        
        return {
            "success": True,
            "message": "Default templates initialized successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to initialize default templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initialize templates: {str(e)}"
        )


@router.get("/aspect-ratios/list")
async def list_aspect_ratios(
    current_user: User = Depends(get_current_user)
):
    """
    List all available aspect ratios with descriptions and use cases.
    """
    try:
        aspect_ratios = [
            {
                "ratio": "16:9",
                "name": "Landscape",
                "description": "Perfect for YouTube, Facebook, and website videos",
                "width": 1920,
                "height": 1080,
                "use_cases": ["YouTube", "Facebook", "Website", "Presentations"]
            },
            {
                "ratio": "9:16",
                "name": "Portrait",
                "description": "Ideal for Instagram Stories, TikTok, and mobile viewing",
                "width": 1080,
                "height": 1920,
                "use_cases": ["Instagram Stories", "TikTok", "Snapchat", "Mobile"]
            },
            {
                "ratio": "1:1",
                "name": "Square",
                "description": "Great for Instagram posts and social media feeds",
                "width": 1080,
                "height": 1080,
                "use_cases": ["Instagram Posts", "Facebook Posts", "LinkedIn", "Twitter"]
            },
            {
                "ratio": "4:5",
                "name": "Vertical",
                "description": "Optimized for Instagram and Facebook vertical videos",
                "width": 1080,
                "height": 1350,
                "use_cases": ["Instagram Feed", "Facebook Feed", "Pinterest"]
            }
        ]
        
        return {
            "aspect_ratios": aspect_ratios,
            "total_ratios": len(aspect_ratios)
        }

    except Exception as e:
        logger.exception(f"Failed to list aspect ratios: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list aspect ratios: {str(e)}"
        )


# New prompt template endpoints

@router.get("/prompts/list")
async def list_prompt_templates(
    media_type: Optional[str] = Query(None, description="Filter by media type (image, video, text)"),
    style_category: Optional[str] = Query(None, description="Filter by style category"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List available prompt templates for media generation.

    Returns prompt templates filtered by media type and style category.
    """
    try:
        templates = await template_service.get_prompt_templates(
            db=db,
            media_type=media_type,
            style_category=style_category,
            page=page,
            per_page=per_page
        )

        return templates

    except Exception as e:
        logger.exception(f"Failed to list prompt templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list prompt templates: {str(e)}"
        )


@router.get("/prompts/{template_id}")
async def get_prompt_template(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific prompt template by ID.

    Returns prompt template details including variables and examples.
    """
    try:
        template = await template_service.get_prompt_template_by_id(db, template_id)

        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prompt template not found"
            )

        return template

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get prompt template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get prompt template: {str(e)}"
        )


class RenderTemplateRequest(BaseModel):
    """Request model for rendering prompt templates."""
    variables: Dict[str, str]


@router.post("/prompts/{template_id}/render")
async def render_prompt_template(
    template_id: str,
    request: RenderTemplateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Render a prompt template with provided variables.

    Returns the rendered prompt with configuration overrides.
    """
    try:
        result = await template_service.render_prompt_template(
            db=db,
            template_id=template_id,
            variables=request.variables
        )

        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prompt template not found"
            )

        if "error" in result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to render prompt template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to render prompt template: {str(e)}"
        )


@router.get("/prompts/{template_id}/examples")
async def get_template_examples(
    template_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get examples for a specific prompt template.

    Returns example variable sets and expected outputs.
    """
    try:
        examples = await template_service.get_template_examples(db, template_id)

        if not examples:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prompt template not found"
            )

        return examples

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get template examples for {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template examples: {str(e)}"
        )


@router.get("/categories/enhanced")
async def get_enhanced_template_categories(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get enhanced template categories including both video and prompt templates.

    Returns comprehensive categorization of all available templates.
    """
    try:
        categories = await template_service.get_template_categories_enhanced(db)

        return categories

    except Exception as e:
        logger.exception(f"Failed to get enhanced template categories: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get enhanced template categories: {str(e)}"
        )
