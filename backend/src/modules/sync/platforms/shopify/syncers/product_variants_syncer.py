"""
Shopify Variant Syncer - Handles synchronization of Shopify product variants.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text as sql_text

from ....base.syncer import BaseSyncer
from modules.sync.models import SyncCheckpoint

logger = logging.getLogger(__name__)


class ProductVariantsSyncer(BaseSyncer):
    """
    Syncer for Shopify product variants.

    Handles the synchronization of product variant data from Airbyte to the production database.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'product_variants'

    async def sync(self, db: AsyncSession, store_id: int) -> Dict[str, Any]:
        """
        Sync product variants for a specific store.
        """
        # Get sync session for database operations
        

        total_processed = 0
        batch_size = self.get_batch_size()
        last_sync_time = await self.get_last_sync_time(db, store_id)

        store = await self.get_store_info(db, store_id)
        shop_url = self.get_shop_url(store)

        logger.info(f"🚀 Starting variants sync for store {store_id} (shop_url: {shop_url})")

        try:
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Check total count for this store
                test_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM product_variants WHERE shop_url = :shop_url"), {'shop_url': shop_url})
                total_count = test_query.scalar()
                logger.info(f"Debug: Found {total_count} records in Airbyte product_variants table for shop_url: {shop_url}")

                # Process data in batches until no more data
                offset = 0
                iteration = 1

                while True:
                    logger.info(f"Iteration {iteration}: Fetching data from Airbyte product_variants table")

                    select_sql = self.get_select_query(store, last_sync_time, offset, batch_size)
                    airbyte_result = airbyte_conn.execute(sql_text(select_sql), {
                        'last_sync_time': last_sync_time,
                        'batch_size': batch_size,
                        'offset': offset,
                        'shop_url': shop_url
                    })
                    rows = airbyte_result.fetchall()

                    if not rows:
                        logger.info(f"🎉 Iteration {iteration}: No more records to sync!")
                        break

                    logger.info(f"Iteration {iteration}: Fetched {len(rows)} records from Airbyte")

                    # Get product ID mappings from production database
                    product_mappings = {}
                    product_external_ids = [row[2] for row in rows]  # product_external_id is at index 2

                    if product_external_ids:
                        placeholders = ','.join([':param' + str(i) for i in range(len(product_external_ids))])
                        mapping_query = f"""
                        SELECT platform_product_id, id FROM products
                        WHERE platform_product_id IN ({placeholders}) AND store_id = :store_id
                        """
                        params = {f'param{i}': pid for i, pid in enumerate(product_external_ids)}
                        params['store_id'] = store_id
                        mapping_result = await db.execute(sql_text(mapping_query), params)
                        product_mappings = {row[0]: row[1] for row in mapping_result.fetchall()}

                    # Transform and insert data
                    insert_data = []
                    for row in rows:
                        transformed_row = self.transform_row(row, store_id, product_mappings)
                        if transformed_row:
                            insert_data.append(transformed_row)

                    if insert_data:
                        # Use SQLAlchemy async bulk insert instead of raw psycopg2
                        from sqlalchemy.dialects.postgresql import insert
                        from modules.products.models import ProductVariant

                        # Convert insert_data tuples to dictionaries for bulk insert
                        variant_dicts = []
                        for row in insert_data:
                            variant_dicts.append({
                                'external_id': row[0],
                                'product_id': row[1],
                                'platform_variant_id': row[2],
                                'title': row[3],
                                'sku': row[4],
                                'barcode': row[5],
                                'price': row[6],
                                'compare_at_price': row[7],
                                'cost': row[8],
                                'weight': row[9],
                                'weight_unit': row[10],
                                'quantity': row[11],
                                'inventory_policy': row[12],
                                'inventory_item_id': row[13],
                                'option1': row[14],
                                'option2': row[15],
                                'option3': row[16],
                                'taxable': row[17],
                                'requires_shipping': row[18],
                                'fulfillment_service': row[19],
                                'available_for_sale': row[20],
                                'full_json': row[21],
                                'source_updated_at': row[22]
                            })

                        # Perform bulk upsert
                        insert_stmt = insert(ProductVariant).values(variant_dicts)
                        upsert_stmt = insert_stmt.on_conflict_do_update(
                            index_elements=['platform_variant_id'],
                            set_={
                                'title': insert_stmt.excluded.title,
                                'sku': insert_stmt.excluded.sku,
                                'barcode': insert_stmt.excluded.barcode,
                                'price': insert_stmt.excluded.price,
                                'compare_at_price': insert_stmt.excluded.compare_at_price,
                                'cost': insert_stmt.excluded.cost,
                                'weight': insert_stmt.excluded.weight,
                                'weight_unit': insert_stmt.excluded.weight_unit,
                                'quantity': insert_stmt.excluded.quantity,
                                'inventory_policy': insert_stmt.excluded.inventory_policy,
                                'inventory_item_id': insert_stmt.excluded.inventory_item_id,
                                'option1': insert_stmt.excluded.option1,
                                'option2': insert_stmt.excluded.option2,
                                'option3': insert_stmt.excluded.option3,
                                'taxable': insert_stmt.excluded.taxable,
                                'requires_shipping': insert_stmt.excluded.requires_shipping,
                                'fulfillment_service': insert_stmt.excluded.fulfillment_service,
                                'available_for_sale': insert_stmt.excluded.available_for_sale,
                                'full_json': insert_stmt.excluded.full_json,
                                'updated_at': insert_stmt.excluded.updated_at,
                                'source_updated_at': insert_stmt.excluded.source_updated_at
                            },
                            where=(ProductVariant.source_updated_at.is_(None)) | (insert_stmt.excluded.source_updated_at > ProductVariant.source_updated_at)
                        )

                        result = await db.execute(upsert_stmt)
                        batch_processed = len(variant_dicts)  # Approximate count

                        await db.commit()
                        total_processed += batch_processed
                        logger.info(f"Iteration {iteration}: Successfully inserted {batch_processed} product_variants (total: {total_processed})")

                    # Move to next batch
                    offset += batch_size
                    iteration += 1

                logger.info(f"🏁 VARIANTS SYNC COMPLETE!")
                logger.info(f"   Total iterations: {iteration - 1}")
                logger.info(f"   Total records processed: {total_processed}")

                # Update sync checkpoint
                await self.update_sync_checkpoint(db, store_id, total_processed)

                return {
                    'entity_type': 'product_variants',
                    'processed_count': total_processed,
                    'error_count': 0,
                    'status': 'completed',
                    'iterations_completed': iteration - 1
                }

        except Exception as e:
            await db.rollback()
            logger.exception(f"❌ Error in variants sync: {e}", exc_info=True)
            return {
                'entity_type': 'product_variants',
                'processed_count': total_processed,
                'error_count': 1,
                'status': 'failed',
                'error': str(e)
            }

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching variants from Airbyte."""
        return """
        SELECT
            gen_random_uuid() as external_id,
            id::text as platform_variant_id,
            product_id::text as product_external_id,
            title as title,
            sku as sku,
            barcode as barcode,
            price::numeric as price,
            CASE
                WHEN compare_at_price ~ '^[0-9]+(\\.[0-9]+)?$'
                THEN compare_at_price::numeric
                ELSE NULL
            END as compare_at_price,
            NULL as cost,
            weight::numeric as weight,
            COALESCE(weight_unit, 'kg') as weight_unit,
            COALESCE(inventory_quantity, 0) as quantity,
            inventory_policy as inventory_policy,
            inventory_item_id::text as inventory_item_id,
            option1 as option1,
            option2 as option2,
            option3 as option3,
            COALESCE(taxable, true) as taxable,
            COALESCE(requires_shipping, true) as requires_shipping,
            NULL as fulfillment_service,
            COALESCE(available_for_sale, true) as available_for_sale,
            row_to_json(product_variants.*)::text as full_json,
            COALESCE(updated_at, _airbyte_extracted_at) as source_updated_at
        FROM product_variants
        WHERE _airbyte_extracted_at > :last_sync_time
          AND id IS NOT NULL
          AND product_id IS NOT NULL
          AND shop_url = :shop_url
        ORDER BY id::text
        LIMIT :batch_size OFFSET :offset
        """

    def get_insert_query(self) -> str:
        """Generate the INSERT query for bulk inserting variants."""
        return """
        INSERT INTO product_variants (
            external_id, product_id, title, sku, barcode, price, compare_at_price,
            cost, weight, weight_unit, quantity, inventory_policy, inventory_item_id,
            option1, option2, option3, taxable, requires_shipping, fulfillment_service,
            available_for_sale, full_json, source_updated_at
        ) VALUES %s
        ON CONFLICT (external_id) DO UPDATE SET
            title = EXCLUDED.title,
            sku = EXCLUDED.sku,
            barcode = EXCLUDED.barcode,
            price = EXCLUDED.price,
            compare_at_price = EXCLUDED.compare_at_price,
            cost = EXCLUDED.cost,
            weight = EXCLUDED.weight,
            weight_unit = EXCLUDED.weight_unit,
            quantity = EXCLUDED.quantity,
            inventory_policy = EXCLUDED.inventory_policy,
            inventory_item_id = EXCLUDED.inventory_item_id,
            option1 = EXCLUDED.option1,
            option2 = EXCLUDED.option2,
            option3 = EXCLUDED.option3,
            taxable = EXCLUDED.taxable,
            requires_shipping = EXCLUDED.requires_shipping,
            fulfillment_service = EXCLUDED.fulfillment_service,
            available_for_sale = EXCLUDED.available_for_sale,
            full_json = EXCLUDED.full_json,
            updated_at = NOW(),
            source_updated_at = EXCLUDED.source_updated_at
        WHERE product_variants.source_updated_at IS NULL
           OR EXCLUDED.source_updated_at > product_variants.source_updated_at
        """

    def transform_row(self, row: tuple, store_id: int, product_mappings: Dict[str, int]) -> Optional[tuple]:
        """
        Transform a row from Airbyte format to production format.

        Row structure from SELECT:
        0: external_id, 1: platform_variant_id, 2: product_external_id, 3: title, 4: sku, 5: barcode, 6: price,
        7: compare_at_price, 8: cost, 9: weight, 10: weight_unit, 11: quantity,
        12: inventory_policy, 13: inventory_item_id, 14: option1, 15: option2, 16: option3,
        17: taxable, 18: requires_shipping, 19: fulfillment_service, 20: available_for_sale,
        21: full_json, 22: source_updated_at
        """
        external_id, platform_variant_id, product_external_id, *rest = row
        product_id = product_mappings.get(product_external_id)

        if product_id:
            # Insert product_id and platform_variant_id and return the transformed row
            return (external_id, product_id, platform_variant_id) + tuple(rest)
        else:
            # Skip variants without valid product IDs
            logger.warning(f"Skipping variant {external_id} - no valid product_id found for product_external_id {product_external_id}")
            return None
