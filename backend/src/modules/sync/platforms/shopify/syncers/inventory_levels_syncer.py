"""
Shopify Inventory Syncer - Handles synchronization of Shopify inventory levels.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy import text as sql_text
from sqlalchemy.ext.asyncio import AsyncSession

from ....base.syncer import BaseSyncer
from modules.sync.models import SyncCheckpoint

logger = logging.getLogger(__name__)


class InventoryLevelsSyncer(BaseSyncer):
    """
    Syncer for Shopify inventory levels.

    Handles the synchronization of inventory level data from Airbyte to the production database.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'inventory_levels'

    async def sync(self, db: AsyncSession, store_id: int) -> Dict[str, Any]:
        """
        Sync inventory levels for a specific store.
        """

        total_processed = 0
        batch_size = self.get_batch_size()
        last_sync_time = await self.get_last_sync_time(db, store_id)

        store = await self.get_store_info(db, store_id)
        shop_url = self.get_shop_url(store)

        logger.info(f"🚀 Starting inventory sync for store {store_id} (shop_url: {shop_url})")

        try:
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Check total count for this store
                test_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM inventory_levels WHERE shop_url = :shop_url"), {'shop_url': shop_url})
                total_count = test_query.scalar()
                logger.info(f"Debug: Found {total_count} records in Airbyte inventory_levels table for shop_url: {shop_url}")

                # Process data in batches until no more data
                offset = 0
                iteration = 1

                while True:
                    logger.info(f"Iteration {iteration}: Fetching data from Airbyte inventory_levels table")

                    select_sql = self.get_select_query(store, last_sync_time, offset, batch_size)
                    airbyte_result = airbyte_conn.execute(sql_text(select_sql), {
                        'last_sync_time': last_sync_time,
                        'batch_size': batch_size,
                        'offset': offset,
                        'shop_url': shop_url
                    })
                    rows = airbyte_result.fetchall()

                    if not rows:
                        logger.info(f"🎉 Iteration {iteration}: No more records to sync!")
                        break

                    logger.info(f"Iteration {iteration}: Fetched {len(rows)} records from Airbyte")

                    # Transform and insert data
                    insert_data = []
                    for row in rows:
                        transformed_row = self.transform_row(row, store_id)
                        if transformed_row:
                            insert_data.append(transformed_row)

                    if insert_data:
                        # Use SQLAlchemy async bulk insert instead of raw psycopg2
                        from sqlalchemy.dialects.postgresql import insert
                        from modules.products.models import InventoryLevel

                        # Convert insert_data tuples to dictionaries for bulk insert
                        inventory_dicts = []
                        for row in insert_data:
                            inventory_dicts.append({
                                'external_id': row[1],
                                'platform_inventory_id': row[0],
                                'inventory_item_id': row[2],
                                'location_id': row[3],
                                'available': row[4],
                                'full_json': row[5],
                                'created_at': row[6],
                                'updated_at': row[7],
                                'source_updated_at': row[8]
                            })

                        # Perform bulk upsert
                        insert_stmt = insert(InventoryLevel).values(inventory_dicts)
                        upsert_stmt = insert_stmt.on_conflict_do_update(
                            index_elements=['platform_inventory_id'],
                            set_={
                                'available': insert_stmt.excluded.available,
                                'full_json': insert_stmt.excluded.full_json,
                                'updated_at': insert_stmt.excluded.updated_at,
                                'source_updated_at': insert_stmt.excluded.source_updated_at
                            }
                        )

                        result = await db.execute(upsert_stmt)
                        batch_processed = len(inventory_dicts)  # Approximate count

                        await db.commit()
                        total_processed += batch_processed
                        logger.info(f"Iteration {iteration}: Successfully inserted {batch_processed} inventory_levels (total: {total_processed})")

                    # Move to next batch
                    offset += batch_size
                    iteration += 1

                logger.info(f"🏁 INVENTORY SYNC COMPLETE!")
                logger.info(f"   Total iterations: {iteration - 1}")
                logger.info(f"   Total records processed: {total_processed}")

                # Update sync checkpoint
                await self.update_sync_checkpoint(db, store_id, total_processed)

                return {
                    'entity_type': 'inventory_levels',
                    'processed_count': total_processed,
                    'error_count': 0,
                    'status': 'completed',
                    'iterations_completed': iteration - 1
                }

        except Exception as e:
            await db.rollback()
            logger.exception(f"❌ Error in inventory sync: {e}", exc_info=True)
            return {
                'entity_type': 'inventory_levels',
                'processed_count': total_processed,
                'error_count': 1,
                'status': 'failed',
                'error': str(e)
            }

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching inventory levels from Airbyte."""
        return """
        SELECT
            CONCAT(inventory_item_id::text, '_', location_id::text) as platform_inventory_id,
            gen_random_uuid() as external_id,
            inventory_item_id::text as inventory_item_id,
            location_id::text as location_id,
            COALESCE(available, 0) as available,
            row_to_json(inventory_levels.*)::text as full_json,
            NOW() as created_at,
            NOW() as updated_at,
            COALESCE(updated_at, created_at, NOW()) as source_updated_at
        FROM inventory_levels
        WHERE _airbyte_extracted_at > :last_sync_time
          AND shop_url = :shop_url
        ORDER BY inventory_item_id, location_id
        LIMIT :batch_size OFFSET :offset
        """

    def get_insert_query(self) -> str:
        """Generate the INSERT query for bulk inserting inventory levels."""
        return """
        INSERT INTO inventory_levels (
            external_id, inventory_item_id, location_id, available,
            full_json, created_at, updated_at, source_updated_at
        ) VALUES %s
        ON CONFLICT (external_id) DO UPDATE SET
            available = EXCLUDED.available,
            full_json = EXCLUDED.full_json,
            updated_at = EXCLUDED.updated_at,
            source_updated_at = EXCLUDED.source_updated_at
        """

    def transform_row(self, row: tuple, store_id: int) -> Optional[tuple]:
        """
        Transform a row from Airbyte format to production format.

        Row structure from SELECT:
        0: platform_inventory_id, 1: external_id, 2: inventory_item_id, 3: location_id, 4: available,
        5: full_json, 6: created_at, 7: updated_at, 8: source_updated_at
        """
        # All transformations are done in the SQL query, so just return the row
        return row
