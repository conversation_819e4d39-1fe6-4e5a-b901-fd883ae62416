"""
Shopify Product Syncer - Handles synchronization of Shopify products.
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text as sql_text

from ....base.syncer import BaseSyncer
from modules.sync.models import SyncCheckpoint

logger = logging.getLogger(__name__)


class ProductsSyncer(BaseSyncer):
    """
    Syncer for Shopify products.

    Handles the synchronization of product data from Airbyte to the production database.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'products'

    async def sync(self, db: AsyncSession, store_id: int) -> Dict[str, Any]:
        """
        Sync products for a specific store with comprehensive statistics tracking.
        """
        total_processed = 0
        batch_size = self.get_batch_size()
        last_sync_time = await self.get_last_sync_time(db, store_id)

        store = await self.get_store_info(db, store_id)
        shop_url = self.get_shop_url(store)

        # Start comprehensive statistics tracking
        await self.start_sync_tracking(db, store_id)
        self.sync_stats['source_filter_criteria'] = {'shop_url': shop_url}

        logger.info(f"🚀 Starting products sync for store {store_id} (shop_url: {shop_url})")

        try:
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Check total count for this store
                test_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM products WHERE shop_url = :shop_url"), {'shop_url': shop_url})
                total_count = test_query.scalar()
                logger.debug(f"Found {total_count} records in Airbyte products table for shop_url: '{shop_url}'")

                # Also check total count without shop_url filter for comparison
                total_all_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM products"))
                total_all_count = total_all_query.scalar()
                logger.debug(f"Total records in Airbyte products table (all shops): {total_all_count}")

                # Check distinct shop_urls in the table
                shop_urls_query = airbyte_conn.execute(sql_text("SELECT DISTINCT shop_url FROM products WHERE shop_url IS NOT NULL LIMIT 10"))
                distinct_shop_urls = [row[0] for row in shop_urls_query.fetchall()]
                logger.debug(f"Sample distinct shop_urls in products table: {distinct_shop_urls}")

                # Process data in batches until no more data
                offset = 0
                iteration = 1
                errors_in_sync = 0

                while True:
                    batch_start_time = time.time()
                    logger.info(f"Iteration {iteration}: Fetching data from Airbyte products table")

                    try:
                        select_sql = self.get_select_query(store, last_sync_time, offset, batch_size)
                        logger.debug(f"Executing query with parameters:")
                        logger.info(f"  last_sync_time: {last_sync_time}")
                        logger.info(f"  shop_url: {shop_url}")
                        logger.info(f"  batch_size: {batch_size}")
                        logger.info(f"  offset: {offset}")
                        logger.info(f"  store_id: {store_id}")
                        logger.debug(f"Query SQL: {select_sql}")

                        airbyte_result = airbyte_conn.execute(sql_text(select_sql), {
                            'last_sync_time': last_sync_time,
                            'batch_size': batch_size,
                            'offset': offset,
                            'store_id': store_id,
                            'shop_url': shop_url
                        })
                        rows = airbyte_result.fetchall()

                        logger.debug(f"Query returned {len(rows)} rows")

                        if not rows:
                            logger.info(f"🎉 Iteration {iteration}: No more records to sync!")
                            # Additional debug: check if there are any records at all for this shop_url
                            debug_query = airbyte_conn.execute(sql_text("""
                                SELECT COUNT(*) FROM products
                                WHERE shop_url = :shop_url
                            """), {'shop_url': shop_url})
                            total_for_shop = debug_query.scalar()
                            logger.debug(f"Total records for shop_url '{shop_url}': {total_for_shop}")

                            # Check records newer than last_sync_time
                            debug_query2 = airbyte_conn.execute(sql_text("""
                                SELECT COUNT(*) FROM products
                                WHERE shop_url = :shop_url AND _airbyte_extracted_at > :last_sync_time
                            """), {'shop_url': shop_url, 'last_sync_time': last_sync_time})
                            newer_records = debug_query2.scalar()
                            logger.debug(f"Records newer than {last_sync_time} for shop_url '{shop_url}': {newer_records}")
                            break

                        logger.info(f"Iteration {iteration}: Fetched {len(rows)} records from Airbyte")

                        # Transform and insert data
                        insert_data = []
                        batch_errors = 0

                        for row in rows:
                            try:
                                transformed_row = self.transform_row(row, store_id)
                                if transformed_row:
                                    insert_data.append(transformed_row)
                                else:
                                    self.track_data_quality_issue('invalid')
                                    batch_errors += 1
                            except Exception as e:
                                self.track_error('transformation_error', str(e), str(row[0]) if row else None)
                                self.track_data_quality_issue('transformation_error')
                                batch_errors += 1
                                continue

                        batch_processed = 0
                        if insert_data:
                            try:
                                # Use SQLAlchemy async bulk insert instead of raw psycopg2
                                from sqlalchemy.dialects.postgresql import insert
                                from modules.products.models import Product

                                # Convert insert_data tuples to dictionaries for bulk insert
                                product_dicts = []
                                for row in insert_data:
                                    product_dicts.append({
                                        'external_id': row[0],
                                        'platform_product_id': row[1],
                                        'title': row[2],
                                        'description': row[3],
                                        'vendor': row[4],
                                        'product_type': row[5],
                                        'tags': row[6],
                                        'status': row[7],
                                        'published': row[8],
                                        'store_id': row[9],
                                        'created_at': row[10],
                                        'updated_at': row[11],
                                        'handle': row[12],
                                        'full_json': row[13],
                                        'published_at': row[14],
                                        'options': row[15],
                                        'seo': row[16],
                                        'metafields': row[17],
                                        'collections': row[18],
                                        'featured_media': row[19],
                                        'source_updated_at': row[20]
                                    })

                                # Perform bulk upsert
                                insert_stmt = insert(Product).values(product_dicts)
                                upsert_stmt = insert_stmt.on_conflict_do_update(
                                    index_elements=['platform_product_id'],
                                    set_={
                                        'title': insert_stmt.excluded.title,
                                        'description': insert_stmt.excluded.description,
                                        'vendor': insert_stmt.excluded.vendor,
                                        'product_type': insert_stmt.excluded.product_type,
                                        'tags': insert_stmt.excluded.tags,
                                        'status': insert_stmt.excluded.status,
                                        'published': insert_stmt.excluded.published,
                                        'handle': insert_stmt.excluded.handle,
                                        'full_json': insert_stmt.excluded.full_json,
                                        'published_at': insert_stmt.excluded.published_at,
                                        'options': insert_stmt.excluded.options,
                                        'seo': insert_stmt.excluded.seo,
                                        'metafields': insert_stmt.excluded.metafields,
                                        'collections': insert_stmt.excluded.collections,
                                        'featured_media': insert_stmt.excluded.featured_media,
                                        'updated_at': insert_stmt.excluded.updated_at,
                                        'source_updated_at': insert_stmt.excluded.source_updated_at
                                    },
                                    where=(Product.source_updated_at.is_(None)) | (insert_stmt.excluded.source_updated_at > Product.source_updated_at)
                                )

                                result = await db.execute(upsert_stmt)
                                batch_processed = len(product_dicts)  # Approximate count

                                await db.commit()
                                total_processed += batch_processed
                                logger.info(f"Iteration {iteration}: Successfully inserted {batch_processed} products (total: {total_processed})")

                                # Track successful batch
                                batch_time = time.time() - batch_start_time
                                self.track_batch_processing(iteration, len(insert_data), batch_time, batch_processed, batch_errors)

                            except Exception as e:
                                await db.rollback()
                                self.track_error('database_insert_error', str(e))
                                batch_errors += len(insert_data)
                                errors_in_sync += batch_errors
                                logger.exception(f"Error inserting batch {iteration}: {e}")

                        # Move to next batch
                        offset += batch_size
                        iteration += 1

                    except Exception as e:
                        self.track_error('batch_processing_error', str(e))
                        errors_in_sync += 1
                        logger.exception(f"Error processing batch {iteration}: {e}")
                        break

                logger.info(f"🏁 PRODUCTS SYNC COMPLETE!")
                logger.info(f"   Total iterations: {iteration - 1}")
                logger.info(f"   Total records processed: {total_processed}")
                logger.info(f"   Total errors: {errors_in_sync}")

                # Finalize comprehensive statistics
                await self.finalize_sync_statistics(db, store_id)

                # Determine if sync was fully successful (no errors)
                all_successful = errors_in_sync == 0

                # Update sync checkpoint only on full success
                await self.update_sync_checkpoint(db, store_id, total_processed, all_successful)

                return {
                    'entity_type': 'products',
                    'processed_count': total_processed,
                    'error_count': errors_in_sync,
                    'status': 'completed' if all_successful else 'partial_failure',
                    'iterations_completed': iteration - 1,
                    'statistics': self.sync_stats.copy()
                }

        except Exception as e:
            await db.rollback()
            self.track_error('sync_failed', str(e))
            await self.finalize_sync_statistics(db, store_id)
            logger.exception(f"❌ Error in products sync: {e}", exc_info=True)
            return {
                'entity_type': 'products',
                'processed_count': total_processed,
                'error_count': errors_in_sync + 1,
                'status': 'failed',
                'error': str(e),
                'statistics': self.sync_stats.copy()
            }

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching products from Airbyte."""
        return """
        SELECT
            gen_random_uuid() as external_id,
            id::text as platform_product_id,
            title as title,
            body_html as description,
            vendor as vendor,
            product_type as product_type,
            tags as tags,
            status as status,
            (published_at IS NOT NULL)::boolean as published,
            :store_id as store_id,
            COALESCE(created_at, _airbyte_extracted_at) as created_at,
            COALESCE(updated_at, _airbyte_extracted_at) as updated_at,
            handle as handle,
            row_to_json(products.*)::text as full_json,
            published_at as published_at,
            options::text as options,
            seo::text as seo,
            NULL as metafields,
            NULL as collections,
            featured_media::text as featured_media,
            COALESCE(updated_at, _airbyte_extracted_at) as source_updated_at
        FROM products
        WHERE _airbyte_extracted_at > :last_sync_time
          AND id IS NOT NULL
          AND title IS NOT NULL
          AND shop_url = :shop_url
        ORDER BY id::text
        LIMIT :batch_size OFFSET :offset
        """

    def get_insert_query(self) -> str:
        """Generate the INSERT query for bulk inserting products."""
        return """
        INSERT INTO products (
            external_id, title, description, vendor, product_type, tags,
            status, published, store_id, created_at, updated_at, handle,
            full_json, published_at, options, seo, metafields, collections,
            featured_media, source_updated_at
        ) VALUES %s
        ON CONFLICT (external_id) DO UPDATE SET
            title = EXCLUDED.title,
            description = EXCLUDED.description,
            vendor = EXCLUDED.vendor,
            product_type = EXCLUDED.product_type,
            tags = EXCLUDED.tags,
            status = EXCLUDED.status,
            published = EXCLUDED.published,
            handle = EXCLUDED.handle,
            full_json = EXCLUDED.full_json,
            published_at = EXCLUDED.published_at,
            options = EXCLUDED.options,
            seo = EXCLUDED.seo,
            metafields = EXCLUDED.metafields,
            collections = EXCLUDED.collections,
            featured_media = EXCLUDED.featured_media,
            updated_at = NOW(),
            source_updated_at = EXCLUDED.source_updated_at
        WHERE products.source_updated_at IS NULL
           OR EXCLUDED.source_updated_at > products.source_updated_at
        """

    def transform_row(self, row: tuple, store_id: int) -> Optional[tuple]:
        """
        Transform a row from Airbyte format to production format.

        Row structure from SELECT:
        0: external_id, 1: platform_product_id, 2: title, 3: description, 4: vendor, 5: product_type, 6: tags,
        7: status, 8: published, 9: store_id, 10: created_at, 11: updated_at, 12: handle,
        13: full_json, 14: published_at, 15: options, 16: seo, 17: metafields, 18: collections,
        19: featured_media, 20: source_updated_at
        """
        # All transformations are done in the SQL query, so just return the row
        return row
