"""
Shopify Metafield Image Syncer - Handles synchronization of Shopify product image metafields.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text as sql_text
import json

from ....base.syncer import BaseSyncer
from modules.sync.models import SyncCheckpoint

logger = logging.getLogger(__name__)


class MetafieldProductImagesSyncer(BaseSyncer):
    """
    Syncer for Shopify product image metafields.

    Aggregates metafields from metafield_product_images table and updates product_images.metafields column.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'metafield_product_images'

    def get_destination_table(self) -> str:
        """Override to specify the correct destination table for statistics tracking.

        Metafield syncers write to product_images table, not metafield_product_images.
        """
        return 'product_images'

    async def sync(self, db: AsyncSession, store_id: int) -> Dict[str, Any]:
        """
        Sync product image metafields for a specific store.
        """
        total_processed = 0
        batch_size = self.get_batch_size()
        last_sync_time = await self.get_last_sync_time(db, store_id)

        store = await self.get_store_info(db, store_id)
        shop_url = self.get_shop_url(store)

        logger.info(f"🚀 Starting image metafields sync for store {store_id} (shop_url: {shop_url})")

        try:
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Check total count
                test_query = airbyte_conn.execute(sql_text("SELECT COUNT(*) FROM metafield_product_images"))
                total_count = test_query.scalar()
                logger.info(f"Debug: Found {total_count} metafield records in Airbyte metafield_product_images table")

                # Process data in batches until no more data
                offset = 0
                iteration = 1

                while True:
                    logger.info(f"Iteration {iteration}: Fetching image metafield data from Airbyte")

                    select_sql = self.get_select_query(store, last_sync_time, offset, batch_size)
                    airbyte_result = airbyte_conn.execute(sql_text(select_sql), {
                        'last_sync_time': last_sync_time,
                        'batch_size': batch_size,
                        'offset': offset,
                        'store_id': store_id
                    })
                    rows = airbyte_result.fetchall()

                    if not rows:
                        logger.info(f"🎉 Iteration {iteration}: No more image metafield records to sync!")
                        break

                    logger.info(f"Iteration {iteration}: Fetched {len(rows)} image metafield records from Airbyte")

                    # Aggregate metafields by owner_id and update images
                    metafields_by_owner = self.aggregate_metafields(rows)

                    if metafields_by_owner:
                        update_count = await self.update_images_metafields(db, metafields_by_owner, store_id)
                        total_processed += update_count
                        logger.info(f"Iteration {iteration}: Successfully updated {update_count} images with metafields (total: {total_processed})")

                    # Move to next batch
                    offset += batch_size
                    iteration += 1

                logger.info(f"🏁 IMAGE METAFIELDS SYNC COMPLETE!")
                logger.info(f"   Total iterations: {iteration - 1}")
                logger.info(f"   Total images updated: {total_processed}")

                # Update sync checkpoint
                await self.update_sync_checkpoint(db, store_id, total_processed)

                return {
                    'entity_type': 'metafield_product_images',
                    'processed_count': total_processed,
                    'error_count': 0,
                    'status': 'completed',
                    'iterations_completed': iteration - 1
                }

        except Exception as e:
            await db.rollback()
            logger.exception(f"❌ Error in image metafields sync: {e}", exc_info=True)
            return {
                'entity_type': 'metafield_product_images',
                'processed_count': total_processed,
                'error_count': 1,
                'status': 'failed',
                'error': str(e)
            }

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching image metafields from Airbyte."""
        return """
        SELECT
            owner_id,
            key,
            value,
            namespace,
            type,
            COALESCE(updated_at, _airbyte_extracted_at) as updated_at
        FROM metafield_product_images
        WHERE _airbyte_extracted_at > :last_sync_time
          AND owner_id IS NOT NULL
          AND key IS NOT NULL
        ORDER BY owner_id, namespace, key
        LIMIT :batch_size OFFSET :offset
        """

    def aggregate_metafields(self, rows: List[Tuple]) -> Dict[str, Dict[str, Any]]:
        """
        Aggregate metafields by owner_id.

        Returns:
            Dict[owner_id, Dict[namespace, Dict[key, value]]]
        """
        metafields_by_owner = {}

        for row in rows:
            owner_id, key, value, namespace, field_type, updated_at = row

            if owner_id not in metafields_by_owner:
                metafields_by_owner[owner_id] = {}

            if namespace not in metafields_by_owner[owner_id]:
                metafields_by_owner[owner_id][namespace] = {}

            # Store the metafield with its metadata
            metafields_by_owner[owner_id][namespace][key] = {
                'value': value,
                'type': field_type,
                'updated_at': updated_at.isoformat() if updated_at else None
            }

        return metafields_by_owner

    async def update_images_metafields(self, db: AsyncSession, metafields_by_owner: Dict[str, Dict[str, Any]], store_id: int) -> int:
        """
        Update product_images table with aggregated metafields.
        """
        update_count = 0

        for owner_id, metafields in metafields_by_owner.items():
            try:
                # Convert to JSON for storage
                metafields_json = json.dumps(metafields)

                # Update the image record
                update_sql = """
                UPDATE product_images
                SET metafields = :metafields,
                    updated_at = NOW()
                WHERE platform_image_id = :platform_image_id
                  AND product_id IN (SELECT id FROM products WHERE store_id = :store_id)
                """

                result = await db.execute(sql_text(update_sql), {
                    'metafields': metafields_json,
                    'platform_image_id': str(owner_id),
                    'store_id': store_id
                })

                if result.rowcount > 0:
                    update_count += 1

            except Exception as e:
                logger.exception(f"Error updating metafields for image {owner_id}: {e}")
                # Rollback immediately to clear the aborted transaction state
                await db.rollback()
                continue

        # Commit all changes at once
        try:
            await db.commit()
        except Exception as e:
            logger.exception(f"Error committing image metafield updates: {e}")
            await db.rollback()
            return 0

        return update_count

    async def update_sync_checkpoint(self, db: AsyncSession, store_id: int, processed_count: int):
        """Update sync checkpoint after successful sync, using airbyte connection for counting."""
        from sqlalchemy import text as sql_text, select
        from datetime import datetime, timezone

        checkpoint_result = await db.execute(
            select(SyncCheckpoint).filter(
                SyncCheckpoint.store_id == store_id,
                SyncCheckpoint.entity_type == self.entity_type
            )
        )
        checkpoint = checkpoint_result.scalar_one_or_none()

        now = datetime.now(timezone.utc)

        # Use airbyte engine to get the actual count from the source table
        airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
        with airbyte_engine.connect() as airbyte_conn:
            total_count_query = sql_text(f"SELECT COUNT(*) FROM {self.entity_type}")
            total_count_result = airbyte_conn.execute(total_count_query)
            actual_total_count = total_count_result.scalar()

        if checkpoint:
            checkpoint.last_successful_sync_at = now
            checkpoint.last_sync_status = 'completed'
            checkpoint.updated_at = now
            checkpoint.total_records = actual_total_count
        else:
            checkpoint = SyncCheckpoint(
                store_id=store_id,
                entity_type=self.entity_type,
                last_successful_sync_at=now,
                total_records=actual_total_count,
                last_sync_status='completed',
                created_at=now,
                updated_at=now
            )
            db.add(checkpoint)

        # Record sync checkpoint update metric
        from core.metrics import sync_checkpoint_updates
        sync_checkpoint_updates.labels(
            store_domain="",  # We don't have store_domain here, could be enhanced
            entity_type=self.entity_type,
            stage="completed"
        ).inc()

        await db.commit()
        logger.debug(f"Updated checkpoint for {self.entity_type}: {processed_count} processed, {checkpoint.total_records} total in airbyte table")