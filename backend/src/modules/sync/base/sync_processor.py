"""
Base sync processor class providing common functionality for platform-specific sync processors.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text as sql_text, select

from modules.stores.models import Store

logger = logging.getLogger(__name__)


class BaseSyncProcessor:
    """
    Base class for platform-specific sync processors.

    Provides common functionality like:
    - Store discovery
    - Entity type management
    - Result aggregation
    - Error handling
    """

    def __init__(self, platform_name: str, platform_config: Optional[Dict[str, Any]] = None):
        self.platform_name = platform_name
        self.config = platform_config or {}
        self.supported_entity_types = self._get_supported_entity_types()

    def _get_supported_entity_types(self) -> List[str]:
        """Get list of supported entity types for this platform."""
        entity_types = self.config.get('entity_types')
        if not entity_types:
            raise ValueError(f"No entity_types configured for platform {self.platform_name}")
        return entity_types

    async def sync_all_entities(
        self,
        db: AsyncSession,
        store_id: Optional[int] = None,
        entity_types: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Sync all specified entity types for a store.

        Args:
            db: Database session
            store_id: Store ID to sync (optional - if None, syncs all stores found in data)
            entity_types: List of entity types to sync (optional)

        Returns:
            Sync results summary
        """
        if entity_types is None:
            entity_types = self.supported_entity_types

        # If no store_id specified, find all stores with data in Airbyte
        if store_id is None:
            stores_to_sync = await self._find_stores_with_data(db)
        else:
            stores_to_sync = [store_id]

        all_results = {
            'platform': self.platform_name,
            'stores_synced': [],
            'total_processed': 0,
            'total_errors': 0,
            'start_time': datetime.now(timezone.utc),
            'end_time': None,
            'status': 'running'
        }

        try:
            for current_store_id in stores_to_sync:
                logger.info(f"Starting {self.platform_name} sync for store {current_store_id}, entities: {entity_types}")

                store_results = {
                    'store_id': current_store_id,
                    'entity_results': {},
                    'total_processed': 0,
                    'total_errors': 0,
                    'status': 'running'
                }

                try:
                    for entity_type in entity_types:
                        logger.info(f"Syncing {entity_type} for store {current_store_id}")

                        entity_result = await self._sync_entity_type(db, current_store_id, entity_type)
                        store_results['entity_results'][entity_type] = entity_result
                        store_results['total_processed'] += entity_result.get('processed_count', 0)
                        store_results['total_errors'] += entity_result.get('error_count', 0)

                        logger.info(f"Completed {entity_type}: {entity_result}")

                    store_results['status'] = 'completed'

                except Exception as e:
                    store_results['status'] = 'failed'
                    store_results['error'] = str(e)
                    logger.exception(f"{self.platform_name} sync failed for store {current_store_id}: {e}")

                all_results['stores_synced'].append(store_results)
                all_results['total_processed'] += store_results['total_processed']
                all_results['total_errors'] += store_results['total_errors']

            all_results['status'] = 'completed'
            all_results['end_time'] = datetime.now(timezone.utc)

            logger.info(f"{self.platform_name} sync completed for {len(stores_to_sync)} stores: {all_results['total_processed']} records processed")

        except Exception as e:
            all_results['status'] = 'failed'
            all_results['error'] = str(e)
            all_results['end_time'] = datetime.now(timezone.utc)
            logger.exception(f"{self.platform_name} sync failed: {e}")
            raise

        return all_results

    async def _find_stores_with_data(self, db: AsyncSession) -> List[int]:
        """Find all stores that have data in Airbyte tables for this platform."""
        try:
            airbyte_engine = self._create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                # Get unique shop_urls from products table
                shop_urls_query = sql_text("SELECT DISTINCT shop_url FROM products WHERE shop_url IS NOT NULL")
                shop_urls_result = airbyte_conn.execute(shop_urls_query)
                shop_urls = [row[0] for row in shop_urls_result.fetchall()]

                store_ids = []
                for shop_url in shop_urls:
                    # Find matching store by shop_name or shop_domain
                    result = await db.execute(
                        select(Store).filter(
                            (Store.shop_name == shop_url) |
                            (Store.shop_domain.like(f"{shop_url}.myshopify.com"))
                        )
                    )
                    store = result.scalar_one_or_none()

                    if store:
                        store_ids.append(store.id)
                        logger.info(f"Found store {store.id} ({store.shop_name or 'Unnamed'}) for shop_url: {shop_url}")
                    else:
                        logger.warning(f"No store found for shop_url: {shop_url}")

                return store_ids

        except Exception as e:
            logger.exception(f"Error finding stores with data: {e}")
            return []

    async def _sync_entity_type(self, db: AsyncSession, store_id: int, entity_type: str) -> Dict[str, Any]:
        """Sync a specific entity type using the appropriate syncer."""
        syncer_class = self._get_syncer_class(entity_type)
        if not syncer_class:
            raise ValueError(f"Unsupported entity type: {entity_type}")

        syncer = syncer_class(platform_config=self.config)
        return await syncer.sync(db, store_id)

    def _get_syncer_class(self, entity_type: str):
        """Get the syncer class for a specific entity type."""
        # This will be implemented by platform-specific processors
        raise NotImplementedError("Platform processors must implement _get_syncer_class")

    def _create_airbyte_engine(self):
        """Create Airbyte database engine."""
        from core.config import get_settings
        settings = get_settings()

        airbyte_url = (
            f"postgresql://{settings.AIRBYTE_DATABASE_USER}:"
            f"{settings.AIRBYTE_DATABASE_PASSWORD}@"
            f"{settings.AIRBYTE_DATABASE_HOST}:"
            f"{settings.AIRBYTE_DATABASE_PORT}/"
            f"{settings.AIRBYTE_DATABASE_NAME}"
        )
        from sqlalchemy import create_engine
        return create_engine(
            airbyte_url,
            pool_size=5,
            max_overflow=10,
            pool_recycle=3600
        )