"""
Base syncer class providing common functionality for all entity syncers.
"""

import logging
import time
import psutil
import platform
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text as sql_text

from core.config import get_settings
from modules.sync.models import SyncCheckpoint
from modules.stores.models import Store
from core.metrics import sync_checkpoint_updates

logger = logging.getLogger(__name__)




class BaseSyncer:
    """
    Base class for entity-specific syncers.

    Provides common functionality like:
    - Database connections
    - Checkpoint management
    - Store filtering
    - Batch processing
    - Error handling
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        self.settings = get_settings()
        self.airbyte_engine = airbyte_engine
        self.platform_config = platform_config or {}
        self.entity_type = self.__class__.__name__.replace('Syncer', '').lower()

        # Initialize comprehensive statistics tracking
        self.sync_stats = self._initialize_sync_statistics()
        self.batch_stats = []
        self.error_stats = {}
        self.performance_stats = self._initialize_performance_stats()

    async def get_store_info(self, db: AsyncSession, store_id: int) -> Optional[Store]:
        """Get store information and extract shop_url."""
        from sqlalchemy import select

        result = await db.execute(select(Store).filter(Store.id == store_id))
        store = result.scalar_one_or_none()
        if not store:
            raise ValueError(f"Store {store_id} not found")
        return store

    def get_shop_url(self, store: Store) -> str:
        """Extract shop_url from store's shop_domain."""
        return store.shop_domain.replace('.myshopify.com', '') if store.shop_domain else store.shop_name or f"store-{store.id}"

    async def get_last_sync_time(self, db: AsyncSession, store_id: int) -> datetime:
        """Get the last successful sync time for this entity type."""
        from sqlalchemy import select

        result = await db.execute(
            select(SyncCheckpoint).filter(
                SyncCheckpoint.store_id == store_id,
                SyncCheckpoint.entity_type == self.entity_type
            )
        )
        checkpoint = result.scalar_one_or_none()

        if checkpoint and checkpoint.last_successful_sync_at:
            logger.debug(f"Found checkpoint for {self.entity_type}: {checkpoint.last_successful_sync_at}")
            logger.debug(f"Checkpoint details - ID: {checkpoint.id}, Store: {checkpoint.store_id}, Status: {checkpoint.last_sync_status}")
            return checkpoint.last_successful_sync_at

        # Default to a very old date to sync all records
        default_time = datetime(2020, 1, 1, tzinfo=timezone.utc)
        logger.debug(f"No checkpoint found for {self.entity_type} (store {store_id}), using default: {default_time}")
        if checkpoint:
            logger.debug(f"Checkpoint exists but no last_successful_sync_at - Status: {checkpoint.last_sync_status}")
        return default_time

    def get_count_query(self, destination_table: str, store_id: int) -> sql_text:
        """Get the appropriate count query for the destination table."""
        if destination_table == 'products':
            # Products table has direct store_id
            return sql_text(f"SELECT COUNT(*) FROM {destination_table} WHERE store_id = :store_id")
        elif destination_table == 'product_images':
            # Product images don't have store_id, join through products
            return sql_text(f"""
                SELECT COUNT(*)
                FROM {destination_table} pi
                JOIN products p ON pi.product_id = p.id
                WHERE p.store_id = :store_id
            """)
        elif destination_table == 'product_variants':
            # Product variants don't have store_id, join through products
            return sql_text(f"""
                SELECT COUNT(*)
                FROM {destination_table} pv
                JOIN products p ON pv.product_id = p.id
                WHERE p.store_id = :store_id
            """)
        elif destination_table == 'inventory_levels':
            # Inventory levels don't have store_id, join through variants and products
            return sql_text(f"""
                SELECT COUNT(*)
                FROM {destination_table} il
                JOIN product_variants pv ON il.inventory_item_id = pv.inventory_item_id
                JOIN products p ON pv.product_id = p.id
                WHERE p.store_id = :store_id
            """)
        else:
            # Default: assume table has store_id column
            return sql_text(f"SELECT COUNT(*) FROM {destination_table} WHERE store_id = :store_id")

    async def update_sync_checkpoint(self, db: AsyncSession, store_id: int, processed_count: int, all_successful: bool = True):
        """Update sync checkpoint only on full success."""
        from sqlalchemy import select

        checkpoint_result = await db.execute(
            select(SyncCheckpoint).filter(
                SyncCheckpoint.store_id == store_id,
                SyncCheckpoint.entity_type == self.entity_type
            )
        )
        checkpoint = checkpoint_result.scalar_one_or_none()

        now = datetime.now(timezone.utc)

        if checkpoint:
            # Always update total_records to reflect current table state
            destination_table = self.get_destination_table()
            total_count_query = self.get_count_query(destination_table, store_id)
            total_count_result = await db.execute(total_count_query, {'store_id': store_id})
            checkpoint.total_records = total_count_result.scalar() or 0

            # Update sync timing fields
            checkpoint.last_updated_at = now
            checkpoint.sync_finished_at = now
            checkpoint.records_processed_in_sync = processed_count

            # Only update success markers if everything succeeded
            if all_successful:
                checkpoint.last_successful_sync_at = now
                checkpoint.last_sync_status = 'completed'
                checkpoint.current_sync_stage = 'completed'
            else:
                checkpoint.last_sync_status = 'partial_failure'
                checkpoint.current_sync_stage = 'failed'

            checkpoint.updated_at = now
        else:
            # Create checkpoint only on success
            if all_successful:
                destination_table = self.get_destination_table()
                total_count_query = self.get_count_query(destination_table, store_id)
                total_count_result = await db.execute(total_count_query, {'store_id': store_id})
                actual_total_count = total_count_result.scalar() or 0

                checkpoint = SyncCheckpoint(
                    store_id=store_id,
                    entity_type=self.entity_type,
                    last_successful_sync_at=now,
                    total_records=actual_total_count,
                    last_sync_status='completed',
                    current_sync_stage='completed',
                    sync_finished_at=now,
                    records_processed_in_sync=processed_count,
                    created_at=now,
                    updated_at=now
                )
                db.add(checkpoint)

        # Record sync checkpoint update metric
        sync_checkpoint_updates.labels(
            store_domain="",  # We don't have store_domain here, could be enhanced
            entity_type=self.entity_type,
            stage="completed" if all_successful else "failed"
        ).inc()

        await db.commit()
        logger.debug(f"Updated checkpoint for {self.entity_type}: {processed_count} processed, {checkpoint.total_records if checkpoint else 0} total in {self.get_destination_table()} table, success: {all_successful}")

    def _initialize_sync_statistics(self) -> Dict[str, Any]:
        """Initialize comprehensive sync statistics tracking."""
        return {
            'sync_started_at': None,
            'sync_finished_at': None,
            'source_record_count_before': 0,
            'source_record_count_after': 0,
            'destination_record_count_before': 0,
            'destination_record_count_after': 0,
            'source_new_records_count': 0,
            'source_updated_records_count': 0,
            'source_deleted_records_count': 0,
            'destination_inserted_count': 0,
            'destination_updated_count': 0,
            'destination_deleted_count': 0,
            'destination_failed_count': 0,
            'batch_count': 0,
            'total_batches_processed': 0,
            'batches_with_errors': 0,
            'error_count_total': 0,
            'duplicate_records_found': 0,
            'invalid_records_skipped': 0,
            'null_values_count': 0,
            'data_transformation_errors': 0,
            'retry_count': 0,
            'source_filter_criteria': {},
            'batch_sizes': [],
            'batch_processing_times': []
        }

    def _initialize_performance_stats(self) -> Dict[str, Any]:
        """Initialize performance statistics tracking."""
        return {
            'memory_usage_start': psutil.Process().memory_info().rss / 1024 / 1024,  # MB
            'memory_usage_peak': 0,
            'start_time': time.time(),
            'database_connections_used': 0
        }

    async def start_sync_tracking(self, db: AsyncSession, store_id: int):
        """Start comprehensive sync statistics tracking."""
        from sqlalchemy import select

        now = datetime.now(timezone.utc)
        self.sync_stats['sync_started_at'] = now
        self.performance_stats['start_time'] = time.time()

        # Update checkpoint with Airbyte sync start time
        checkpoint_result = await db.execute(
            select(SyncCheckpoint).filter(
                SyncCheckpoint.store_id == store_id,
                SyncCheckpoint.entity_type == self.entity_type
            )
        )
        checkpoint = checkpoint_result.scalar_one_or_none()

        if checkpoint:
            checkpoint.airbyte_sync_started_at = now
            checkpoint.current_sync_stage = 'airbyte_sync'
            await db.commit()
            logger.info(f"📊 Set Airbyte sync start time for {self.entity_type}: {now}")

        # Capture initial counts
        await self._capture_initial_counts(db, store_id)

        logger.info(f"📊 Started comprehensive tracking for {self.entity_type} sync (store {store_id})")

    async def _capture_initial_counts(self, db: AsyncSession, store_id: int):
        """Capture initial record counts from source and destination."""
        try:
            # Get the correct destination table name
            destination_table = self.get_destination_table()

            logger.debug(f"Attempting to query destination table '{destination_table}' for store_id {store_id}")

            # Get destination count
            dest_count_query = self.get_count_query(destination_table, store_id)
            dest_result = await db.execute(dest_count_query, {'store_id': store_id})
            self.sync_stats['destination_record_count_before'] = dest_result.scalar() or 0
            logger.debug(f"Destination count for {destination_table}: {self.sync_stats['destination_record_count_before']}")

            # Get source count (will be overridden by subclasses if needed)
            airbyte_engine = self.airbyte_engine or self.create_airbyte_engine()
            with airbyte_engine.connect() as airbyte_conn:
                source_count_query = sql_text(f"SELECT COUNT(*) FROM {self.entity_type}")
                source_result = airbyte_conn.execute(source_count_query)
                self.sync_stats['source_record_count_before'] = source_result.scalar() or 0
                logger.debug(f"Total source count for {self.entity_type}: {self.sync_stats['source_record_count_before']}")

                # Get source last updated timestamp
                last_updated_query = sql_text(f"SELECT MAX(_airbyte_extracted_at) FROM {self.entity_type}")
                last_updated_result = airbyte_conn.execute(last_updated_query)
                self.sync_stats['source_last_updated_at'] = last_updated_result.scalar()
                logger.debug(f"Latest _airbyte_extracted_at for {self.entity_type}: {self.sync_stats['source_last_updated_at']}")

        except Exception as e:
            logger.warning(f"Could not capture initial counts: {e}")
            logger.warning(f"This error likely indicates that table '{self.get_destination_table()}' does not exist in the local database")
            logger.warning(f"For metafield syncers, the destination is typically a different table (e.g., product_variants.metafields)")

    def track_batch_processing(self, batch_number: int, batch_size: int, processing_time: float,
                             records_processed: int, errors_in_batch: int = 0):
        """Track detailed statistics for each batch."""
        batch_stat = {
            'batch_number': batch_number,
            'batch_size': batch_size,
            'processing_time': processing_time,
            'records_processed': records_processed,
            'errors_in_batch': errors_in_batch,
            'timestamp': datetime.now(timezone.utc),
            'records_per_second': records_processed / processing_time if processing_time > 0 else 0
        }

        self.batch_stats.append(batch_stat)
        self.sync_stats['batch_count'] += 1
        self.sync_stats['batch_sizes'].append(batch_size)
        self.sync_stats['batch_processing_times'].append(processing_time)

        if records_processed > 0:
            self.sync_stats['total_batches_processed'] += 1

        if errors_in_batch > 0:
            self.sync_stats['batches_with_errors'] += 1
            self.sync_stats['error_count_total'] += errors_in_batch

        # Update performance stats
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        if current_memory > self.performance_stats['memory_usage_peak']:
            self.performance_stats['memory_usage_peak'] = current_memory

    def track_error(self, error_type: str, error_message: str, record_id: str = None):
        """Track detailed error statistics."""
        if error_type not in self.error_stats:
            self.error_stats[error_type] = {
                'count': 0,
                'messages': [],
                'record_ids': []
            }

        self.error_stats[error_type]['count'] += 1
        self.error_stats[error_type]['messages'].append(error_message)
        if record_id:
            self.error_stats[error_type]['record_ids'].append(record_id)

        self.sync_stats['error_count_total'] += 1

    def track_data_quality_issue(self, issue_type: str, record_id: str = None):
        """Track data quality issues."""
        if issue_type == 'duplicate':
            self.sync_stats['duplicate_records_found'] += 1
        elif issue_type == 'invalid':
            self.sync_stats['invalid_records_skipped'] += 1
        elif issue_type == 'null_value':
            self.sync_stats['null_values_count'] += 1
        elif issue_type == 'transformation_error':
            self.sync_stats['data_transformation_errors'] += 1

    async def finalize_sync_statistics(self, db: AsyncSession, store_id: int):
        """Finalize and calculate comprehensive sync statistics."""
        from sqlalchemy import select

        now = datetime.now(timezone.utc)
        self.sync_stats['sync_finished_at'] = now

        # Update checkpoint with local sync finished time
        checkpoint_result = await db.execute(
            select(SyncCheckpoint).filter(
                SyncCheckpoint.store_id == store_id,
                SyncCheckpoint.entity_type == self.entity_type
            )
        )
        checkpoint = checkpoint_result.scalar_one_or_none()

        if checkpoint:
            checkpoint.local_sync_finished_at = now
            checkpoint.current_sync_stage = 'completed'
            await db.commit()
            logger.info(f"📊 Set local sync finished time for {self.entity_type}: {now}")

        # Calculate final counts
        await self._capture_final_counts(db, store_id)

        # Calculate performance metrics
        self._calculate_performance_metrics()

        # Calculate batch statistics
        self._calculate_batch_statistics()

        # Calculate error rates
        self._calculate_error_rates()

        # Capture environment info
        self._capture_environment_info()

        # Log final statistics for validation
        logger.info(f"📊 FINAL SYNC STATS for {self.entity_type}:")
        logger.info(f"   Source records: {self.sync_stats['source_record_count_before']} -> {self.sync_stats['source_record_count_after']}")
        logger.info(f"   Destination records: {self.sync_stats['destination_record_count_before']} -> {self.sync_stats['destination_record_count_after']}")
        logger.info(f"   Records processed: {sum(batch['records_processed'] for batch in self.batch_stats)}")
        logger.info(f"   Total errors: {self.sync_stats['error_count_total']}")
        logger.info(f"   Performance: {self.performance_stats.get('records_per_second', 0):.2f} records/sec")

        logger.info(f"📊 Finalized comprehensive statistics for {self.entity_type} sync")

    async def _capture_final_counts(self, db: AsyncSession, store_id: int):
        """Capture final record counts."""
        try:
            # Get the correct destination table name
            destination_table = self.get_destination_table()

            logger.debug(f"Attempting to query final destination table '{destination_table}' for store_id {store_id}")

            # Get final destination count
            dest_count_query = self.get_count_query(destination_table, store_id)
            dest_result = await db.execute(dest_count_query, {'store_id': store_id})
            self.sync_stats['destination_record_count_after'] = dest_result.scalar() or 0
            logger.debug(f"Final destination count for {destination_table}: {self.sync_stats['destination_record_count_after']}")

            # Calculate changes
            dest_before = self.sync_stats['destination_record_count_before']
            dest_after = self.sync_stats['destination_record_count_after']
            if dest_after > dest_before:
                self.sync_stats['destination_inserted_count'] = dest_after - dest_before

        except Exception as e:
            logger.warning(f"Could not capture final destination counts: {e}")
            logger.warning(f"This error likely indicates that table '{self.get_destination_table()}' does not exist in the local database")
            logger.warning(f"For metafield syncers, the destination is typically a different table (e.g., product_variants.metafields)")
            logger.warning(f"This causes transaction abortion, making all subsequent queries fail")

    def _calculate_performance_metrics(self):
        """Calculate performance metrics."""
        total_time = time.time() - self.performance_stats['start_time']
        total_records = sum(batch['records_processed'] for batch in self.batch_stats)

        self.performance_stats.update({
            'total_sync_time': total_time,
            'records_per_second': total_records / total_time if total_time > 0 else 0,
            'final_memory_usage': psutil.Process().memory_info().rss / 1024 / 1024
        })

    def _calculate_batch_statistics(self):
        """Calculate batch-level statistics."""
        if self.sync_stats['batch_sizes']:
            self.sync_stats.update({
                'average_batch_size': sum(self.sync_stats['batch_sizes']) / len(self.sync_stats['batch_sizes']),
                'max_batch_size': max(self.sync_stats['batch_sizes']),
                'min_batch_size': min(self.sync_stats['batch_sizes'])
            })

        if self.sync_stats['batch_processing_times']:
            self.sync_stats.update({
                'average_batch_processing_time': sum(self.sync_stats['batch_processing_times']) / len(self.sync_stats['batch_processing_times']),
                'max_batch_processing_time': max(self.sync_stats['batch_processing_times']),
                'min_batch_processing_time': min(self.sync_stats['batch_processing_times'])
            })

    def _calculate_error_rates(self):
        """Calculate error rates and statistics."""
        total_records = sum(batch['records_processed'] for batch in self.batch_stats)
        if total_records > 0:
            self.sync_stats['error_rate_percentage'] = (self.sync_stats['error_count_total'] / total_records) * 100

    def _capture_environment_info(self):
        """Capture environment and version information."""
        self.sync_stats['environment_info'] = {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'processor': platform.processor(),
            'architecture': platform.architecture(),
            'memory_total': psutil.virtual_memory().total / 1024 / 1024 / 1024,  # GB
            'cpu_count': psutil.cpu_count(),
            'sync_version': '1.0.0',  # Would be dynamic in real implementation
            'airbyte_version': '0.50.0'  # Would be dynamic in real implementation
        }

    async def update_sync_checkpoint_comprehensive(self, db: AsyncSession, store_id: int, processed_count: int):
        """Update sync checkpoint with comprehensive statistics."""
        from sqlalchemy import select

        checkpoint_result = await db.execute(
            select(SyncCheckpoint).filter(
                SyncCheckpoint.store_id == store_id,
                SyncCheckpoint.entity_type == self.entity_type
            )
        )
        checkpoint = checkpoint_result.scalar_one_or_none()

        now = datetime.now(timezone.utc)

        if checkpoint:
            # Update existing checkpoint with comprehensive stats
            self._update_checkpoint_with_stats(checkpoint, processed_count, now)
        else:
            # Create new checkpoint with comprehensive stats
            checkpoint = self._create_checkpoint_with_stats(store_id, processed_count, now)
            db.add(checkpoint)

        # Record sync checkpoint update metric
        sync_checkpoint_updates.labels(
            store_domain="",  # We don't have store_domain here, could be enhanced
            entity_type=self.entity_type,
            stage="completed"
        ).inc()

        await db.commit()
        logger.info(f"📊 Updated comprehensive checkpoint for {self.entity_type}: {processed_count} processed")

    def _update_checkpoint_with_stats(self, checkpoint, processed_count: int, now: datetime):
        """Update existing checkpoint with comprehensive statistics."""
        checkpoint.last_successful_sync_at = now
        checkpoint.last_sync_status = 'completed'
        checkpoint.updated_at = now
        checkpoint.records_processed_in_sync = processed_count
        checkpoint.sync_finished_at = now
        checkpoint.sync_duration_seconds = (now - (checkpoint.sync_started_at or now)).total_seconds()

        # Set local sync start time if not already set
        if not checkpoint.local_sync_started_at:
            checkpoint.local_sync_started_at = checkpoint.sync_started_at

        logger.info(f"📊 Updating checkpoint with {processed_count} processed records")

        # Update with comprehensive statistics
        self._populate_checkpoint_stats(checkpoint)

    def _create_checkpoint_with_stats(self, store_id: int, processed_count: int, now: datetime):
        """Create new checkpoint with comprehensive statistics."""
        checkpoint = SyncCheckpoint(
            store_id=store_id,
            entity_type=self.entity_type,
            last_successful_sync_at=now,
            last_sync_status='completed',
            created_at=now,
            updated_at=now,
            records_processed_in_sync=processed_count,
            sync_started_at=self.sync_stats['sync_started_at'],
            sync_finished_at=now,
            sync_duration_seconds=(now - (self.sync_stats['sync_started_at'] or now)).total_seconds()
        )

        # Populate with comprehensive statistics
        self._populate_checkpoint_stats(checkpoint)
        return checkpoint

    def _serialize_for_json(self, obj):
        """Serialize objects for JSON storage, converting datetime objects to ISO strings."""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {key: self._serialize_for_json(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._serialize_for_json(item) for item in obj]
        else:
            return obj

    def _populate_checkpoint_stats(self, checkpoint):
        """Populate checkpoint with all comprehensive statistics."""
        logger.info(f"📊 Populating checkpoint stats for {self.entity_type} with {len(self.sync_stats)} stat fields")

        # Basic sync tracking
        checkpoint.last_updated_at = self.sync_stats['sync_finished_at']
        checkpoint.last_id = None  # Could be set to last processed ID
        checkpoint.airbyte_state = None  # Airbyte state JSON if available

        # End-to-end sync tracking
        checkpoint.local_sync_started_at = self.sync_stats['sync_started_at']
        checkpoint.sync_trigger_type = 'manual'  # Could be passed from caller
        checkpoint.records_processed_in_sync = sum(batch['records_processed'] for batch in self.batch_stats)

        # Source database statistics
        checkpoint.source_table_name = self.entity_type
        checkpoint.source_database_name = 'airbyte'
        checkpoint.source_record_count_before = self.sync_stats['source_record_count_before']
        checkpoint.source_record_count_after = self.sync_stats['source_record_count_after']
        checkpoint.source_new_records_count = self.sync_stats['source_new_records_count']
        checkpoint.source_updated_records_count = self.sync_stats['source_updated_records_count']
        checkpoint.source_deleted_records_count = self.sync_stats['source_deleted_records_count']
        checkpoint.source_last_updated_at = self.sync_stats.get('source_last_updated_at')
        checkpoint.source_filter_criteria = self.sync_stats['source_filter_criteria']

        # Destination database statistics
        checkpoint.destination_table_name = self.get_destination_table()
        checkpoint.destination_database_name = 'ecommerce_db'
        checkpoint.destination_record_count_before = self.sync_stats['destination_record_count_before']
        checkpoint.destination_record_count_after = self.sync_stats['destination_record_count_after']
        checkpoint.destination_inserted_count = self.sync_stats['destination_inserted_count']
        checkpoint.destination_updated_count = self.sync_stats['destination_updated_count']
        checkpoint.destination_deleted_count = self.sync_stats['destination_deleted_count']
        checkpoint.destination_failed_count = self.sync_stats['destination_failed_count']
        checkpoint.destination_last_updated_at = self.sync_stats['sync_finished_at']

        # Sync process statistics
        checkpoint.sync_started_at = self.sync_stats['sync_started_at']
        checkpoint.sync_finished_at = self.sync_stats['sync_finished_at']
        checkpoint.batch_count = self.sync_stats['batch_count']
        checkpoint.average_batch_size = self.sync_stats.get('average_batch_size')
        checkpoint.max_batch_size = self.sync_stats.get('max_batch_size', 0)
        checkpoint.min_batch_size = self.sync_stats.get('min_batch_size', 0)
        checkpoint.total_batches_processed = self.sync_stats['total_batches_processed']
        checkpoint.batches_with_errors = self.sync_stats['batches_with_errors']
        checkpoint.retry_count = self.sync_stats['retry_count']
        checkpoint.max_retries = 3  # Default, could be configurable

        # Performance statistics
        checkpoint.records_per_second = self.performance_stats.get('records_per_second')
        checkpoint.average_batch_processing_time = self.sync_stats.get('average_batch_processing_time')
        checkpoint.max_batch_processing_time = self.sync_stats.get('max_batch_processing_time')
        checkpoint.min_batch_processing_time = self.sync_stats.get('min_batch_processing_time')
        checkpoint.memory_usage_peak = self.performance_stats['memory_usage_peak']
        checkpoint.database_connection_count = self.performance_stats.get('database_connections_used', 1)

        # Error statistics
        checkpoint.last_error_message = str(self.error_stats) if self.error_stats else None
        checkpoint.error_count_total = self.sync_stats['error_count_total']
        checkpoint.error_count_by_type = self._serialize_for_json(self.error_stats)
        checkpoint.last_error_at = self.sync_stats['sync_finished_at'] if self.sync_stats['error_count_total'] > 0 else None
        checkpoint.consecutive_error_count = 0  # Would need to be tracked across syncs
        checkpoint.error_rate_percentage = self.sync_stats.get('error_rate_percentage')

        # Data quality statistics
        checkpoint.duplicate_records_found = self.sync_stats['duplicate_records_found']
        checkpoint.invalid_records_skipped = self.sync_stats['invalid_records_skipped']
        checkpoint.null_values_count = self.sync_stats['null_values_count']
        checkpoint.data_transformation_errors = self.sync_stats['data_transformation_errors']

        # Batch-level statistics (serialize datetime objects to ISO strings)
        checkpoint.batch_statistics = self._serialize_for_json(self.batch_stats)

        # Metadata and versioning
        checkpoint.sync_version = '1.0.0'
        checkpoint.platform_version = 'shopify_api_2023_10'
        checkpoint.airbyte_version = '0.50.0'
        checkpoint.configuration_hash = 'sync_config_v1'  # Would be calculated from actual config
        checkpoint.environment_info = self.sync_stats.get('environment_info')

        # Update total records to reflect current state
        checkpoint.total_records = self.sync_stats['destination_record_count_after']

        # Log populated fields for validation
        populated_fields = [
            field for field in [
                'source_table_name', 'source_database_name', 'source_record_count_before',
                'destination_record_count_before', 'batch_count', 'error_count_total',
                'records_per_second', 'total_records', 'sync_started_at', 'sync_finished_at'
            ] if getattr(checkpoint, field, None) is not None
        ]
        logger.info(f"✅ Checkpoint populated with {len(populated_fields)} fields: {populated_fields}")

    def get_batch_size(self) -> int:
        """Get batch size from configuration."""
        batch_size = self.platform_config.get('batch_size')
        if batch_size is None:
            raise ValueError(f"No batch_size configured for platform syncer")
        return batch_size

    def get_destination_table(self) -> str:
        """Get the destination table name for statistics tracking.

        Subclasses can override this if their destination table differs from entity_type.
        For example, metafield syncers write to product_variants but have entity_type 'metafield_product_variants'.
        """
        return self.entity_type

    def create_airbyte_engine(self):
        """Create Airbyte database engine."""
        airbyte_url = (
            f"postgresql://{self.settings.AIRBYTE_DATABASE_USER}:"
            f"{self.settings.AIRBYTE_DATABASE_PASSWORD}@"
            f"{self.settings.AIRBYTE_DATABASE_HOST}:"
            f"{self.settings.AIRBYTE_DATABASE_PORT}/"
            f"{self.settings.AIRBYTE_DATABASE_NAME}"
        )
        from sqlalchemy import create_engine
        return create_engine(
            airbyte_url,
            pool_size=5,
            max_overflow=10,
            pool_recycle=3600
        )

    async def sync(self, db: AsyncSession, store_id: int) -> Dict[str, Any]:
        """
        Main sync method to be implemented by subclasses.

        Args:
            db: Database session
            store_id: Store ID to sync

        Returns:
            Sync results dictionary
        """
        raise NotImplementedError("Subclasses must implement sync method")

    def get_select_query(self, store: Store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """
        Generate the SELECT query for fetching data from Airbyte.
        To be implemented by subclasses.
        """
        raise NotImplementedError("Subclasses must implement get_select_query")

    def get_insert_query(self) -> str:
        """
        Generate the INSERT query for bulk inserting data.
        To be implemented by subclasses.
        """
        raise NotImplementedError("Subclasses must implement get_insert_query")

    def transform_row(self, row: tuple, store_id: int) -> Optional[tuple]:
        """
        Transform a row from Airbyte format to production format.
        To be implemented by subclasses.

        Returns:
            Transformed row tuple, or None to skip the row
        """
        raise NotImplementedError("Subclasses must implement transform_row")