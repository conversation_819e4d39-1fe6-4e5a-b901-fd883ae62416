"""
Airbyte Service
Handles Airbyte source, destination, and connection management for e-commerce platforms.
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional, List

import requests
import psycopg2
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from modules.stores.models import Store
from core.services.redis_lock import redis_lock
from core.metrics import airbyte_sync_jobs_created, airbyte_sync_jobs_completed

logger = logging.getLogger(__name__)


class AirbyteService:
    """Manages Airbyte sources, destinations, and connections for e-commerce platforms."""

    def __init__(self):
        settings = get_settings()
        self.settings = settings
        self.airbyte_available = False

        # Load configuration
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "core", "configs", "airbyte_service_config.json")
        with open(config_path, 'r') as f:
            self.config = json.load(f)

        # Extract Airbyte configuration from config file
        airbyte_config = self.config.get("airbyte", {})
        self.api_url = airbyte_config.get("api_url", settings.AIRBYTE_API_URL)
        self.user_id = airbyte_config.get("user_id", settings.AIRBYTE_USER_ID)
        self.password = airbyte_config.get("password", settings.AIRBYTE_PASSWORD)
        self.workspace_id = airbyte_config.get("workspace_id", settings.AIRBYTE_WORKSPACE_ID)
        self.source_definition_name = airbyte_config.get("source_definition_name", "Shopify-Custom")
        self.destination_definition_name = airbyte_config.get("destination_definition_name", "Postgres")

        # Database configuration
        db_config = airbyte_config.get("database", {})
        self.db_host = db_config.get("host", settings.AIRBYTE_DATABASE_HOST)
        self.db_port = db_config.get("port", settings.AIRBYTE_DATABASE_PORT)
        self.db_user = db_config.get("user", settings.AIRBYTE_DATABASE_USER)
        self.db_password = db_config.get("password", settings.AIRBYTE_DATABASE_PASSWORD)
        self.db_name = db_config.get("name", settings.AIRBYTE_DATABASE_NAME)

        # Timeouts
        timeout_config = airbyte_config.get("timeouts", {})
        self.default_timeout = timeout_config.get("default", 30)
        self.discovery_timeout = timeout_config.get("discovery", 60)
        self.creation_timeout = timeout_config.get("creation", 60)

        logger.info(f"✅ Airbyte service config loaded from {config_path}")
        logger.info(f"Using source definition: {self.source_definition_name}")
        logger.info(f"Using destination definition: {self.destination_definition_name}")
        logger.info(f"Configured sync streams: {self.config.get('sync_streams', [])}")
        logger.info(f"Sync schedule: {self.config.get('connection', {}).get('schedule', {})}")

        self._validate_connectivity()

    @property
    def headers(self):
        """Get headers with basic authentication."""
        import base64
        credentials = base64.b64encode(f"{self.user_id}:{self.password}".encode()).decode()
        return {
            "Content-Type": "application/json",
            "Authorization": f"Basic {credentials}"
        }

    def _validate_connectivity(self):
        """Validate Airbyte configuration and connectivity."""
        logger.info("Validating Airbyte connectivity...")
        self._validate_database_connectivity()

        try:
            response = requests.get(f"{self.api_url}/api/v1/health", headers=self.headers, timeout=10)
            if response.status_code == 200:
                self.airbyte_available = True
                logger.info("✅ Airbyte API reachable")
            else:
                logger.warning("❌ Airbyte API unreachable")
        except requests.exceptions.RequestException as e:
            logger.exception(f"Airbyte API check failed: {e}")

    def _validate_database_connectivity(self):
        """Validate Postgres connectivity for Airbyte."""
        try:
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user=self.db_user,
                password=self.db_password,
                database=self.db_name
            )
            conn.close()
            logger.info("✅ Airbyte database connectivity OK")
        except psycopg2.Error as e:
            logger.exception(f"❌ Airbyte DB connectivity failed: {e}")

    # -------- Workspace / Listing --------

    def get_existing_sources(self) -> List[Dict[str, Any]]:
        """Get all sources in workspace."""
        try:
            resp = requests.post(
                f"{self.api_url}/api/v1/sources/list",
                headers=self.headers,
                json={"workspaceId": self.workspace_id},
                timeout=self.default_timeout
            )
            resp.raise_for_status()
            return resp.json().get("sources", [])
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to get sources: {e}")
            return []

    def get_existing_destinations(self) -> List[Dict[str, Any]]:
        """Get all destinations in workspace."""
        try:
            resp = requests.post(
                f"{self.api_url}/api/v1/destinations/list",
                headers=self.headers,
                json={"workspaceId": self.workspace_id},
                timeout=self.default_timeout
            )
            resp.raise_for_status()
            return resp.json().get("destinations", [])
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to get destinations: {e}")
            return []

    def get_existing_connections(self) -> List[Dict[str, Any]]:
        """Get all connections in workspace."""
        try:
            resp = requests.post(
                f"{self.api_url}/api/v1/connections/list",
                headers=self.headers,
                json={"workspaceId": self.workspace_id},
                timeout=self.default_timeout
            )
            resp.raise_for_status()
            return resp.json().get("connections", [])
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to get connections: {e}")
            return []

    # -------- Source / Destination / Connection --------

    def create_shopify_source(self, shop_domain: str, access_token: str, shop_name: str, start_date: str = "2020-01-01") -> Optional[str]:
        """Create Shopify source or return existing."""
        # Validate inputs
        if not shop_domain or not access_token:
            logger.error("Shop domain and access token are required")
            return None

        if not shop_domain.endswith('.myshopify.com'):
            shop_domain = f"{shop_domain}.myshopify.com"
            logger.info(f"Added .myshopify.com suffix: {shop_domain}")

        # Check for existing source
        existing_sources = self.get_existing_sources()
        if existing_sources:
            for s in existing_sources:
                if s.get("name") == shop_name:
                    logger.info(f"Found existing source: {s.get('sourceId')}")
                    return s.get("sourceId")

        # Get Shopify source definition
        try:
            logger.info("Fetching source definitions...")
            resp = requests.post(f"{self.api_url}/api/v1/source_definitions/list", headers=self.headers, timeout=self.default_timeout)
            resp.raise_for_status()
            definitions = resp.json().get("sourceDefinitions", [])
            logger.info(f"Found {len(definitions)} source definitions")
            shopify_def = next((d for d in definitions if d["name"] == self.source_definition_name), None)
            if not shopify_def:
                logger.error(f"Source definition '{self.source_definition_name}' not found")
                logger.error(f"Available definitions: {[d['name'] for d in definitions]}")
                return None
            logger.info(f"Found source definition: {shopify_def['sourceDefinitionId']}")
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to get source definitions: {e}")
            return None

        shopify_config = self.config["shopify_source"]
        payload = {
            "name": shop_name,
            "sourceDefinitionId": shopify_def["sourceDefinitionId"],
            "workspaceId": self.workspace_id,
            "connectionConfiguration": {
                "shop": shop_domain,
                "credentials": {
                    "auth_method": "api_password",
                    "api_password": access_token
                },
                "start_date": start_date or shopify_config["start_date"],
                "bulk_window_in_days": shopify_config["bulk_window_in_days"],
                "job_termination_threshold": shopify_config["job_termination_threshold"],
                "job_checkpoint_interval": shopify_config["job_checkpoint_interval"],
                "job_heartbeat_interval": shopify_config["job_heartbeat_interval"]
            }
        }
        try:
            logger.info(f"Creating Shopify source with payload: {payload}")
            resp = requests.post(f"{self.api_url}/api/v1/sources/create", headers=self.headers, json=payload, timeout=self.creation_timeout)
            if resp.status_code != 200:
                logger.error(f"Airbyte API error: {resp.status_code} - {resp.text}")
            resp.raise_for_status()
            result = resp.json()
            source_id = result.get("sourceId")
            logger.info(f"Successfully created Shopify source: {source_id}")
            return source_id
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to create Shopify source: {e}")
            if hasattr(e, 'response') and e.response:
                logger.exception(f"Response content: {e.response.text}")
            return None

    # The code `create_postgres_destination` appears to be a function or method call in Python
    # related to creating a destination in a PostgreSQL database. However, without the actual
    # implementation of the function or method, it is not possible to provide more specific
    # details about what it does.
    def create_postgres_destination(self, shop_id: int) -> Optional[str]:
        """Create Postgres destination or return existing shared destination."""
        # Use a single shared destination for all stores
        destination_name = self.destination_definition_name

        # Check if shared destination already exists
        for d in self.get_existing_destinations():
            if d.get("name") == destination_name:
                logger.info(f"Found existing shared destination: {d.get('destinationId')}")
                return d.get("destinationId")

        # Create new shared destination if it doesn't exist
        resp = requests.post(f"{self.api_url}/api/v1/destination_definitions/list", headers=self.headers, timeout=self.default_timeout)
        resp.raise_for_status()
        definitions = resp.json().get("destinationDefinitions", [])
        postgres_def = next((d for d in definitions if d["name"] == self.destination_definition_name), None)
        if not postgres_def:
            logger.error(f"{self.destination_definition_name} destination definition not found")
            return None

        connection_config = self.config["connection"]
        payload = {
            "name": destination_name,
            "destinationDefinitionId": postgres_def["destinationDefinitionId"],
            "workspaceId": self.workspace_id,
            "connectionConfiguration": {
                "host": self.db_host,
                "port": self.db_port,
                "database": self.db_name,
                "username": self.db_user,
                "password": self.db_password,
                "ssl": self.config["airbyte"]["database"].get("ssl", False),
                "schema": self.config["airbyte"]["database"].get("schema", "public")
            },
            "resourceRequirements": connection_config["resource_requirements"]
        }
        try:
            logger.info(f"Creating shared Postgres destination: {destination_name}")
            resp = requests.post(f"{self.api_url}/api/v1/destinations/create", headers=self.headers, json=payload, timeout=self.creation_timeout)
            resp.raise_for_status()
            destination_id = resp.json().get("destinationId")
            logger.info(f"Successfully created shared destination: {destination_id}")
            return destination_id
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to create {destination_name} destination: {e}")
            return None

    def discover_source_schema(self, source_id: str) -> Optional[Dict[str, Any]]:
        """Discover schema for source."""
        if not source_id:
            logger.error("Source ID is required for schema discovery")
            return None

        # Verify source exists
        existing_sources = self.get_existing_sources()
        if existing_sources:
            source_exists = any(s.get("sourceId") == source_id for s in existing_sources)
            if not source_exists:
                logger.error(f"Source {source_id} does not exist")
                return None

        try:
            logger.info(f"Discovering schema for source: {source_id}")
            payload = {"sourceId": source_id}
            logger.info(f"Discovery payload: {payload}")
            resp = requests.post(f"{self.api_url}/api/v1/sources/discover_schema", headers=self.headers, json=payload, timeout=self.discovery_timeout)
            if resp.status_code != 200:
                logger.error(f"Schema discovery failed: {resp.status_code} - {resp.text}")
            resp.raise_for_status()
            result = resp.json()
            catalog = result.get("catalog")
            if catalog:
                logger.info(f"Successfully discovered schema with {len(catalog.get('streams', []))} streams")
            else:
                logger.warning("Schema discovery returned empty catalog")
            return catalog
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to discover schema: {e}")
            if hasattr(e, 'response') and e.response:
                logger.exception(f"Response content: {e.response.text}")
            return None

    def _create_sync_catalog(self, catalog: Dict[str, Any], shop_id: int) -> Dict[str, Any]:
        """Create sync catalog for products and related streams."""
        streams_to_sync = set(self.config["sync_streams"])
        sync_catalog = {"streams": []}

        for stream in catalog.get("streams", []):
            name = stream["stream"]["name"]
            if name not in streams_to_sync:
                continue

            stream_config_data = self.config["stream_config"].get(name, {})
            stream_config = {
                "stream": stream["stream"],
                "config": {
                    "syncMode": stream_config_data.get("sync_mode", "incremental"),
                    "destinationSyncMode": stream_config_data.get("destination_sync_mode", "append"),
                    "selected": True
                }
            }

            # Add cursor field from config or stream default
            cursor_field = stream_config_data.get("cursor_field")
            if cursor_field:
                stream_config["config"]["cursorField"] = cursor_field
            elif stream["stream"].get("defaultCursorField"):
                stream_config["config"]["cursorField"] = stream["stream"]["defaultCursorField"]

            # Add primary key from config or stream default
            primary_key = stream_config_data.get("primary_key")
            if primary_key:
                stream_config["config"]["primaryKey"] = primary_key
            elif stream["stream"].get("sourceDefinedPrimaryKey"):
                stream_config["config"]["primaryKey"] = stream["stream"]["sourceDefinedPrimaryKey"]

            sync_catalog["streams"].append(stream_config)
        return sync_catalog

    def create_connection(self, source_id: str, destination_id: str, shop_id: int, shop_name: str, catalog: Dict[str, Any]) -> Optional[str]:
        """Create connection for syncing."""
        connection_name = f"{shop_name} -> {self.destination_definition_name} (Shared)"
        for c in self.get_existing_connections():
            if c.get("name") == connection_name:
                return c.get("connectionId")

        sync_catalog = self._create_sync_catalog(catalog, shop_id)
        connection_config = self.config["connection"]
        payload = {
            "name": connection_name,
            "sourceId": source_id,
            "destinationId": destination_id,
            "status": "active",
            "syncMode": "incremental",
            "destinationSyncMode": "append",
            "schedule": {
                "timeUnit": connection_config["schedule"]["time_unit"],
                "units": connection_config["schedule"]["units"]
            },
            "syncCatalog": sync_catalog,
            "resourceRequirements": connection_config["resource_requirements"]
        }
        try:
            logger.info(f"Creating connection with payload: {payload}")
            resp = requests.post(f"{self.api_url}/api/v1/connections/create", headers=self.headers, json=payload, timeout=self.creation_timeout)
            if resp.status_code != 200:
                logger.error(f"Connection creation failed: {resp.status_code} - {resp.text}")
            resp.raise_for_status()
            result = resp.json()
            connection_id = result.get("connectionId")
            logger.info(f"Successfully created connection: {connection_id}")
            return connection_id
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to create connection: {e}")
            if hasattr(e, 'response') and e.response:
                logger.exception(f"Response content: {e.response.text}")
            return None

    # -------- Async Setup & Sync --------

    async def setup_shop_sync_async(self, db: AsyncSession, store: Store, access_token: str, start_date: str = None) -> Dict[str, Any]:
        """Complete setup of Airbyte sync for a shop with Redis locking."""
        if not self.airbyte_available:
            raise Exception("Airbyte service not available.")

        lock_key = f"lock:shop:{store.id}"
        async with redis_lock.acquire_lock(lock_key) as lock_acquired:
            if not lock_acquired:
                raise Exception(f"Shop {store.id} setup in progress")

            # Check if connection already exists and enable it
            if store.airbyte_connection_id:
                if self.enable_connection(store.airbyte_connection_id):
                    logger.info(f"Re-enabled existing connection for store {store.id}")
                    job_id = await self.trigger_sync(store.airbyte_connection_id, store.id)
                    return {
                        "source_id": store.airbyte_source_id,
                        "destination_id": store.airbyte_destination_id,
                        "connection_id": store.airbyte_connection_id,
                        "initial_sync_job_id": job_id
                    }
                else:
                    logger.warning(f"Failed to re-enable connection for store {store.id}, will recreate")

            # Create new setup if no existing connection or re-enable failed
            source_id = self.create_shopify_source(store.shop_domain, access_token, store.shop_name or f"Store {store.id}", start_date)
            if not source_id:
                raise Exception("Failed to create Shopify source")

            destination_id = self.create_postgres_destination(store.id)
            if not destination_id:
                raise Exception("Failed to create Postgres destination")

            catalog = self.discover_source_schema(source_id)
            if not catalog:
                raise Exception("Failed to discover source schema")

            connection_id = self.create_connection(source_id, destination_id, store.id, store.shop_name or f"Store {store.id}", catalog)
            if not connection_id:
                raise Exception("Failed to create connection")

            # Save Airbyte IDs
            store.airbyte_source_id = source_id
            store.airbyte_destination_id = destination_id
            store.airbyte_connection_id = connection_id
            await db.commit()

            job_id = await self.trigger_sync(connection_id, store.id)
            return {
                "source_id": source_id,
                "destination_id": destination_id,
                "connection_id": connection_id,
                "initial_sync_job_id": job_id
            }

    async def setup_shop_sync(self, db: AsyncSession, store: Store, access_token: str, start_date: str = None) -> Dict[str, Any]:
        """Async version of setup_shop_sync that takes AsyncSession directly."""
        return await self.setup_shop_sync_async(db, store, access_token, start_date)

    async def trigger_sync(self, connection_id: str, shop_id: int, entity_type: str = None) -> Optional[int]:
        """Trigger sync for connection."""
        lock_key = f"lock:shop:{shop_id}"
        async with redis_lock.acquire_lock(lock_key) as lock_acquired:
            if not lock_acquired:
                logger.warning(f"Sync already in progress for shop {shop_id}")
                return None
            try:
                payload = {"connectionId": connection_id}
                resp = requests.post(f"{self.api_url}/api/v1/connections/sync", headers=self.headers, json=payload, timeout=self.default_timeout)
                resp.raise_for_status()
                job_id = resp.json()["job"]["id"]

                # Record Airbyte sync job created metric
                airbyte_sync_jobs_created.labels(
                    store_domain="",  # We don't have store_domain here, could be enhanced
                    connection_id=connection_id
                ).inc()

                logger.info(f"Created Airbyte sync job {job_id} for connection {connection_id}")

                # Schedule job status polling
                from servers.worker.tasks.sync_tasks import poll_airbyte_job_status
                # Poll after 30 seconds, then every 60 seconds for up to 30 minutes
                poll_airbyte_job_status.apply_async(
                    args=[job_id, shop_id, entity_type],
                    countdown=30  # Start polling after 30 seconds
                )

                return job_id
            except requests.exceptions.RequestException as e:
                logger.exception(f"Failed to trigger sync: {e}")
                return None

    # -------- Deletion / Deregistration --------

    def delete_connection(self, connection_id: str) -> bool:
        try:
            resp = requests.delete(f"{self.api_url}/api/v1/connections/{connection_id}", headers=self.headers, timeout=self.default_timeout)
            if resp.status_code == 403:
                logger.warning(f"403 Forbidden: No permission to delete connection {connection_id}. Connection will remain in Airbyte but will be disconnected from the store.")
                return False
            elif resp.status_code == 404:
                logger.info(f"Connection {connection_id} not found (may have been already deleted)")
                return True  # Consider this successful
            resp.raise_for_status()
            logger.info(f"Successfully deleted connection {connection_id}")
            return True
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to delete connection {connection_id}: {e}")
            return False

    def delete_source(self, source_id: str) -> bool:
        try:
            resp = requests.delete(f"{self.api_url}/api/v1/sources/{source_id}", headers=self.headers, timeout=self.default_timeout)
            if resp.status_code == 403:
                logger.warning(f"403 Forbidden: No permission to delete source {source_id}. Source will remain in Airbyte but will be disconnected from the store.")
                return False
            elif resp.status_code == 404:
                logger.info(f"Source {source_id} not found (may have been already deleted)")
                return True  # Consider this successful
            resp.raise_for_status()
            logger.info(f"Successfully deleted source {source_id}")
            return True
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to delete source {source_id}: {e}")
            return False

    def delete_destination(self, destination_id: str) -> bool:
        try:
            resp = requests.delete(f"{self.api_url}/api/v1/destinations/{destination_id}", headers=self.headers, timeout=self.default_timeout)
            resp.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to delete destination: {e}")
            return False

    def disable_connection(self, connection_id: str) -> bool:
        """Disable (pause) a connection."""
        try:
            # Use the correct API endpoint format with connectionId in the request body
            payload = {
                "connectionId": connection_id,
                "status": "inactive"
            }
            resp = requests.post(f"{self.api_url}/api/v1/connections/update", headers=self.headers, json=payload, timeout=self.default_timeout)
            if resp.status_code == 403:
                logger.warning(f"403 Forbidden when disabling connection {connection_id}. This might be due to insufficient permissions.")
                logger.warning("The connection may still be active in Airbyte, but we'll proceed with the disconnect.")
                # Don't return False here - we want to continue with the disconnect process
                # The store will be marked as inactive in our database
                return True
            resp.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to disable connection: {e}")
            return False

    def enable_connection(self, connection_id: str) -> bool:
        """Enable (resume) a connection."""
        try:
            payload = {
                "connectionId": connection_id,
                "status": "active"
            }
            resp = requests.post(f"{self.api_url}/api/v1/connections/update", headers=self.headers, json=payload, timeout=self.default_timeout)
            resp.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to enable connection: {e}")
            return False

    def get_jobs_for_connection(self, connection_id: str) -> List[Dict[str, Any]]:
        """Get all jobs for a connection."""
        try:
            resp = requests.post(
                f"{self.api_url}/api/v1/jobs/list",
                headers=self.headers,
                json={"configTypes": ["sync"], "configId": connection_id},
                timeout=self.default_timeout
            )
            resp.raise_for_status()
            return resp.json().get("jobs", [])
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to get jobs for connection {connection_id}: {e}")
            return []

    def get_job_details(self, job_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed information for a specific Airbyte job including sync statistics."""
        try:
            resp = requests.post(
                f"{self.api_url}/api/v1/jobs/get",
                headers=self.headers,
                json={"id": job_id},
                timeout=self.default_timeout
            )
            resp.raise_for_status()
            job_data = resp.json().get("job", {})

            # Extract sync statistics if available
            if "attempts" in job_data and job_data["attempts"]:
                latest_attempt = job_data["attempts"][-1]  # Get the latest attempt
                if "sync" in latest_attempt:
                    sync_stats = latest_attempt["sync"]
                    logger.info(f"Airbyte job {job_id} sync stats: {sync_stats}")
                    return {
                        "job": job_data,
                        "sync_stats": sync_stats
                    }

            return {"job": job_data, "sync_stats": None}
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to get job details for job {job_id}: {e}")
            return None

    def get_job_sync_stats(self, job_id: int) -> Optional[Dict[str, Any]]:
        """Get sync statistics for a completed Airbyte job."""
        job_details = self.get_job_details(job_id)
        if not job_details or not job_details.get("sync_stats"):
            return None

        sync_stats = job_details["sync_stats"]
        stats = {}

        # Extract record counts from sync stats
        if "streams" in sync_stats:
            total_records_emitted = 0
            total_records_committed = 0

            for stream in sync_stats["streams"]:
                stream_name = stream.get("streamName", "")
                stream_stats = stream.get("stats", {})

                # Count records emitted (read from source)
                records_emitted = stream_stats.get("recordsEmitted", 0)
                total_records_emitted += records_emitted

                # Count records committed (written to destination)
                records_committed = stream_stats.get("recordsCommitted", 0)
                total_records_committed += records_committed

                stats[f"{stream_name}_emitted"] = records_emitted
                stats[f"{stream_name}_committed"] = records_committed

            stats["total_records_emitted"] = total_records_emitted
            stats["total_records_committed"] = total_records_committed

        # Extract timing information
        if "startTime" in sync_stats:
            stats["sync_start_time"] = sync_stats["startTime"]
        if "endTime" in sync_stats:
            stats["sync_end_time"] = sync_stats["endTime"]

        logger.info(f"Extracted sync stats for job {job_id}: {stats}")
        return stats

    def get_incremental_sync_record_count(self, connection_id: str, entity_type: str, last_sync_time: datetime = None) -> Dict[str, Any]:
        """Get the number of records that would be synced in an incremental sync."""
        try:
            # Get connection details to find the source
            connections = self.get_existing_connections()
            connection = next((c for c in connections if c.get("connectionId") == connection_id), None)

            if not connection:
                logger.error(f"Connection {connection_id} not found")
                return {"error": "Connection not found"}

            source_id = connection.get("sourceId")
            if not source_id:
                logger.error(f"No source ID found for connection {connection_id}")
                return {"error": "No source ID found"}

            # Discover schema to get available streams
            catalog = self.discover_source_schema(source_id)
            if not catalog:
                return {"error": "Could not discover source schema"}

            # Find the stream for this entity type
            stream = None
            for s in catalog.get("streams", []):
                if s.get("stream", {}).get("name") == entity_type:
                    stream = s
                    break

            if not stream:
                return {"error": f"Stream {entity_type} not found in catalog"}

            # Query Airbyte database for record count
            airbyte_engine = self._create_airbyte_engine()
            with airbyte_engine.connect() as conn:
                if last_sync_time:
                    # Count records newer than last sync time
                    count_query = f"SELECT COUNT(*) FROM {entity_type} WHERE _airbyte_extracted_at > :last_sync_time"
                    result = conn.execute(count_query, {"last_sync_time": last_sync_time})
                else:
                    # Count all records
                    count_query = f"SELECT COUNT(*) FROM {entity_type}"
                    result = conn.execute(count_query)

                record_count = result.scalar() or 0

                # Also get the latest extraction time
                latest_time_query = f"SELECT MAX(_airbyte_extracted_at) FROM {entity_type}"
                latest_result = conn.execute(latest_time_query)
                latest_time = latest_result.scalar()

            return {
                "entity_type": entity_type,
                "record_count": record_count,
                "latest_extraction_time": latest_time,
                "last_sync_time": last_sync_time,
                "connection_id": connection_id
            }

        except Exception as e:
            logger.exception(f"Error getting incremental sync record count: {e}")
            return {"error": str(e)}

    def _create_airbyte_engine(self):
        """Create Airbyte database engine for queries."""
        from sqlalchemy import create_engine
        airbyte_url = (
            f"postgresql://{self.db_user}:{self.db_password}@"
            f"{self.db_host}:{self.db_port}/{self.db_name}"
        )
        return create_engine(airbyte_url)

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a running job."""
        try:
            resp = requests.post(f"{self.api_url}/api/v1/jobs/{job_id}/cancel", headers=self.headers, timeout=self.default_timeout)
            if resp.status_code == 403:
                logger.warning(f"403 Forbidden when cancelling job {job_id}. This might be due to insufficient permissions.")
                # Don't fail the whole process for permission issues
                return True
            resp.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            logger.exception(f"Failed to cancel job {job_id}: {e}")
            return False

    async def disconnect_store_from_airbyte(self, db: AsyncSession, store: Store) -> bool:
        """Disable connection for store (keep source and destination for reconnection)."""
        success = True

        # Store the store ID before any potential session issues
        store_id = store.id

        if store.airbyte_connection_id:
            logger.info(f"Starting disconnect process for store {store_id}, connection {store.airbyte_connection_id}")

            # Cancel any running sync jobs for this connection
            jobs = self.get_jobs_for_connection(store.airbyte_connection_id)
            running_jobs = [job for job in jobs if job.get("status") in ["running", "pending"]]

            if running_jobs:
                logger.info(f"Found {len(running_jobs)} running/pending jobs to cancel for store {store_id}")
                for job in running_jobs:
                    job_id = job.get("job", {}).get("id")
                    if job_id and not self.cancel_job(str(job_id)):
                        logger.warning(f"Failed to cancel job {job_id} for store {store_id}")
                        # Don't fail the whole disconnect if job cancellation fails
                    else:
                        logger.info(f"Cancelled running job {job_id} for store {store_id}")
            else:
                logger.info(f"No running jobs found for store {store_id}")

            # Disable the connection
            if not self.disable_connection(store.airbyte_connection_id):
                logger.warning(f"Failed to disable Airbyte connection for store {store_id}, but proceeding with disconnect")
                # Don't set success = False here - we still want to complete the disconnect
            else:
                logger.info(f"Successfully disabled Airbyte connection for store {store_id}")

        if success:
            logger.info(f"Disconnect process completed successfully for store {store_id} (kept shared destination)")
        else:
            logger.warning(f"Disconnect process had issues for store {store_id}")

        return success

    async def deregister_store_from_airbyte(self, db: AsyncSession, store: Store) -> bool:
        """Delete connection and source for store (keep shared destination for other stores)."""
        success = True

        # Store the store ID before any potential session issues
        store_id = store.id

        # Check if we have permission to list connections first
        # If we can't list connections, we probably don't have permission to delete them either
        try:
            test_connections = self.get_existing_connections()
            if not test_connections and test_connections != []:
                logger.warning(f"No permission to access Airbyte workspace for store {store_id}. Skipping Airbyte cleanup.")
                # Don't attempt any deletions if we can't even list resources
                store.airbyte_source_id = None
                store.airbyte_destination_id = None
                store.airbyte_connection_id = None
                await db.commit()
                logger.info(f"Skipped Airbyte cleanup for store {store_id} due to insufficient permissions")
                return True
        except Exception as e:
            logger.warning(f"Failed to check Airbyte permissions for store {store_id}: {e}. Skipping cleanup.")
            # Don't attempt any deletions if we can't check permissions
            store.airbyte_source_id = None
            store.airbyte_destination_id = None
            store.airbyte_connection_id = None
            await db.commit()
            logger.info(f"Skipped Airbyte cleanup for store {store_id} due to permission check failure")
            return True

        # Delete connection
        connection_deleted = True
        if store.airbyte_connection_id:
            if not self.delete_connection(store.airbyte_connection_id):
                logger.warning(f"Failed to delete connection {store.airbyte_connection_id} for store {store_id} - may be due to insufficient permissions")
                connection_deleted = False

        # Delete source
        source_deleted = True
        if store.airbyte_source_id:
            if not self.delete_source(store.airbyte_source_id):
                logger.warning(f"Failed to delete source {store.airbyte_source_id} for store {store_id} - may be due to insufficient permissions")
                source_deleted = False

        # Note: We don't delete the shared destination as other stores may be using it
        # The destination will remain available for other stores

        # Clear the IDs from database regardless of deletion success
        # This ensures the store is disconnected from our perspective
        store.airbyte_source_id = None
        store.airbyte_destination_id = None  # Keep destination ID for reference
        store.airbyte_connection_id = None
        await db.commit()

        if connection_deleted and source_deleted:
            logger.info(f"Successfully deregistered store {store_id} from Airbyte (kept shared destination)")
        elif connection_deleted or source_deleted:
            logger.warning(f"Partially deregistered store {store_id} from Airbyte - some resources may remain due to permission restrictions")
        else:
            logger.warning(f"Store {store_id} deregistered from database but Airbyte resources may remain due to permission restrictions")

        return success
