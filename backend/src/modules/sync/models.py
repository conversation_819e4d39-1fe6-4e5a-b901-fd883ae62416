"""
Database models for Airbyte sync system.
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime, Float,
    ForeignKey, JSON, BigInteger, UniqueConstraint, Index
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB, UUID
import uuid

from core.db.database import Base


class WebhookEvent(Base):
    """Webhook events received from Shopify."""

    __tablename__ = "webhook_events"

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    event_id = Column(String(255), nullable=False, unique=True, index=True)
    topic = Column(String(100), nullable=False, index=True)
    shop_domain = Column(String(255), nullable=False, index=True)
    store_id = Column(Integer, Foreign<PERSON>ey("stores.id", ondelete="CASCADE"), nullable=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id", ondelete="SET NULL"), nullable=True)
    event_type = Column(String(50), nullable=True)  # For admin service compatibility
    payload = Column(JSONB, nullable=False)
    headers = Column(JSONB, nullable=True)
    hmac_verified = Column(Boolean, default=False, nullable=True)
    status = Column(String(20), default='pending', nullable=True, index=True)
    retry_count = Column(Integer, default=0, nullable=True)
    max_retries = Column(Integer, default=3, nullable=True)
    last_error = Column(Text, nullable=True)
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=True)

    # Relationships
    store = relationship("Store", back_populates="webhook_events")

    # Indexes
    __table_args__ = (
        Index('idx_webhook_events_shop_topic', 'shop_domain', 'topic'),
        Index('idx_webhook_events_created_at', 'created_at'),
    )


class SyncCheckpoint(Base):
    """Sync checkpoints for tracking sync progress per store and entity."""

    __tablename__ = "sync_checkpoints"

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    store_id = Column(Integer, ForeignKey("stores.id", ondelete="CASCADE"), nullable=False)
    entity_type = Column(String(50), nullable=False)  # 'products', 'variants', 'images'
    last_updated_at = Column(DateTime(timezone=True), nullable=True)
    last_id = Column(String(255), nullable=True)
    airbyte_state = Column(JSONB, nullable=True)  # Store Airbyte connector state
    total_records = Column(Integer, default=0, nullable=True)
    last_sync_status = Column(String(20), default='pending', nullable=True)
    last_error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=True)
    last_successful_sync_at = Column(DateTime(timezone=True), nullable=True)
    airbyte_last_sync_at = Column(DateTime(timezone=True), nullable=True)  # When Airbyte last synced this entity

    # Comprehensive end-to-end sync tracking
    airbyte_sync_started_at = Column(DateTime(timezone=True), nullable=True)  # When Airbyte sync started
    airbyte_sync_finished_at = Column(DateTime(timezone=True), nullable=True)  # When Airbyte sync finished
    local_sync_started_at = Column(DateTime(timezone=True), nullable=True)  # When local processing started
    local_sync_finished_at = Column(DateTime(timezone=True), nullable=True)  # When local processing finished
    sync_duration_seconds = Column(Float, nullable=True)  # Total sync duration
    airbyte_job_id = Column(String(255), nullable=True)  # Airbyte job ID
    sync_trigger_type = Column(String(20), nullable=True)  # 'manual' or 'scheduled'
    current_sync_stage = Column(String(30), nullable=True)  # 'airbyte_sync', 'local_processing', 'completed', 'failed'
    records_processed_in_sync = Column(Integer, default=0, nullable=True)  # Records processed in current/last sync

    # Source Database Statistics
    source_table_name = Column(String(100), nullable=True)  # Source table name (e.g., 'products', 'metafield_product_variants')
    source_database_name = Column(String(100), nullable=True)  # Source database name (e.g., 'airbyte')
    source_record_count_before = Column(Integer, default=0, nullable=True)  # Records in source before sync
    source_record_count_after = Column(Integer, default=0, nullable=True)  # Records in source after sync
    source_new_records_count = Column(Integer, default=0, nullable=True)  # New records added to source
    source_updated_records_count = Column(Integer, default=0, nullable=True)  # Records updated in source
    source_deleted_records_count = Column(Integer, default=0, nullable=True)  # Records deleted from source
    source_last_updated_at = Column(DateTime(timezone=True), nullable=True)  # Max _airbyte_extracted_at in source
    source_filter_criteria = Column(JSONB, nullable=True)  # Filter criteria used (e.g., {'shop_url': 'store-name'})

    # Destination Database Statistics
    destination_table_name = Column(String(100), nullable=True)  # Destination table name (e.g., 'products', 'product_variants')
    destination_database_name = Column(String(100), nullable=True)  # Destination database name (e.g., 'ecommerce_db')
    destination_record_count_before = Column(Integer, default=0, nullable=True)  # Records in destination before sync
    destination_record_count_after = Column(Integer, default=0, nullable=True)  # Records in destination after sync
    destination_inserted_count = Column(Integer, default=0, nullable=True)  # Records inserted into destination
    destination_updated_count = Column(Integer, default=0, nullable=True)  # Records updated in destination
    destination_deleted_count = Column(Integer, default=0, nullable=True)  # Records deleted from destination
    destination_failed_count = Column(Integer, default=0, nullable=True)  # Records that failed to sync
    destination_last_updated_at = Column(DateTime(timezone=True), nullable=True)  # When destination was last updated

    # Sync Process Statistics
    sync_started_at = Column(DateTime(timezone=True), nullable=True)  # Overall sync start time
    sync_finished_at = Column(DateTime(timezone=True), nullable=True)  # Overall sync finish time
    batch_count = Column(Integer, default=0, nullable=True)  # Total number of batches processed
    average_batch_size = Column(Float, nullable=True)  # Average records per batch
    max_batch_size = Column(Integer, default=0, nullable=True)  # Largest batch size
    min_batch_size = Column(Integer, default=0, nullable=True)  # Smallest batch size
    total_batches_processed = Column(Integer, default=0, nullable=True)  # Batches successfully processed
    batches_with_errors = Column(Integer, default=0, nullable=True)  # Batches that had errors
    retry_count = Column(Integer, default=0, nullable=True)  # Number of retries attempted
    max_retries = Column(Integer, default=3, nullable=True)  # Maximum retries allowed

    # Performance Statistics
    records_per_second = Column(Float, nullable=True)  # Processing speed
    average_batch_processing_time = Column(Float, nullable=True)  # Average time per batch in seconds
    max_batch_processing_time = Column(Float, nullable=True)  # Longest batch processing time
    min_batch_processing_time = Column(Float, nullable=True)  # Shortest batch processing time
    memory_usage_peak = Column(Float, nullable=True)  # Peak memory usage in MB
    database_connection_count = Column(Integer, default=0, nullable=True)  # Number of DB connections used

    # Error Statistics
    error_count_total = Column(Integer, default=0, nullable=True)  # Total errors encountered
    error_count_by_type = Column(JSONB, nullable=True)  # Errors categorized by type
    last_error_at = Column(DateTime(timezone=True), nullable=True)  # When last error occurred
    consecutive_error_count = Column(Integer, default=0, nullable=True)  # Consecutive sync failures
    error_rate_percentage = Column(Float, nullable=True)  # Percentage of records that failed

    # Data Quality Statistics
    duplicate_records_found = Column(Integer, default=0, nullable=True)  # Duplicate records detected
    invalid_records_skipped = Column(Integer, default=0, nullable=True)  # Records skipped due to validation
    null_values_count = Column(Integer, default=0, nullable=True)  # Null values encountered
    data_transformation_errors = Column(Integer, default=0, nullable=True)  # Transformation failures

    # Batch-level Statistics (JSON array of batch details)
    batch_statistics = Column(JSONB, nullable=True)  # Detailed stats for each batch

    # Metadata and Versioning
    sync_version = Column(String(50), nullable=True)  # Version of sync software
    platform_version = Column(String(50), nullable=True)  # Platform API version
    airbyte_version = Column(String(50), nullable=True)  # Airbyte version used
    configuration_hash = Column(String(255), nullable=True)  # Hash of sync configuration
    environment_info = Column(JSONB, nullable=True)  # Environment details (OS, Python version, etc.)

    # Relationships
    store = relationship("Store", back_populates="sync_checkpoints")

    # Constraints
    __table_args__ = (
        UniqueConstraint('store_id', 'entity_type', name='uq_sync_checkpoints_store_entity'),
        Index('idx_sync_checkpoints_store_entity', 'store_id', 'entity_type'),
        Index('idx_sync_checkpoints_updated_at', 'last_updated_at'),
    )


class SyncJob(Base):
    """Sync jobs for tracking Airbyte sync operations."""

    __tablename__ = "sync_jobs"

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    store_id = Column(Integer, ForeignKey("stores.id", ondelete="CASCADE"), nullable=False)
    entity_type = Column(String(50), nullable=False)  # 'products', 'variants', 'images'
    job_type = Column(String(20), default='incremental', nullable=False)  # 'incremental', 'full_refresh'
    status = Column(String(20), default='pending', nullable=False, index=True)
    triggered_by = Column(String(50), nullable=True)  # 'webhook', 'manual', 'schedule'
    airbyte_job_id = Column(BigInteger, nullable=True, index=True)
    airbyte_connection_id = Column(String(255), nullable=True)
    started_at = Column(DateTime(timezone=True), nullable=True)
    finished_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Float, nullable=True)
    records_processed = Column(Integer, default=0, nullable=True)
    records_failed = Column(Integer, default=0, nullable=True)
    retry_count = Column(Integer, default=0, nullable=True)
    max_retries = Column(Integer, default=3, nullable=True)
    error_message = Column(Text, nullable=True)
    error_details = Column(JSONB, nullable=True)
    celery_task_id = Column(String(255), nullable=True, index=True)
    job_metadata = Column(JSONB, nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=True)

    # Relationships
    store = relationship("Store", back_populates="sync_jobs")

    # Indexes
    __table_args__ = (
        Index('idx_sync_jobs_store_entity', 'store_id', 'entity_type'),
        Index('idx_sync_jobs_created_at', 'created_at'),
    )


class DeadLetterQueue(Base):
    """Dead letter queue for failed operations."""

    __tablename__ = "dead_letter_queue"

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    source_type = Column(String(50), nullable=False, index=True)  # 'webhook', 'sync_job', 'consumer'
    source_id = Column(String(255), nullable=False)
    entity_type = Column(String(50), nullable=True)
    store_id = Column(Integer, ForeignKey("stores.id", ondelete="SET NULL"), nullable=True)
    original_payload = Column(JSONB, nullable=False)
    failure_reason = Column(String(100), nullable=False)
    error_message = Column(Text, nullable=False)
    error_details = Column(JSONB, nullable=True)
    retry_count = Column(Integer, default=0, nullable=True)
    last_retry_at = Column(DateTime(timezone=True), nullable=True)
    resolved = Column(Boolean, default=False, nullable=True, index=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=True)

    # Relationships
    store = relationship("Store", back_populates="dead_letter_items")

    # Indexes
    __table_args__ = (
        Index('idx_dlq_store_id', 'store_id'),
        Index('idx_dlq_created_at', 'created_at'),
    )


# Import Store model to resolve relationships
# This import is placed at the bottom to avoid circular imports
try:
    from modules.stores.models import Store
except ImportError:
    # Handle case where stores.models is not yet imported
    pass

