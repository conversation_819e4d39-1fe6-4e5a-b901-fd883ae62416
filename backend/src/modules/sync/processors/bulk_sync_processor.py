"""
Bulk sync processor factory - Creates platform-specific processors for data synchronization.

This provides a clean, modular architecture for handling different e-commerce platforms
with their specific sync requirements and data transformations.
"""

import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from modules.stores.models import Store
from ..platforms.shopify.shopify_processor import ShopifyProcessor

logger = logging.getLogger(__name__)


class BulkSyncProcessor:
    """
    Factory class for creating platform-specific sync processors.

    This replaces the monolithic sync processor with a modular architecture that:
    - Supports multiple e-commerce platforms (Shopify, WooCommerce, etc.)
    - Provides platform-specific data transformations
    - Enables easy addition of new platforms and entity types
    - Maintains clean separation of concerns
    """

    def __init__(self, airbyte_engine=None):
        self.airbyte_engine = airbyte_engine

    async def sync_all_entities(
        self,
        db: AsyncSession,
        store_id: Optional[str] = None,
        entity_types: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Sync all specified entity types for a store using the appropriate platform processor.

        Args:
            db: Database session
            store_id: Store ID to sync (optional - if None, syncs all stores found in data)
            entity_types: List of entity types to sync (optional)

        Returns:
            Sync results summary
        """
        # Get store information to determine platform
        if store_id:
            result = await db.execute(select(Store).filter(Store.external_id == store_id))
            store = result.scalar_one_or_none()
            if not store:
                raise ValueError(f"Store {store_id} not found")
        else:
            # For auto-discovery, we'll need to determine platform from data
            # For now, default to Shopify
            store = None

        # Create platform-specific processor
        platform = store.platform if store else 'shopify'
        processor = self._create_platform_processor(platform, self.airbyte_engine)

        # Delegate to platform-specific processor
        # Convert external_id to integer store_id for the processor
        store_db_id = store.id if store else None
        return await processor.sync_all_entities(db, store_db_id, entity_types)

    def _create_platform_processor(self, platform: str, airbyte_engine=None):
        """Create the appropriate platform-specific processor."""
        if platform == 'shopify':
            platform_config = self._load_platform_config(platform)
            return ShopifyProcessor(platform_config)
        else:
            raise ValueError(f"Unsupported platform: {platform}")

    def _load_platform_config(self, platform: str) -> Dict[str, Any]:
        """Load platform-specific configuration."""
        import json
        import os
        from pathlib import Path

        # Get the path to the worker config relative to this module
        current_dir = Path(__file__).parent
        config_path = current_dir.parent.parent.parent / "servers" / "worker" / "config.json"

        try:
            with open(config_path, 'r') as f:
                full_config = json.load(f)
            return full_config['platforms'][platform]
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.exception(f"Could not load platform config for {platform}: {e}")
            raise