"""
API Router for Store Management
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.stores.models import Store
from modules.stores.schemas import (
    StoreCreate,
    StoreResponse,
    StoreUpdate,
    StoreConnectionTest,
    StoreConnectionTestRequest,
    SyncJobResponse,
    SyncRequest
)
from modules.sync.models import SyncCheckpoint
from modules.stores.service import store_service
from modules.auth.models import User
from modules.sync.airbyte_service import AirbyteService
from core.metrics import direct_sync_api_calls, sync_triggered_total, sync_completed_total

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=List[StoreResponse])
async def get_stores(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all stores for the current user."""
    stores = await store_service.get_by_owner(db, current_user.id)
    return stores


@router.post("/", response_model=StoreResponse)
async def create_store(
    store: StoreCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new store."""
    db_store = await store_service.create_store(db, store, current_user.id)
    return db_store


@router.get("/{store_id}", response_model=StoreResponse)
async def get_store(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific store."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")
    
    return store


@router.put("/{store_id}", response_model=StoreResponse)
async def update_store(
    store_id: str,
    store_update: StoreUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update a store."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this store")
    
    updated_store = await store_service.update(db, db_obj=store, obj_in=store_update)
    return updated_store


@router.post("/{store_id}/disconnect")
async def disconnect_store(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Disconnect a store (deregister from Airbyte, stop sync, but keep admin key for reconnection)."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to disconnect this store")

    # Disconnect from Airbyte (disable sync, keep resources)
    airbyte_service = AirbyteService()
    airbyte_disconnected = await airbyte_service.disconnect_store_from_airbyte(db, store)

    if not airbyte_disconnected:
        logger.warning(f"Failed to disconnect store {store_id} from Airbyte, but continuing with disconnect")

    # Disconnect logic: keep admin key but stop sync and mark as inactive
    update_data = {
        "is_active": False
    }
    await store_service.update(db, db_obj=store, obj_in=update_data)

    return {"message": "Store disconnected successfully"}


@router.post("/{store_id}/connect")
async def connect_store(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Connect a store (restore access and enable sync)."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to connect this store")

    # Check if store has required credentials for Airbyte setup
    if not store.admin_access_token:
        raise HTTPException(
            status_code=400,
            detail="Store access token is missing. Please reconnect using the manual setup option on the store connection page."
        )

    if not store.shop_domain:
        raise HTTPException(
            status_code=400,
            detail="Store domain is missing. Please reconnect using the manual setup option on the store connection page."
        )

    # Set store as active first
    update_data = {
        "is_active": True
    }
    await store_service.update(db, db_obj=store, obj_in=update_data)

    # Re-register with Airbyte
    airbyte_service = AirbyteService()
    try:
        airbyte_setup = await airbyte_service.setup_shop_sync(
            db, store, store.admin_access_token
        )
    except Exception as e:
        logger.exception(f"Failed to set up Airbyte sync for store {store_id}: {e}")
        # Rollback the store activation since Airbyte setup failed
        update_data = {
            "is_active": False
        }
        await store_service.update(db, db_obj=store, obj_in=update_data)
        raise HTTPException(
            status_code=500,
            detail=f"Store connection failed: {str(e)}. The store has been deactivated. Please try again or contact support."
        )

    return {"message": "Store connected successfully"}


@router.post("/{store_id}/enable-sync")
async def enable_store_sync(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Enable sync operations for a store (resume paused sync)."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to enable sync for this store")

    # Check if store has Airbyte connection
    if not store.airbyte_connection_id:
        raise HTTPException(status_code=400, detail="Store is not connected to Airbyte")

    # Enable sync via Airbyte
    airbyte_service = AirbyteService()
    sync_enabled = airbyte_service.enable_connection(store.airbyte_connection_id)

    if not sync_enabled:
        raise HTTPException(status_code=500, detail="Failed to enable sync operations")

    return {"message": "Sync operations enabled successfully"}


@router.post("/{store_id}/disable-sync")
async def disable_store_sync(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Disable sync operations for a store (pause sync without disconnecting)."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to disable sync for this store")

    # Check if store has Airbyte connection
    if not store.airbyte_connection_id:
        raise HTTPException(status_code=400, detail="Store is not connected to Airbyte")

    # Disable sync via Airbyte (pause sync operations)
    airbyte_service = AirbyteService()
    sync_disabled = airbyte_service.disable_connection(store.airbyte_connection_id)

    if not sync_disabled:
        raise HTTPException(status_code=500, detail="Failed to disable sync operations")

    return {"message": "Sync operations disabled successfully"}


@router.delete("/{store_id}")
async def delete_store(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a store."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this store")

    # Check if store has Airbyte resources that need cleanup
    has_airbyte_resources = (
        store.airbyte_connection_id or
        store.airbyte_source_id or
        store.airbyte_destination_id
    )

    if has_airbyte_resources:
        logger.info(f"Store {store_id} has Airbyte resources, attempting cleanup")

        # Full cleanup from Airbyte (delete source, destination, and connection)
        airbyte_service = AirbyteService()
        airbyte_cleanup = await airbyte_service.deregister_store_from_airbyte(db, store)

        if not airbyte_cleanup:
            logger.error(f"Failed to cleanup Airbyte resources for store {store_id}. Aborting store deletion.")
            raise HTTPException(
                status_code=500,
                detail="Failed to cleanup Airbyte resources. Store deletion aborted to prevent orphaned resources."
            )

        logger.info(f"Successfully cleaned up Airbyte resources for store {store_id}")
    else:
        logger.info(f"Store {store_id} has no Airbyte resources to cleanup")

    # Proceed with database deletion only if Airbyte cleanup succeeded (or no cleanup needed)
    await store_service.remove_by_external_id(db, external_id=store_id)
    return {"message": "Store deleted successfully"}


@router.post("/{store_id}/test-connection", response_model=StoreConnectionTest)
async def test_store_connection(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Test connection to an existing store's platform."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to test this store")

    connection_result = await store_service.test_connection(store)
    return connection_result


@router.post("/test-connection", response_model=StoreConnectionTest)
async def test_connection_by_credentials(
    request: StoreConnectionTestRequest,
    current_user: User = Depends(get_current_user),
):
    """Test connection using store credentials (for adding new stores)."""
    # Create a temporary store object for testing
    temp_store_data = StoreCreate(
        platform=request.platform,
        shop_domain=request.shop_domain,
        admin_access_token=request.admin_access_token
    )

    # Convert to store model for testing
    temp_store = Store(**temp_store_data.model_dump())
    temp_store.id = 0  # Temporary ID
    temp_store.owner_id = current_user.id

    connection_result = await store_service.test_connection(temp_store)
    return connection_result


@router.get("/platform/{platform}", response_model=List[StoreResponse])
async def get_stores_by_platform(
    platform: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all stores for a specific platform."""
    stores = await store_service.get_by_platform(db, platform)
    # Filter by ownership
    user_stores = [store for store in stores if store.owner_id == current_user.id]
    return user_stores


@router.get("/by-domain/{domain}")
async def get_store_by_domain(
    domain: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get store information by domain (for auto-populating access token)."""
    # Ensure domain has .myshopify.com suffix
    if not domain.endswith('.myshopify.com'):
        domain = f"{domain}.myshopify.com"

    # Find store by domain
    result = await db.execute(
        select(Store).where(Store.shop_domain == domain)
    )
    store = result.scalar_one_or_none()

    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")

    # Return store info (without sensitive data like tokens)
    return {
        "external_id": str(store.external_id),
        "shop_domain": store.shop_domain,
        "shop_name": store.shop_name,
        "has_access_token": bool(store.admin_access_token),
        "is_active": store.is_active
    }


@router.get("/by-domain/{domain}/token")
async def get_store_access_token(
    domain: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get access token for a store by domain (for auto-populating in frontend)."""
    # Ensure domain has .myshopify.com suffix
    if not domain.endswith('.myshopify.com'):
        domain = f"{domain}.myshopify.com"

    # Find store by domain
    result = await db.execute(
        select(Store).where(Store.shop_domain == domain)
    )
    store = result.scalar_one_or_none()

    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")

    # Return access token if available
    if not store.admin_access_token:
        raise HTTPException(status_code=404, detail="Access token not found for this store")

    return {
        "admin_access_token": store.admin_access_token,
        "shop_name": store.shop_name
    }




@router.post("/{store_id}/sync/products", response_model=SyncJobResponse)
async def sync_products(
    store_id: str,
    sync_request: SyncRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Trigger product sync for a specific store (direct Airbyte API call)."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to sync this store")

    # Check if store has required credentials
    if not store.admin_access_token or not store.shop_domain:
        raise HTTPException(status_code=400, detail="Store is not properly configured for sync")

    # Check if store has Airbyte connection
    if not store.airbyte_connection_id:
        raise HTTPException(status_code=400, detail="Store is not connected to Airbyte")

    # Validate sync mode
    if sync_request.mode not in ['full', 'incremental']:
        raise HTTPException(status_code=400, detail="Invalid sync mode. Must be 'full' or 'incremental'")

    # Record direct sync API call metric
    direct_sync_api_calls.labels(
        store_domain=store.shop_domain,
        mode=sync_request.mode,
        status='started'
    ).inc()

    # Record sync triggered metric
    sync_triggered_total.labels(
        trigger_type='api',
        entity_type='products',
        store_domain=store.shop_domain
    ).inc()

    # Directly trigger sync via Airbyte service
    airbyte_service = AirbyteService()

    try:
        job_id = await airbyte_service.trigger_sync(store.airbyte_connection_id, store.id)
        if job_id:
            mode_text = "Full" if sync_request.mode == 'full' else "Incremental"
            return SyncJobResponse(
                job_id=str(job_id),
                status="running",
                message=f"{mode_text} product sync has been started"
            )
        else:
            # Record failed sync
            direct_sync_api_calls.labels(
                store_domain=store.shop_domain,
                mode=sync_request.mode,
                status='failed'
            ).inc()
            sync_completed_total.labels(
                trigger_type='api',
                entity_type='products',
                store_domain=store.shop_domain,
                status='failed'
            ).inc()
            raise HTTPException(status_code=500, detail="Failed to start sync - no job ID returned")
    except Exception as e:
        logger.exception(f"Failed to trigger sync: {e}")
        # Record failed sync
        direct_sync_api_calls.labels(
            store_domain=store.shop_domain,
            mode=sync_request.mode,
            status='error'
        ).inc()
        sync_completed_total.labels(
            trigger_type='api',
            entity_type='products',
            store_domain=store.shop_domain,
            status='error'
        ).inc()
        raise HTTPException(status_code=500, detail=f"Failed to start sync: {str(e)}")


@router.get("/{store_id}/sync-progress", response_model=List[dict])
async def get_sync_progress(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get sync progress for a specific store."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")

    from modules.stores.progress_service import ProgressService
    progress_service = ProgressService(db)
    progress_records = await progress_service.get_all_sync_progress(store.id)

    return progress_records


@router.get("/{store_id}/sync-progress/{sync_type}", response_model=dict)
async def get_sync_progress_by_type(
    store_id: str,
    sync_type: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get the latest sync progress for a specific sync type."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")

    from modules.stores.progress_service import ProgressService
    progress_service = ProgressService(db)
    progress_record = await progress_service.get_sync_progress(store.id, sync_type)

    if not progress_record:
        # Return a default sync progress object when no record exists
        from datetime import datetime
        return {
            "id": 0,
            "store_id": str(store.external_id),  # Return external_id for API consistency
            "sync_type": sync_type,
            "status": "completed",  # Default to completed when no sync is running
            "total_items": 0,
            "processed_items": 0,
            "current_batch": 0,
            "total_batches": 0,
            "last_update": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "completed_at": None,
            "error_message": None
        }

    return progress_record.to_dict()


@router.get("/{store_id}/sync-checkpoints", response_model=List[dict])
async def get_sync_checkpoints(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get sync checkpoints for a specific store."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")

    from sqlalchemy import select
    result = await db.execute(
        select(SyncCheckpoint).where(SyncCheckpoint.store_id == store.id)
    )
    checkpoints = result.scalars().all()

    return [
        {
            "id": cp.id,
            "entity_type": cp.entity_type,
            "last_updated_at": cp.last_updated_at.isoformat() if cp.last_updated_at else None,
            "last_sync_status": cp.last_sync_status,
            "total_records": cp.total_records,
            "last_successful_sync_at": cp.last_successful_sync_at.isoformat() if cp.last_successful_sync_at else None,
            "airbyte_last_sync_at": cp.airbyte_last_sync_at.isoformat() if cp.airbyte_last_sync_at else None,
            "last_error_message": cp.last_error_message,
            # Comprehensive end-to-end sync tracking
            "airbyte_sync_started_at": cp.airbyte_sync_started_at.isoformat() if cp.airbyte_sync_started_at else None,
            "airbyte_sync_finished_at": cp.airbyte_sync_finished_at.isoformat() if cp.airbyte_sync_finished_at else None,
            "local_sync_started_at": cp.local_sync_started_at.isoformat() if cp.local_sync_started_at else None,
            "local_sync_finished_at": cp.local_sync_finished_at.isoformat() if cp.local_sync_finished_at else None,
            "sync_duration_seconds": cp.sync_duration_seconds,
            "airbyte_job_id": cp.airbyte_job_id,
            "sync_trigger_type": cp.sync_trigger_type,
            "current_sync_stage": cp.current_sync_stage,
            "records_processed_in_sync": cp.records_processed_in_sync
        }
        for cp in checkpoints
    ]


@router.get("/{store_id}/airbyte-status")
async def get_airbyte_service_status(
    store_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get real-time Airbyte service status for a specific store."""
    store = await store_service.get_by_external_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")

    airbyte_service = AirbyteService()

    # Check Airbyte service health
    service_status = {
        "airbyte_available": airbyte_service.airbyte_available,
        "service_health": "healthy" if airbyte_service.airbyte_available else "unhealthy",
        "last_checked": None,  # We'll add timestamp logic if needed
    }

    # Get connection status if store has Airbyte connection
    connection_status = None
    if store.airbyte_connection_id:
        try:
            connections = airbyte_service.get_existing_connections()
            connection = next((c for c in connections if c.get("connectionId") == store.airbyte_connection_id), None)
            if connection:
                connection_status = {
                    "connection_id": store.airbyte_connection_id,
                    "status": connection.get("status", "unknown"),
                    "name": connection.get("name", ""),
                    "source_id": connection.get("sourceId", ""),
                    "destination_id": connection.get("destinationId", ""),
                }

                # Get latest job status for this connection
                jobs = airbyte_service.get_jobs_for_connection(store.airbyte_connection_id)
                if jobs:
                    latest_job = jobs[0]  # Jobs are typically ordered by creation time
                    job_details = airbyte_service.get_job_details(latest_job.get("job", {}).get("id"))
                    if job_details:
                        connection_status["latest_job"] = {
                            "job_id": latest_job.get("job", {}).get("id"),
                            "status": latest_job.get("status", "unknown"),
                            "created_at": latest_job.get("createdAt", ""),
                            "updated_at": latest_job.get("updatedAt", ""),
                        }
        except Exception as e:
            logger.exception(f"Failed to get Airbyte connection status: {e}")
            connection_status = {
                "error": f"Failed to retrieve connection status: {str(e)}"
            }

    # Get source status if available
    source_status = None
    if store.airbyte_source_id:
        try:
            sources = airbyte_service.get_existing_sources()
            source = next((s for s in sources if s.get("sourceId") == store.airbyte_source_id), None)
            if source:
                source_status = {
                    "source_id": store.airbyte_source_id,
                    "name": source.get("name", ""),
                    "sourceDefinitionId": source.get("sourceDefinitionId", ""),
                    "connectionConfiguration": source.get("connectionConfiguration", {}),
                }
        except Exception as e:
            logger.exception(f"Failed to get Airbyte source status: {e}")
            source_status = {
                "error": f"Failed to retrieve source status: {str(e)}"
            }

    return {
        "store_id": store_id,
        "airbyte_service": service_status,
        "connection": connection_status,
        "source": source_status,
        "store_airbyte_ids": {
            "connection_id": store.airbyte_connection_id,
            "source_id": store.airbyte_source_id,
            "destination_id": store.airbyte_destination_id,
        }
    }
