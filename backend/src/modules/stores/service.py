import logging
from typing import List, Optional, Dict, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.services.base_service import BaseService
from modules.stores.models import Store
from modules.stores.schemas import StoreCreate, StoreUpdate

logger = logging.getLogger(__name__)


class StoreService(BaseService[Store, StoreCreate, StoreUpdate]):
    """Service for store operations."""

    def __init__(self):
        super().__init__(Store)

    async def get_by_owner(self, db: AsyncSession, owner_id: int) -> List[Store]:
        """Get all stores owned by a user. NOTE: Uses database ID for user relationships."""
        result = await db.execute(select(Store).where(Store.owner_id == owner_id))
        return result.scalars().all()

    async def get_by_platform(self, db: AsyncSession, platform: str) -> List[Store]:
        """Get all stores by platform."""
        result = await db.execute(select(Store).where(Store.platform == platform))
        return result.scalars().all()

    async def get_active_stores(self, db: AsyncSession) -> List[Store]:
        """Get all active stores."""
        result = await db.execute(select(Store).where(Store.is_active == True))
        return result.scalars().all()

    async def get_by_external_id(self, db: AsyncSession, external_id: str) -> Optional[Store]:
        """Get store by external ID."""
        result = await db.execute(select(Store).where(Store.external_id == external_id))
        return result.scalar_one_or_none()

    async def create_store(self, db: AsyncSession, store_create: StoreCreate, owner_id: int) -> Store:
        """Create a new store or update existing one with same domain, and automatically set up Airbyte infrastructure."""
        store_data = store_create.model_dump()
        store_data["owner_id"] = owner_id

        # Auto-populate shop_name with domain prefix if not provided or empty
        if (not store_data.get('shop_name') or store_data.get('shop_name') == '') and store_data.get('shop_domain'):
            store_data['shop_name'] = store_data['shop_domain'].replace('.myshopify.com', '')

        # Check if store with same domain already exists
        existing_store = None
        if store_create.shop_domain:
            result = await db.execute(
                select(Store).where(Store.shop_domain == store_create.shop_domain)
            )
            existing_store = result.scalar_one_or_none()
            if existing_store:
                logger.info(f"Store with domain {store_create.shop_domain} already exists (ID: {existing_store.id}). Updating instead of creating new.")

        if existing_store:
            # Update existing store
            for key, value in store_data.items():
                setattr(existing_store, key, value)
            await db.commit()
            # Note: No refresh needed - use external_id for same-session operations
            db_store = existing_store
            logger.info(f"Updated existing store {db_store.id} with domain {db_store.shop_domain}")
        else:
            # Create new store
            db_store = Store(**store_data)
            db.add(db_store)
            await db.commit()
            # Note: No refresh needed - use external_id for same-session operations
            logger.info(f"Created new store {db_store.id} with domain {db_store.shop_domain}")

        # Automatically set up Airbyte infrastructure for the store
        if db_store.platform.lower() == "shopify" and db_store.admin_access_token and db_store.shop_domain:
            try:
                from modules.sync.airbyte_service import AirbyteService
                airbyte_service = AirbyteService()

                airbyte_result = await airbyte_service.setup_shop_sync(
                    db,
                    db_store,
                    db_store.admin_access_token
                )

                if airbyte_result:
                    # Note: No refresh needed - Airbyte fields are set directly on the object
                    logger.info(f"Successfully set up Airbyte sync for store {db_store.shop_domain}: {airbyte_result}")
                else:
                    logger.warning(f"Failed to set up Airbyte sync for store {db_store.shop_domain}")

            except Exception as e:
                logger.exception(f"Error setting up Airbyte for store {db_store.shop_domain}: {e}")
                # Don't fail store creation/update if Airbyte setup fails
                pass

        return db_store

    async def test_connection(self, store: Store) -> Dict[str, Any]:
        """Test connection to a store's platform by forwarding to the appropriate plugin."""
        if store.platform.lower() == "shopify":
            from plugins.shopify.shopify_service import ShopifyGraphQLService
            from modules.stores.sync_service import StoreSyncService

            store_service = ShopifyGraphQLService(
                shop_domain=store.shop_domain,
                admin_access_token=store.admin_access_token
            )

            sync_service = StoreSyncService(store_service, store.shop_domain)
            result = await sync_service.test_connection()

            # Add logging to capture the exact response structure
            logger.debug(f"Raw Shopify connection test result: {result}")
            logger.debug(f"Result type: {type(result)}")
            logger.debug(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

            # Transform the nested Shopify response to match StoreConnectionTest schema
            admin_success = result.get("admin", {}).get("success", False)
            overall_success = result.get("overall_success", admin_success)

            # Build success message based on results
            if overall_success:
                message = "Successfully connected to Shopify Admin API"
            else:
                admin_message = result.get("admin", {}).get("message", "Admin connection failed")
                message = admin_message

            # Extract store info from admin result
            store_info = None
            if admin_success and result.get("admin", {}).get("shop_info"):
                shop_info = result["admin"]["shop_info"]
                store_info = {
                    "id": shop_info.get("id"),
                    "name": shop_info.get("name"),
                    "domain": shop_info.get("myshopifyDomain"),
                    "currency": shop_info.get("currencyCode"),
                    "admin_connected": admin_success
                }

            transformed_result = {
                "success": overall_success,
                "message": message,
                "store_info": store_info
            }

            logger.debug(f"Transformed result: {transformed_result}")
            return transformed_result
        else:
            return {
                "success": False,
                "message": f"Connection testing not implemented for platform: {store.platform}",
                "store_info": None
            }


# Create service instance
store_service = StoreService()
