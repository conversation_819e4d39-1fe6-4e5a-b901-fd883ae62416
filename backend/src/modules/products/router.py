"""
Products API Router
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from core.config import get_settings

from modules.auth.router import get_current_user
from modules.auth.models import User
from modules.products.models import Product, ProductVariant, ProductImage
from modules.products.schemas import (
    ProductCreate,
    ProductListResponse,
    ProductResponse,
    ProductUpdate,
    PaginatedProductResponse,
    ProductVariantResponse,
    ProductImageResponse
)
from modules.products.service import product_service
from modules.assets.service import asset_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=PaginatedProductResponse)
async def get_products(
    page: int = 1,
    limit: int = 50,
    search: str = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all products for the current user's stores."""
    try:
        logger.info(f"Products API called with page={page}, limit={limit}" + (f", search='{search}'" if search else ""))

        # Get user's stores
        from modules.stores.models import Store
        from sqlalchemy import select

        stores_result = await db.execute(
            select(Store).filter(Store.owner_id == current_user.id)
        )
        user_stores = stores_result.scalars().all()

        logger.info(f"User {current_user.id} has {len(user_stores)} stores")
        for store in user_stores:
            logger.info(f"Store: {store.id} - {store.shop_name or 'Unnamed Store'}")

        if not user_stores:
            # Local dev fallback: if user has no stores, include all stores
            try:
                from modules.stores.models import Store
                from sqlalchemy import select as _select
                if get_settings().ENVIRONMENT == "local":
                    stores_result_all = await db.execute(_select(Store))
                    user_stores = stores_result_all.scalars().all()
                    logger.info("Local env: no user stores; falling back to all stores for products list")
            except Exception:
                pass
            if not user_stores:
                logger.info("No stores found for user")
                return PaginatedProductResponse(
                    items=[],
                    total=0,
                    page=page,
                    limit=limit,
                    total_pages=0
                )

        store_ids = [store.id for store in user_stores]

        # Get ALL products for all user's stores with their relationships loaded
        from sqlalchemy.orm import selectinload
        from sqlalchemy import select, or_, and_

        all_products = []
        for store_id in store_ids:
            # Build the base query
            query = select(Product).options(
                selectinload(Product.variants),
                selectinload(Product.images)
            ).filter(Product.store_id == store_id)

            # Add search filter if search query is provided
            if search:
                search_filter = f"%{search.lower()}%"
                query = query.filter(
                    or_(
                        Product.title.ilike(search_filter),
                        Product.description.ilike(search_filter),
                        Product.vendor.ilike(search_filter),
                        Product.product_type.ilike(search_filter),
                        Product.tags.ilike(search_filter)
                    )
                )
                logger.debug(f"Applying search filter: {search}")

            query = query.order_by(Product.updated_at.desc())

            # Execute the query
            products_result = await db.execute(query)
            store_products = products_result.scalars().all()

            logger.debug(f"Store {store_id} has {len(store_products)} products" + (f" matching '{search}'" if search else ""))

            # Convert to dict and add computed fields
            for product in store_products:
                # Convert variants to schema instances
                variants_list = []
                if product.variants:
                    for variant in product.variants:
                        variant_response = ProductVariantResponse(
                            id=variant.id,
                            external_id=variant.external_id,
                            product_id=variant.product_id,
                            title=variant.title,
                            sku=variant.sku,
                            barcode=variant.barcode,
                            price=variant.price,
                            compare_at_price=variant.compare_at_price,
                            cost=variant.cost,
                            weight=variant.weight,
                            weight_unit=variant.weight_unit,
                            quantity=variant.quantity,
                            inventory_policy=variant.inventory_policy,
                            inventory_item_id=variant.inventory_item_id,
                            option1=variant.option1,
                            option2=variant.option2,
                            option3=variant.option3,
                            taxable=variant.taxable,
                            requires_shipping=variant.requires_shipping,
                            fulfillment_service=variant.fulfillment_service,
                            available_for_sale=variant.available_for_sale,
                            full_json=variant.full_json,
                            metafields=variant.metafields,
                            created_at=variant.created_at,
                            updated_at=variant.updated_at,
                            source_updated_at=variant.source_updated_at
                        )
                        variants_list.append(variant_response)

                # Convert images to schema instances
                images_list = []
                if product.images:
                    for image in product.images:
                        image_response = ProductImageResponse(
                            id=image.id,
                            external_id=image.external_id,
                            product_id=image.product_id,
                            variant_id=image.variant_id,
                            src=image.src,
                            alt=image.alt,
                            width=image.width,
                            height=image.height,
                            position=image.position,
                            full_json=image.full_json,
                            metafields=image.metafields,
                            created_at=image.created_at,
                            updated_at=image.updated_at,
                            source_updated_at=image.source_updated_at
                        )
                        images_list.append(image_response)

                # Create ProductListResponse instance
                product_response = ProductListResponse(
                    id=product.id,
                    external_id=product.external_id,
                    title=product.title,
                    handle=product.handle,
                    vendor=product.vendor,
                    product_type=product.product_type,
                    status=product.status,
                    published=product.published,
                    description=product.description,
                    tags=product.tags,
                    options=product.options,
                    seo=product.seo,
                    metafields=product.metafields,
                    collections=product.collections,
                    full_json=product.full_json,
                    featured_media=product.featured_media,
                    store_id=product.store_id,
                    created_at=product.created_at,
                    updated_at=product.updated_at,
                    published_at=product.published_at,
                    source_updated_at=product.source_updated_at,
                    variant_count=len(variants_list),
                    image_count=len(images_list),
                    variants=variants_list,
                    images=images_list,
                    assets=await asset_service.get_all_media_for_product(product)
                )

                # Add to products list
                all_products.append(product_response)

                # Log if no images found (for debugging)
                if product_response.image_count == 0:
                    logger.debug(f"Product {product.title}: no images found")
                if not product_response.assets:
                    logger.debug(f"Product {product.title}: no assets found")

        # Calculate total count
        total_products = len(all_products)
        logger.info(f"Total products found: {total_products}")

        # Apply pagination to the combined results
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_products = all_products[start_idx:end_idx]

        # Calculate total pages
        total_pages = (total_products + limit - 1) // limit  # Ceiling division

        logger.info(f"Returning page {page} with {len(paginated_products)} products out of {total_products} total")

        response = PaginatedProductResponse(
            items=paginated_products,
            total=total_products,
            page=page,
            limit=limit,
            total_pages=total_pages
        )

        logger.info(f"Response total field: {response.total}")
        return response

    except Exception as e:
        logger.exception(f"Error getting products: {e}")
        raise HTTPException(status_code=500, detail=str(e))



@router.get("/optimized", response_model=PaginatedProductResponse)
async def get_products_optimized(
    page: int = 1,
    limit: int = 50,
    search: str | None = None,
    vendors: str | None = None,          # CSV
    tags: str | None = None,             # CSV (match any, ILIKE)
    status: str | None = None,           # CSV
    types: str | None = None,            # CSV (product_type)
    collections: str | None = None,      # CSV (match any, ILIKE)
    media_sources: str | None = None,    # CSV: original,generated
    media_types: str | None = None,      # CSV: image,video
    min_media: int | None = None,        # minimum combined count across selected sources/types
    sort_field: str | None = None,       # product_title|created|updated|inventory|product_type|vendor
    sort_dir: str | None = None,         # asc|desc
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Optimized products list with server-side filters/sorts (offset pagination, no N+1).

    Notes about fields not in DB yet:
    - Categories and Sales channel are not dedicated columns on Product; may exist in full_json. Skipped for now.
    - Original videos are not stored; Video counts come from generated assets only.
    """
    try:
        from sqlalchemy import select, func, or_, and_, literal, case
        from sqlalchemy.orm import aliased
        from modules.stores.models import Store
        # from modules.media.models import GeneratedAsset  # TODO: Create GeneratedAsset model or use MediaJob

        logger.info(
            "Products (optimized) called",
            extra={
                "page": page,
                "limit": limit,
                "search": search,
                "vendors": vendors,
                "tags": tags,
                "status": status,
                "types": types,
                "collections": collections,
                "media_sources": media_sources,
                "media_types": media_types,
                "min_media": min_media,
                "sort_field": sort_field,
                "sort_dir": sort_dir,
            },
        )

        # Helpers to parse CSVs
        def parse_csv(val: str | None) -> list[str]:
            if not val:
                return []
            return [v.strip() for v in val.split(",") if v.strip()]

        vendors_list = parse_csv(vendors)
        tags_list = parse_csv(tags)
        status_list = parse_csv(status)
        types_list = parse_csv(types)
        collections_list = parse_csv(collections)
        media_sources_list = [v.lower() for v in parse_csv(media_sources)]  # original, generated
        media_types_list = [v.lower() for v in parse_csv(media_types)]      # image, video
        dir_desc = (sort_dir or "desc").lower() == "desc"

        # 1) Gather the user's stores and their tenant/workspace IDs
        stores_result = await db.execute(select(Store.id, Store.tenant_id).filter(Store.owner_id == current_user.id))
        rows = stores_result.all()
        store_ids = [r[0] for r in rows]
        tenant_ids = list({r[1] for r in rows if r[1] is not None})

        if not store_ids and get_settings().ENVIRONMENT == "local":
            # Local dev fallback: include all stores
            stores_result_all = await db.execute(select(Store.id, Store.tenant_id))
            rows_all = stores_result_all.all()
            store_ids = [r[0] for r in rows_all]
            tenant_ids = list({r[1] for r in rows_all if r[1] is not None})

        # If user has no stores, fall back to querying all products (dev-friendly default)
        all_products_mode = not store_ids

        # 2) Base product query with filters
        base_query = select(Product) if all_products_mode else select(Product).filter(Product.store_id.in_(store_ids))

        if search:
            like = f"%{search.lower()}%"
            base_query = base_query.filter(
                or_(
                    Product.title.ilike(like),
                    Product.description.ilike(like),
                    Product.vendor.ilike(like),
                    Product.product_type.ilike(like),
                    Product.tags.ilike(like),
                )
            )

        if vendors_list:
            lowered = [v.strip().lower() for v in vendors_list]
            base_query = base_query.filter(func.lower(func.trim(Product.vendor)).in_(lowered))
        if status_list:
            lowered = [s.strip().lower() for s in status_list]
            # Build robust status conditions:
            # - Match case/space-insensitive DB status
            # - Treat NULL status with published=True as 'active' (legacy imports)
            status_conditions = []
            if "active" in lowered:
                status_conditions.append(
                    or_(
                        func.lower(func.trim(Product.status)) == "active",
                        and_(Product.status.is_(None), Product.published.is_(True)),
                    )
                )
            if "draft" in lowered:
                status_conditions.append(
                    or_(
                        func.lower(func.trim(Product.status)) == "draft",
                        and_(Product.status.is_(None), Product.published.is_(False)),
                    )
                )
            if "archived" in lowered:
                status_conditions.append(func.lower(func.trim(Product.status)) == "archived")
            if status_conditions:
                base_query = base_query.filter(or_(*status_conditions))
        if types_list:
            lowered = [t.strip().lower() for t in types_list]
            base_query = base_query.filter(func.lower(func.trim(Product.product_type)).in_(lowered))

        if tags_list:
            ors = [Product.tags.ilike(f"%{t}%") for t in tags_list]
            base_query = base_query.filter(or_(*ors))
        if collections_list:
            lowered = [c.lower() for c in collections_list]
            ors = [func.lower(Product.collections).ilike(f"%{c}%") for c in lowered]
            base_query = base_query.filter(or_(*ors))

        # 3) Media filters using scalar subqueries (DB-level, no N+1)
        #    - Original images from ProductImage
        #    - Generated assets by type from GeneratedAsset for user's tenant workspaces
        img_count_sq = (
            select(func.count(ProductImage.id))
            .where(ProductImage.product_id == Product.id)
            .correlate(Product)
            .scalar_subquery()
        )
        # TODO: Implement generated asset counting using MediaJob model
        gen_img_count_sq = literal(0)  # Placeholder until GeneratedAsset model is created
        gen_video_count_sq = literal(0)  # Placeholder until GeneratedAsset model is created

        # Determine which components to include
        include_original = (not media_sources_list) or ("original" in media_sources_list)
        include_generated = "generated" in media_sources_list
        include_images = (not media_types_list) or ("image" in media_types_list)
        include_videos = "video" in media_types_list

        # Total media count expression based on selections
        total_media_expr = literal(0)
        if include_images and include_original:
            total_media_expr = total_media_expr + img_count_sq
        if include_images and include_generated:
            total_media_expr = total_media_expr + gen_img_count_sq
        if include_videos and include_generated:
            total_media_expr = total_media_expr + gen_video_count_sq
        # (Original videos not stored -> contribute 0)

        # Apply media presence filter when sources selected but no min_media
        if media_sources_list and min_media is None:
            # Require presence per chosen source/type
            presence_expr = literal(0)
            if include_images and include_original:
                presence_expr = presence_expr + case((img_count_sq > 0, 1), else_=0)
            if include_images and include_generated:
                presence_expr = presence_expr + case((gen_img_count_sq > 0, 1), else_=0)
            if include_videos and include_generated:
                presence_expr = presence_expr + case((gen_video_count_sq > 0, 1), else_=0)
            base_query = base_query.filter(presence_expr > 0)

        # Apply min_media threshold when provided
        if min_media is not None:
            base_query = base_query.filter(total_media_expr >= min_media)

        # 4) Sorting
        inv_sum_sq = (
            select(func.coalesce(func.sum(ProductVariant.quantity), 0))
            .where(ProductVariant.product_id == Product.id)
            .correlate(Product)
            .scalar_subquery()
        )

        sort_map = {
            "product_title": Product.title,
            "created": Product.created_at,
            "updated": Product.updated_at,
            "product_type": Product.product_type,
            "vendor": Product.vendor,
            "inventory": inv_sum_sq,
        }
        sort_col = sort_map.get((sort_field or "updated").lower(), Product.updated_at)
        if dir_desc:
            base_query = base_query.order_by(sort_col.desc(), Product.id.desc())
        else:
            base_query = base_query.order_by(sort_col.asc(), Product.id.asc())

        # 5) Total count AFTER filters
        total_count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await db.execute(total_count_query)
        total = int(total_result.scalar_one() or 0)

        # 6) Page slice
        base_query = base_query.offset((page - 1) * limit).limit(limit)
        products_result = await db.execute(base_query)
        products = products_result.scalars().all()
        if not products:
            return PaginatedProductResponse(items=[], total=total, page=page, limit=limit, total_pages=(total + limit - 1) // limit)

        product_ids = [p.id for p in products]

        # 7) Image counts for these products (single grouped query)
        image_counts_q = (
            select(ProductImage.product_id, func.count(ProductImage.id).label("cnt"))
            .where(ProductImage.product_id.in_(product_ids))
            .group_by(ProductImage.product_id)
        )
        image_counts_res = await db.execute(image_counts_q)
        image_counts = {pid: int(cnt) for pid, cnt in image_counts_res.all()}

        # 8) Images per product (window fn) - include first N images to populate cards
        from sqlalchemy import over
        rn = func.row_number().over(
            partition_by=ProductImage.product_id,
            order_by=(ProductImage.position.asc(), ProductImage.id.asc()),
        )
        ranked_images_subq = (
            select(
                ProductImage.id.label("id"),
                ProductImage.product_id.label("product_id"),
                ProductImage.external_id.label("external_id"),
                ProductImage.src.label("src"),
                ProductImage.alt.label("alt"),
                ProductImage.width.label("width"),
                ProductImage.height.label("height"),
                ProductImage.position.label("position"),
                rn.label("rn"),
            )
            .where(ProductImage.product_id.in_(product_ids))
            .subquery()
        )
        # Limit to first 8 images per product to keep payloads reasonable
        ranked_images_res = await db.execute(
            select(ranked_images_subq).where(ranked_images_subq.c.rn <= 12)
        )
        images_by_product: dict[int, List[ProductImageResponse]] = {}
        for row in ranked_images_res.all():
            lst = images_by_product.setdefault(int(row.product_id), [])
            lst.append(
                ProductImageResponse(
                    id=int(row.id),
                    external_id=row.external_id,
                    product_id=int(row.product_id),
                    variant_id=None,
                    src=row.src,
                    alt=row.alt,
                    width=int(row.width) if row.width is not None else None,
                    height=int(row.height) if row.height is not None else None,
                    position=int(row.position) if row.position is not None else 0,
                    full_json=None,
                    metafields=None,
                    created_at=None,
                    updated_at=None,
                    source_updated_at=None,
                )
            )

        # 9) Build response items
        items: List[ProductListResponse] = []
        for p in products:
            images_list: List[ProductImageResponse] = images_by_product.get(p.id, [])

            items.append(
                ProductListResponse(
                    id=p.id,
                    external_id=p.external_id,
                    title=p.title,
                    handle=p.handle,
                    vendor=p.vendor,
                    product_type=p.product_type,
                    status=p.status,
                    published=p.published,
                    description=p.description,
                    tags=p.tags,
                    options=p.options,
                    seo=p.seo,
                    metafields=p.metafields,
                    collections=p.collections,
                    full_json=p.full_json,
                    featured_media=p.featured_media,
                    store_id=p.store_id,
                    created_at=p.created_at,
                    updated_at=p.updated_at,
                    published_at=p.published_at,
                    source_updated_at=p.source_updated_at,
                    variant_count=0,
                    image_count=image_counts.get(p.id, 0),
                    images=images_list,
                    variants=None,
                    assets=None,
                )
            )

        total_pages = (total + limit - 1) // limit
        return PaginatedProductResponse(items=items, total=total, page=page, limit=limit, total_pages=total_pages)

    except Exception as e:
        logger.error(f"Error in optimized products endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/facets")
async def get_products_facets(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Return distinct facet values across all of the user's stores.

    Provides full sets for filters without requiring the client to load all pages.
    """
    try:
        from sqlalchemy import select, func, and_, text
        from modules.stores.models import Store

        # Get user's stores
        stores_result = await db.execute(
            select(Store.id).filter(Store.owner_id == current_user.id)
        )
        store_ids = [row[0] for row in stores_result.all()]
        if not store_ids and get_settings().ENVIRONMENT == "local":
            # Local dev fallback: include all stores
            stores_result_all = await db.execute(select(Store.id))
            store_ids = [row[0] for row in stores_result_all.all()]
        if not store_ids:
            return {
                "vendors": [],
                "types": [],
                "status": [],
                "tags": [],
                "collections": [],
            }

        # Vendors
        vendors_res = await db.execute(
            select(Product.vendor, func.lower(Product.vendor).label('vendor_lower'))
            .where(and_(Product.store_id.in_(store_ids), Product.vendor.isnot(None), Product.vendor != ""))
            .distinct()
            .order_by(text('vendor_lower'))
        )
        vendors = [v[0] for v in vendors_res.all() if v[0]]

        # Types (product_type)
        types_res = await db.execute(
            select(Product.product_type, func.lower(Product.product_type).label('type_lower'))
            .where(and_(Product.store_id.in_(store_ids), Product.product_type.isnot(None), Product.product_type != ""))
            .distinct()
            .order_by(text('type_lower'))
        )
        types = [t[0] for t in types_res.all() if t[0]]

        # Statuses
        status_res = await db.execute(
            select(Product.status, func.lower(Product.status).label('status_lower'))
            .where(and_(Product.store_id.in_(store_ids), Product.status.isnot(None), Product.status != ""))
            .distinct()
            .order_by(text('status_lower'))
        )
        statuses = [s[0] for s in status_res.all() if s[0]]

        # Tags: support JSON array stored as text OR comma-separated values
        tbl = Product.__tablename__
        sql = text(
            f"""
            WITH prod AS (
              SELECT tags FROM {tbl}
              WHERE store_id = ANY(:store_ids)
                AND tags IS NOT NULL AND tags <> ''
            ),
            json_tags AS (
              SELECT trim(both '"' from jt) AS tag
              FROM prod, LATERAL jsonb_array_elements_text(prod.tags::jsonb) AS jt
              WHERE prod.tags LIKE '[%'
            ),
            csv_tags AS (
              SELECT trim(both ' ' from regexp_split_to_table(tags, '\\s*,\\s*')) AS tag
              FROM prod
              WHERE prod.tags NOT LIKE '[%'
            ),
            all_tags AS (
              SELECT tag FROM json_tags
              UNION
              SELECT tag FROM csv_tags
            )
            SELECT DISTINCT tag, lower(tag) as tag_lower FROM all_tags
            WHERE tag IS NOT NULL AND tag <> ''
            ORDER BY tag_lower;
            """
        )
        tags_res = await db.execute(sql, {"store_ids": store_ids})
        tags = [r[0] for r in tags_res.all() if r[0]]

        # Collections facet: support JSON array of objects OR comma-separated names
        coll_sql = text(
            f"""
            WITH prod AS (
              SELECT collections FROM {tbl}
              WHERE store_id = ANY(:store_ids)
                AND collections IS NOT NULL AND collections <> ''
            ),
            json_colls AS (
              SELECT
                NULLIF(trim(both ' ' from obj->>'id'), '') AS id,
                NULLIF(trim(both ' ' from COALESCE(obj->>'name', obj->>'title', obj->>'handle', obj->>'id')), '') AS name
              FROM prod,
                    LATERAL jsonb_array_elements(prod.collections::jsonb) AS obj
              WHERE prod.collections LIKE '[%'
            ),
            csv_colls AS (
              SELECT NULL::text AS id,
                      NULLIF(trim(both ' ' from regexp_split_to_table(collections, '\\s*,\\s*')), '') AS name
              FROM prod
              WHERE prod.collections NOT LIKE '[%'
            ),
            all_colls AS (
              SELECT COALESCE(id, name) AS id, name FROM json_colls
              UNION
              SELECT name AS id, name FROM csv_colls
            )
            SELECT DISTINCT id, name, lower(name) as name_lower
            FROM all_colls
            WHERE name IS NOT NULL AND name <> ''
            ORDER BY name_lower;
            """
        )
        collections_res = await db.execute(coll_sql, {"store_ids": store_ids})
        collections = [{"id": str(r[0]), "name": r[1]} for r in collections_res.all() if r[1]]

        return {
            "vendors": vendors,
            "types": types,
            "status": statuses,
            "tags": tags,
            "collections": collections,
        }

    except Exception as e:
        logger.error(f"Error getting product facets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=ProductResponse)
async def create_product(
    product: ProductCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new product."""
    try:
        # Validate store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: store does not belong to user"
            )

        db_product = await product_service.create_product_with_variants(db, product)

        # Load relationships for the response
        from sqlalchemy.orm import selectinload
        from sqlalchemy import select

        result = await db.execute(
            select(Product)
            .options(
                selectinload(Product.variants),
                selectinload(Product.images)
            )
            .filter(Product.id == db_product.id)
        )
        db_product = result.scalar_one()

        # Convert variants to schema instances
        variants_list = []
        if db_product.variants:
            for variant in db_product.variants:
                variant_response = ProductVariantResponse(
                    id=variant.id,
                    external_id=variant.external_id,
                    product_id=variant.product_id,
                    title=variant.title,
                    sku=variant.sku,
                    barcode=variant.barcode,
                    price=variant.price,
                    compare_at_price=variant.compare_at_price,
                    cost=variant.cost,
                    weight=variant.weight,
                    weight_unit=variant.weight_unit,
                    quantity=variant.quantity,
                    inventory_policy=variant.inventory_policy,
                    inventory_item_id=variant.inventory_item_id,
                    option1=variant.option1,
                    option2=variant.option2,
                    option3=variant.option3,
                    taxable=variant.taxable,
                    requires_shipping=variant.requires_shipping,
                    fulfillment_service=variant.fulfillment_service,
                    available_for_sale=variant.available_for_sale,
                    full_json=variant.full_json,
                    metafields=variant.metafields,
                    created_at=variant.created_at,
                    updated_at=variant.updated_at,
                    source_updated_at=variant.source_updated_at
                )
                variants_list.append(variant_response)

        # Convert images to schema instances
        images_list = []
        if db_product.images:
            for image in db_product.images:
                image_response = ProductImageResponse(
                    id=image.id,
                    external_id=image.external_id,
                    product_id=image.product_id,
                    variant_id=image.variant_id,
                    src=image.src,
                    alt=image.alt,
                    width=image.width,
                    height=image.height,
                    position=image.position,
                    full_json=image.full_json,
                    metafields=image.metafields,
                    created_at=image.created_at,
                    updated_at=image.updated_at,
                    source_updated_at=image.source_updated_at
                )
                images_list.append(image_response)

        # Create ProductResponse instance
        product_response = ProductResponse(
            id=db_product.id,
            external_id=db_product.external_id,
            title=db_product.title,
            handle=db_product.handle,
            vendor=db_product.vendor,
            product_type=db_product.product_type,
            status=db_product.status,
            published=db_product.published,
            description=db_product.description,
            tags=db_product.tags,
            options=db_product.options,
            seo=db_product.seo,
            metafields=db_product.metafields,
            collections=db_product.collections,
            full_json=db_product.full_json,
            featured_media=db_product.featured_media,
            store_id=db_product.store_id,
            created_at=db_product.created_at,
            updated_at=db_product.updated_at,
            published_at=db_product.published_at,
            source_updated_at=db_product.source_updated_at,
            variants=variants_list,
            images=images_list,
            assets=await asset_service.get_all_media_for_product(db_product)
        )

        return product_response

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error creating product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific product."""
    try:
        logger.info(f"Getting product {product_id} for user {current_user.id}")

        product = await product_service.get_product_with_full_details_by_external_id(db, product_id)

        if not product:
            logger.warning(f"Product {product_id} not found")
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            logger.warning(f"User {current_user.id} does not have access to product {product_id} (store {product.store_id})")
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        # Debug metafields
        logger.info(f"Product {product_id} metafields: {product.metafields}")
        logger.info(f"Product {product_id} metafields type: {type(product.metafields)}")

        # Convert variants to schema instances
        variants_list = []
        if product.variants:
            for variant in product.variants:
                variant_response = ProductVariantResponse(
                    id=variant.id,
                    external_id=variant.external_id,
                    product_id=variant.product_id,
                    title=variant.title,
                    sku=variant.sku,
                    barcode=variant.barcode,
                    price=variant.price,
                    compare_at_price=variant.compare_at_price,
                    cost=variant.cost,
                    weight=variant.weight,
                    weight_unit=variant.weight_unit,
                    quantity=variant.quantity,
                    inventory_policy=variant.inventory_policy,
                    inventory_item_id=variant.inventory_item_id,
                    option1=variant.option1,
                    option2=variant.option2,
                    option3=variant.option3,
                    taxable=variant.taxable,
                    requires_shipping=variant.requires_shipping,
                    fulfillment_service=variant.fulfillment_service,
                    available_for_sale=variant.available_for_sale,
                    full_json=variant.full_json,
                    metafields=variant.metafields,
                    created_at=variant.created_at,
                    updated_at=variant.updated_at,
                    source_updated_at=variant.source_updated_at
                )
                variants_list.append(variant_response)

        # Convert images to schema instances
        images_list = []
        if product.images:
            for image in product.images:
                image_response = ProductImageResponse(
                    id=image.id,
                    external_id=image.external_id,
                    product_id=image.product_id,
                    variant_id=image.variant_id,
                    src=image.src,
                    alt=image.alt,
                    width=image.width,
                    height=image.height,
                    position=image.position,
                    full_json=image.full_json,
                    metafields=image.metafields,
                    created_at=image.created_at,
                    updated_at=image.updated_at,
                    source_updated_at=image.source_updated_at
                )
                images_list.append(image_response)

        # Create ProductResponse instance
        product_response = ProductResponse(
            id=product.id,
            external_id=product.external_id,
            title=product.title,
            handle=product.handle,
            vendor=product.vendor,
            product_type=product.product_type,
            status=product.status,
            published=product.published,
            description=product.description,
            tags=product.tags,
            options=product.options,
            seo=product.seo,
            metafields=product.metafields,
            collections=product.collections,
            full_json=product.full_json,
            featured_media=product.featured_media,
            store_id=product.store_id,
            created_at=product.created_at,
            updated_at=product.updated_at,
            published_at=product.published_at,
            source_updated_at=product.source_updated_at,
            variants=variants_list,
            images=images_list,
            assets=await asset_service.get_all_media_for_product(product)
        )

        return product_response

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: str,
    product_update: ProductUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update a product."""
    try:
        # Get existing product
        product = await product_service.get_by_external_id(db, product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        updated_product = await product_service.update(db, db_obj=product, obj_in=product_update)

        # Load relationships for the response
        from sqlalchemy.orm import selectinload
        from sqlalchemy import select

        result = await db.execute(
            select(Product)
            .options(
                selectinload(Product.variants),
                selectinload(Product.images)
            )
            .filter(Product.id == updated_product.id)
        )
        updated_product = result.scalar_one()

        # Convert variants to schema instances
        variants_list = []
        if updated_product.variants:
            for variant in updated_product.variants:
                variant_response = ProductVariantResponse(
                    id=variant.id,
                    external_id=variant.external_id,
                    product_id=variant.product_id,
                    title=variant.title,
                    sku=variant.sku,
                    barcode=variant.barcode,
                    price=variant.price,
                    compare_at_price=variant.compare_at_price,
                    cost=variant.cost,
                    weight=variant.weight,
                    weight_unit=variant.weight_unit,
                    quantity=variant.quantity,
                    inventory_policy=variant.inventory_policy,
                    inventory_item_id=variant.inventory_item_id,
                    option1=variant.option1,
                    option2=variant.option2,
                    option3=variant.option3,
                    taxable=variant.taxable,
                    requires_shipping=variant.requires_shipping,
                    fulfillment_service=variant.fulfillment_service,
                    available_for_sale=variant.available_for_sale,
                    full_json=variant.full_json,
                    metafields=variant.metafields,
                    created_at=variant.created_at,
                    updated_at=variant.updated_at,
                    source_updated_at=variant.source_updated_at
                )
                variants_list.append(variant_response)

        # Convert images to schema instances
        images_list = []
        if updated_product.images:
            for image in updated_product.images:
                image_response = ProductImageResponse(
                    id=image.id,
                    external_id=image.external_id,
                    product_id=image.product_id,
                    variant_id=image.variant_id,
                    src=image.src,
                    alt=image.alt,
                    width=image.width,
                    height=image.height,
                    position=image.position,
                    full_json=image.full_json,
                    metafields=image.metafields,
                    created_at=image.created_at,
                    updated_at=image.updated_at,
                    source_updated_at=image.source_updated_at
                )
                images_list.append(image_response)

        # Create ProductResponse instance
        product_response = ProductResponse(
            id=updated_product.id,
            external_id=updated_product.external_id,
            title=updated_product.title,
            handle=updated_product.handle,
            vendor=updated_product.vendor,
            product_type=updated_product.product_type,
            status=updated_product.status,
            published=updated_product.published,
            description=updated_product.description,
            tags=updated_product.tags,
            options=updated_product.options,
            seo=updated_product.seo,
            metafields=updated_product.metafields,
            collections=updated_product.collections,
            full_json=updated_product.full_json,
            featured_media=updated_product.featured_media,
            store_id=updated_product.store_id,
            created_at=updated_product.created_at,
            updated_at=updated_product.updated_at,
            published_at=updated_product.published_at,
            source_updated_at=updated_product.source_updated_at,
            variants=variants_list,
            images=images_list,
            assets=await asset_service.get_all_media_for_product(updated_product)
        )

        return product_response

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error updating product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{product_id}")
async def delete_product(
    product_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a product."""
    try:
        # Get existing product
        product = await product_service.get_by_external_id(db, product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        await product_service.remove(db, id=product_id)
        return {"message": "Product deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error deleting product: {e}")
        raise HTTPException(status_code=500, detail=str(e))

