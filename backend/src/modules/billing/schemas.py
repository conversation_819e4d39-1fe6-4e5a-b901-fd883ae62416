"""
Billing schemas for ProductVideo platform.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field

from core.schemas.base_schemas import BaseSchema
from .models import SubscriptionStatus, BillingPlan, UsageType


class TenantBase(BaseSchema):
    """Base tenant schema."""
    name: str = Field(..., min_length=1, max_length=255)
    slug: str = Field(..., min_length=1, max_length=100)
    billing_email: Optional[str] = Field(None, max_length=255)
    plan_tier: BillingPlan = BillingPlan.STARTER
    is_active: bool = True
    settings: Optional[Dict[str, Any]] = None


class TenantCreate(TenantBase):
    """Schema for creating a tenant."""
    pass


class TenantUpdate(BaseSchema):
    """Schema for updating a tenant."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    billing_email: Optional[str] = Field(None, max_length=255)
    plan_tier: Optional[BillingPlan] = None
    is_active: Optional[bool] = None
    settings: Optional[Dict[str, Any]] = None


class Tenant(TenantBase):
    """Full tenant schema."""
    id: int
    stripe_customer_id: Optional[str] = None
    trial_ends_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None


class SubscriptionBase(BaseSchema):
    """Base subscription schema."""
    stripe_subscription_id: str = Field(..., max_length=255)
    stripe_customer_id: str = Field(..., max_length=255)
    stripe_price_id: str = Field(..., max_length=255)
    status: SubscriptionStatus
    current_period_start: datetime
    current_period_end: datetime
    trial_start: Optional[datetime] = None
    trial_end: Optional[datetime] = None
    amount: Decimal = Field(..., decimal_places=2)
    currency: str = Field(default="usd", max_length=3)
    metadata: Optional[Dict[str, Any]] = None


class SubscriptionCreate(SubscriptionBase):
    """Schema for creating a subscription."""
    tenant_id: int


class SubscriptionUpdate(BaseSchema):
    """Schema for updating a subscription."""
    status: Optional[SubscriptionStatus] = None
    current_period_start: Optional[datetime] = None
    current_period_end: Optional[datetime] = None
    trial_start: Optional[datetime] = None
    trial_end: Optional[datetime] = None
    amount: Optional[Decimal] = None
    metadata: Optional[Dict[str, Any]] = None


class SubscriptionSchema(SubscriptionBase):
    """Full subscription schema."""
    id: int
    tenant_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None


class BillingUsageBase(BaseSchema):
    """Base billing usage schema."""
    usage_type: UsageType
    quantity: float = Field(..., ge=0)
    unit_price: Optional[Decimal] = Field(None, decimal_places=4)
    total_cost: Optional[Decimal] = Field(None, decimal_places=2)
    billing_period_start: datetime
    billing_period_end: datetime
    stripe_usage_record_id: Optional[str] = Field(None, max_length=255)
    stripe_subscription_item_id: Optional[str] = Field(None, max_length=255)
    resource_id: Optional[str] = Field(None, max_length=255)
    resource_type: Optional[str] = Field(None, max_length=50)
    metadata: Optional[Dict[str, Any]] = None


class BillingUsageCreate(BillingUsageBase):
    """Schema for creating billing usage."""
    tenant_id: int


class BillingUsageUpdate(BaseSchema):
    """Schema for updating billing usage."""
    quantity: Optional[float] = Field(None, ge=0)
    unit_price: Optional[Decimal] = Field(None, decimal_places=4)
    total_cost: Optional[Decimal] = Field(None, decimal_places=2)
    stripe_usage_record_id: Optional[str] = Field(None, max_length=255)
    metadata: Optional[Dict[str, Any]] = None


class BillingUsage(BillingUsageBase):
    """Full billing usage schema."""
    id: int
    tenant_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None


class UsageSummary(BaseSchema):
    """Usage summary for a billing period."""
    tenant_id: int
    billing_period_start: datetime
    billing_period_end: datetime
    usage_by_type: Dict[str, float]
    total_cost: Decimal
    currency: str = "usd"


class InvoiceBase(BaseSchema):
    """Base invoice schema."""
    stripe_invoice_id: str = Field(..., max_length=255)
    stripe_customer_id: str = Field(..., max_length=255)
    amount_due: Decimal = Field(..., decimal_places=2)
    amount_paid: Decimal = Field(default=0, decimal_places=2)
    currency: str = Field(default="usd", max_length=3)
    status: str = Field(..., max_length=50)
    invoice_date: datetime
    due_date: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    period_start: datetime
    period_end: datetime
    invoice_pdf_url: Optional[str] = Field(None, max_length=500)
    hosted_invoice_url: Optional[str] = Field(None, max_length=500)
    metadata: Optional[Dict[str, Any]] = None


class InvoiceCreate(InvoiceBase):
    """Schema for creating an invoice."""
    tenant_id: int


class Invoice(InvoiceBase):
    """Full invoice schema."""
    id: int
    tenant_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None


class PaymentMethodBase(BaseSchema):
    """Base payment method schema."""
    stripe_payment_method_id: str = Field(..., max_length=255)
    stripe_customer_id: str = Field(..., max_length=255)
    type: str = Field(..., max_length=50)
    is_default: bool = False
    card_brand: Optional[str] = Field(None, max_length=50)
    card_last4: Optional[str] = Field(None, max_length=4)
    card_exp_month: Optional[int] = Field(None, ge=1, le=12)
    card_exp_year: Optional[int] = Field(None, ge=2024)
    is_active: bool = True
    metadata: Optional[Dict[str, Any]] = None


class PaymentMethodCreate(PaymentMethodBase):
    """Schema for creating a payment method."""
    tenant_id: int


class PaymentMethod(PaymentMethodBase):
    """Full payment method schema."""
    id: int
    tenant_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None


# Stripe webhook schemas
class StripeWebhookEvent(BaseSchema):
    """Stripe webhook event schema."""
    id: str
    type: str
    data: Dict[str, Any]
    created: int
    livemode: bool
    pending_webhooks: int
    request: Optional[Dict[str, Any]] = None


# API request/response schemas
class CreateSubscriptionRequest(BaseSchema):
    """Request to create a subscription."""
    tenant_id: int
    price_id: str
    payment_method_id: Optional[str] = None
    trial_days: Optional[int] = None


class CreateSubscriptionResponse(BaseSchema):
    """Response from creating a subscription."""
    subscription: SubscriptionSchema
    client_secret: Optional[str] = None  # For payment confirmation
    status: str


class RecordUsageRequest(BaseSchema):
    """Request to record usage."""
    tenant_id: int
    usage_type: UsageType
    quantity: float = Field(..., ge=0)
    resource_id: Optional[str] = None
    resource_type: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class RecordUsageResponse(BaseSchema):
    """Response from recording usage."""
    usage_record: BillingUsage
    stripe_usage_record_id: Optional[str] = None


class CreditBalance(BaseSchema):
    """Credit balance information."""
    current_balance: float
    expires_at: Optional[datetime] = None
    plan_tier: str
    monthly_grant: int


class BillingDashboardData(BaseSchema):
    """Billing dashboard data."""
    tenant: Tenant
    current_subscription: Optional[SubscriptionSchema] = None
    current_usage: List[BillingUsage]
    recent_invoices: List[Invoice]
    payment_methods: List[PaymentMethod]
    usage_summary: UsageSummary
    credit_balance: Optional[CreditBalance] = None
    recent_credit_transactions: List[Dict[str, Any]] = []
