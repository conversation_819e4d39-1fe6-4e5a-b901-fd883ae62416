"""
Billing and Tenant models for ProductVideo platform.
Multi-tenant billing with Stripe integration and usage tracking.
"""

from sqlalchemy import <PERSON>umn, BigInteger, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, JSON, Enum, DECIMAL
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum as PyEnum
from decimal import Decimal
import uuid

from core.db.database import Base


class SubscriptionStatus(PyEnum):
    """Subscription status enumeration."""
    ACTIVE = "active"
    PAST_DUE = "past_due"
    CANCELED = "canceled"
    UNPAID = "unpaid"
    INCOMPLETE = "incomplete"
    INCOMPLETE_EXPIRED = "incomplete_expired"
    TRIALING = "trialing"


class BillingPlan(PyEnum):
    """Billing plan types."""
    STARTER = "starter"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"


class UsageType(PyEnum):
    """Usage tracking types."""
    VIDEO_GENERATION = "video_generation"
    STORAGE_GB = "storage_gb"
    BANDWIDTH_GB = "bandwidth_gb"
    API_CALLS = "api_calls"


class CreditTransactionType(PyEnum):
    """Credit transaction types."""
    SUBSCRIPTION_GRANT = "subscription_grant"
    PURCHASE = "purchase"
    USAGE = "usage"
    EXPIRATION = "expiration"
    ADJUSTMENT = "adjustment"


# Tenant model moved to auth/models.py to avoid duplicate table definition
# Billing fields are integrated into the main Tenant model in auth/models.py


class Subscription(Base):
    """
    Stripe subscription tracking.
    """
    __tablename__ = 'subscriptions'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    
    # Stripe data
    stripe_subscription_id = Column(String(255), unique=True, index=True, nullable=False)
    stripe_customer_id = Column(String(255), nullable=False)
    stripe_price_id = Column(String(255), nullable=False)
    
    # Subscription details
    status = Column(Enum(SubscriptionStatus), nullable=False)
    current_period_start = Column(DateTime(timezone=True), nullable=False)
    current_period_end = Column(DateTime(timezone=True), nullable=False)
    trial_start = Column(DateTime(timezone=True), nullable=True)
    trial_end = Column(DateTime(timezone=True), nullable=True)
    
    # Pricing
    amount = Column(DECIMAL(10, 2), nullable=False)  # Monthly amount in cents
    currency = Column(String(3), default="usd", nullable=False)
    
    # Metadata
    subscription_metadata = Column(JSON, nullable=True, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="subscriptions")

    def __repr__(self):
        return f"<Subscription(id={self.id}, tenant_id={self.tenant_id}, status='{self.status}')>"


class BillingUsage(Base):
    """
    Usage tracking for metered billing.
    Tracks video generations, storage, bandwidth, etc.
    """
    __tablename__ = 'billing_usage'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    
    # Usage details
    usage_type = Column(Enum(UsageType), nullable=False)
    quantity = Column(Float, nullable=False)  # Amount used
    unit_price = Column(DECIMAL(10, 4), nullable=True)  # Price per unit in cents
    total_cost = Column(DECIMAL(10, 2), nullable=True)  # Total cost in cents
    
    # Billing period
    billing_period_start = Column(DateTime(timezone=True), nullable=False)
    billing_period_end = Column(DateTime(timezone=True), nullable=False)
    
    # Stripe integration
    stripe_usage_record_id = Column(String(255), unique=True, index=True, nullable=True)
    stripe_subscription_item_id = Column(String(255), nullable=True)
    
    # Context
    resource_id = Column(String(255), nullable=True)  # Video job ID, etc.
    resource_type = Column(String(50), nullable=True)  # "video_job", "storage", etc.
    
    # Metadata
    usage_metadata = Column(JSON, nullable=True, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="usage_records")

    def __repr__(self):
        return f"<BillingUsage(id={self.id}, tenant_id={self.tenant_id}, type='{self.usage_type}', quantity={self.quantity})>"


class Invoice(Base):
    """
    Invoice tracking from Stripe.
    """
    __tablename__ = 'invoices'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    
    # Stripe data
    stripe_invoice_id = Column(String(255), unique=True, index=True, nullable=False)
    stripe_customer_id = Column(String(255), nullable=False)
    
    # Invoice details
    amount_due = Column(DECIMAL(10, 2), nullable=False)  # Amount in cents
    amount_paid = Column(DECIMAL(10, 2), default=0, nullable=False)
    currency = Column(String(3), default="usd", nullable=False)
    status = Column(String(50), nullable=False)  # draft, open, paid, void, uncollectible
    
    # Dates
    invoice_date = Column(DateTime(timezone=True), nullable=False)
    due_date = Column(DateTime(timezone=True), nullable=True)
    paid_at = Column(DateTime(timezone=True), nullable=True)
    
    # Period
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    
    # URLs
    invoice_pdf_url = Column(String(500), nullable=True)
    hosted_invoice_url = Column(String(500), nullable=True)
    
    # Metadata
    invoice_metadata = Column(JSON, nullable=True, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<Invoice(id={self.id}, tenant_id={self.tenant_id}, amount_due={self.amount_due}, status='{self.status}')>"


class PaymentMethod(Base):
    """
    Customer payment methods from Stripe.
    """
    __tablename__ = 'payment_methods'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    
    # Stripe data
    stripe_payment_method_id = Column(String(255), unique=True, index=True, nullable=False)
    stripe_customer_id = Column(String(255), nullable=False)
    
    # Payment method details
    type = Column(String(50), nullable=False)  # card, bank_account, etc.
    is_default = Column(Boolean, default=False, nullable=False)
    
    # Card details (if type is card)
    card_brand = Column(String(50), nullable=True)
    card_last4 = Column(String(4), nullable=True)
    card_exp_month = Column(Integer, nullable=True)
    card_exp_year = Column(Integer, nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Metadata
    payment_metadata = Column(JSON, nullable=True, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<PaymentMethod(id={self.id}, tenant_id={self.tenant_id}, type='{self.type}', last4='{self.card_last4}')>"


class CreditTransaction(Base):
    """
    Credit transaction tracking for purchases, usage, and grants.
    """
    __tablename__ = 'credit_transactions'

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)

    # Transaction details
    transaction_type = Column(Enum(CreditTransactionType), nullable=False)
    amount = Column(DECIMAL(10, 2), nullable=False)  # Positive for credits added, negative for credits used
    balance_after = Column(DECIMAL(10, 2), nullable=False)  # Balance after this transaction

    # Context
    description = Column(String(500), nullable=True)
    stripe_payment_id = Column(String(255), nullable=True)  # For purchases
    resource_id = Column(String(255), nullable=True)  # Media job ID, etc.
    resource_type = Column(String(50), nullable=True)  # "video_generation", "purchase", etc.

    # Expiration
    expires_at = Column(DateTime(timezone=True), nullable=True)  # When these credits expire

    # Metadata
    transaction_metadata = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    tenant = relationship("Tenant", backref="credit_transactions")

    def __repr__(self):
        return f"<CreditTransaction(id={self.id}, tenant_id={self.tenant_id}, type='{self.transaction_type}', amount={self.amount})>"
