"""Service helpers for media library asset management."""

from __future__ import annotations

import logging
from typing import Iterable, List, Optional, Tuple

from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from modules.media.models import MediaLibraryAsset

logger = logging.getLogger(__name__)


class MediaLibraryService:
    """Encapsulates CRUD helpers for media library assets."""

    VALID_COLLECTIONS = {"gallery", "models", "outfits", "scenes"}

    async def list_assets(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        collection: Optional[str] = None,
        page: int = 1,
        limit: int = 50,
    ) -> Tuple[List[MediaLibraryAsset], int]:
        """Fetch paginated assets for a user (optionally filtered by collection)."""
        query = select(MediaLibraryAsset).where(MediaLibraryAsset.user_id == user_id)
        count_query = select(func.count(MediaLibraryAsset.id)).where(
            MediaLibraryAsset.user_id == user_id
        )

        if collection:
            query = query.where(MediaLibraryAsset.collection == collection)
            count_query = count_query.where(MediaLibraryAsset.collection == collection)

        query = query.order_by(MediaLibraryAsset.created_at.desc())
        offset = (page - 1) * limit
        query = query.offset(offset).limit(limit)

        result = await db.execute(query)
        assets = result.scalars().all()

        total_result = await db.execute(count_query)
        total = total_result.scalar_one()

        return assets, total

    async def create_asset(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        tenant_id: Optional[int],
        collection: str,
        type_: str,
        file_uri: str,
        preview_uri: Optional[str],
        original_filename: Optional[str],
        content_type: Optional[str],
        size_bytes: Optional[int],
        metadata: Optional[dict] = None,
    ) -> MediaLibraryAsset:
        """Persist a new media library asset."""
        asset = MediaLibraryAsset(
            user_id=user_id,
            tenant_id=tenant_id,
            collection=collection,
            type=type_,
            file_uri=file_uri,
            preview_uri=preview_uri,
            original_filename=original_filename,
            content_type=content_type,
            size_bytes=size_bytes,
            metadata=metadata or {},
        )
        db.add(asset)
        await db.flush()
        logger.info(
            "Created media library asset", extra={"asset_id": asset.id, "collection": collection}
        )
        return asset

    async def delete_asset(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        asset_id: int,
    ) -> bool:
        """Delete an asset owned by the user; returns True when deleted."""
        result = await db.execute(
            select(MediaLibraryAsset).where(
                MediaLibraryAsset.id == asset_id,
                MediaLibraryAsset.user_id == user_id,
            )
        )
        asset = result.scalar_one_or_none()
        if not asset:
            return False
        await db.delete(asset)
        await db.flush()
        logger.info(
            "Deleted media library asset", extra={"asset_id": asset_id, "collection": asset.collection}
        )
        return True

    def validate_collection(self, collection: str) -> str:
        """Validate and normalize collection name."""
        normalized = (collection or "").strip().lower()
        if normalized not in self.VALID_COLLECTIONS:
            raise ValueError(f"Unsupported collection '{collection}'")
        return normalized


media_library_service = MediaLibraryService()
