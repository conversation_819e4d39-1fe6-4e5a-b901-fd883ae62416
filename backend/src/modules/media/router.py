"""
Media Generation API Router
"""

import logging
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from core.db.database import get_db, get_fresh_db

from modules.auth.models import User, Tenant
from modules.auth.router import get_current_user
from modules.queue.queue_service import celery_service as job_queue_service, TaskPriority
from .models import MediaJob, MediaJobStatus
from .schemas import (
    MediaGenerateRequest,
    MediaGenerateResponse,
    MediaJobStatusResponse,
    MediaPushRequest,
    MediaPushResponse,
    MediaJobListResponse,
    MediaJobInfo,
    ProviderConfig,
    ProviderConfigResponse,
)
from .service import media_service
from modules.stores.models import Store

logger = logging.getLogger(__name__)
router = APIRouter()
from modules.assets.service import asset_service
from modules.assets.schemas import PaginatedAssetResponse  # type: ignore



@router.post("/generate", response_model=MediaGenerateResponse)
async def generate_media(
    request: MediaGenerateRequest,
    db: AsyncSession = Depends(get_fresh_db),
    current_user: User = Depends(get_current_user),
):
    """
    Generate media using simplified request structure.

    Body: {media_type, provider?, item: {product_id, prompt, provider_config?, ...}, shop_id?}
    Returns: job ID for the product
    """
    try:
        # Validate request has required fields
        if not request.item:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Media item is required for media generation",
            )

        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Tenant not found")

        # Validate shop ownership if shop_id provided
        if request.shop_id:
            user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
            user_stores = user_stores_result.scalars().all()
            if not user_stores:
                raise HTTPException(status_code=404, detail="No stores found for user")

            store_ids = [store.id for store in user_stores]
            if request.shop_id not in store_ids:
                raise HTTPException(status_code=403, detail="Access denied: shop does not belong to user")

        # Create media generation job
        job = await media_service.create_generation_job(db, user_id=current_user.id, request=request)

        # Debug logging for external_id
        logger.debug(f"Job created with external_id: {job.external_id} (type: {type(job.external_id)})")

        # Ensure external_id is set (fallback in case default doesn't work)
        if job.external_id is None:
            import uuid
            job.external_id = uuid.uuid4()
            logger.debug(f"Generated new external_id: {job.external_id}")

        # Extract all needed attributes before commit to avoid lazy loading issues
        job_data = {
            "external_id": str(job.external_id),
            "product_id": str(job.product_external_id),
            "media_type": job.media_type,
            "status": job.status,
        }

        # Commit the jobs to ensure they are persisted
        db.add(job)
        await db.commit()

        # Check quota and budget before queuing tasks
        from modules.billing.quota_service import quota_service

        media_type = request.media_type.value.lower()
        total_items = 1  # Single item request

        try:
            has_quota, quota_info = await quota_service.check_quota(db, current_user.id, media_type, total_items)
            if not has_quota:
                quota_type = quota_info.get("quota_type", "unknown")

                # Handle service errors - do not proceed when quota service is unavailable
                if quota_type == "service_error":
                    logger.error(f"Quota service temporarily unavailable for user {current_user.id}")
                    raise HTTPException(
                        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                        detail="Quota service is temporarily unavailable. Please try again later.",
                    )
                else:
                    logger.error(
                        f"Quota check failed for user {current_user.id}: has_quota={has_quota}, quota_info={quota_info}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_402_PAYMENT_REQUIRED,
                        detail=f"Quota exceeded: {quota_type} limit reached",
                    )
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.exception(f"Unexpected error during quota check for user {current_user.id}: {e}")
            # For unexpected errors, allow the request to proceed but log the issue
            logger.warning(f"Proceeding with media generation due to quota service error: {e}")
            has_quota = True  # Allow request to proceed

        try:
            has_budget, budget_info = await quota_service.check_budget(db, current_user.id, media_type, total_items)
            if not has_budget:
                budget_type = budget_info.get("budget_type", "unknown")

                # Handle service errors gracefully for budget check
                if budget_type == "unknown" and "error" in budget_info:
                    logger.warning(f"Budget service temporarily unavailable for user {current_user.id}")
                    raise HTTPException(
                        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                        detail="Budget service is temporarily unavailable. Please try again later.",
                    )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_402_PAYMENT_REQUIRED, detail=f"Budget exceeded: {budget_type}"
                    )
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.exception(f"Unexpected error during budget check for user {current_user.id}: {e}")
            # For unexpected errors, allow the request to proceed but log the issue
            logger.warning(f"Proceeding with media generation due to budget service error: {e}")

        # Deduct quota and budget immediately since we've validated availability
        # This ensures billing happens even if the async worker has issues
        deduction_success = await quota_service.deduct_quota_and_budget(db, current_user.id, media_type, total_items)
        if not deduction_success:
            logger.error(f"Failed to deduct quota/budget for user {current_user.id} after validation")
            # Continue anyway since we already validated availability

        # Queue generation task using Celery
        from modules.queue.queue_service import celery_service

        # Enqueue the task using Celery
        celery_task_id = celery_service.enqueue_media_generation(
            user_id=current_user.id,
            job_id=job_data["external_id"],
            payload=request.model_dump(),
        )

        job_responses = [
            {
                "product_id": job_data["product_id"],
                "job_id": job_data["external_id"],
                "celery_task_id": celery_task_id,
                "status": job_data["status"].value,
            }
        ]

        return MediaGenerateResponse(jobs=job_responses)

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.exception(f"Error generating media: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}", response_model=MediaJobStatusResponse)
async def get_job_status(
    job_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get job status, progress, and media URLs.
    """
    try:
        logger.debug(f"API endpoint /jobs/{job_id} called by user {current_user.id}")

        job = await media_service.get_job_by_external_id(db, job_id)
        if not job:
            logger.warning(f"Job {job_id} not found in API call")
            raise HTTPException(status_code=404, detail="Job not found")

        # Add ownership validation
        # Check if job belongs to current user
        if hasattr(job, "user_id") and job.user_id != current_user.id:
            logger.warning(f"Access denied for job {job_id} - user {current_user.id} vs job owner {job.user_id}")
            raise HTTPException(status_code=403, detail="Access denied: job does not belong to user")

        logger.debug(
            f"API get_job_status for job {job_id}: status={job.status.value}"
        )

        # Create response object with variants list from job
        response = MediaJobStatusResponse(
            job_id=str(job.external_id),
            status=job.status.value,
            variants=job.variants,
            error_message=job.error_message,
        )

        logger.debug(
            f"API response object created: job_id={response.job_id}, status={response.status}"
        )

        # Convert to dict to see final output
        response_dict = response.model_dump()
        logger.debug(f"Final response dict: {response_dict}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/push", response_model=MediaPushResponse)
async def push_to_platform(
    request: MediaPushRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Push media job to connected store's product media.

    Body: {shopId, productId, jobId, publishTargets, publishOptions}
    """
    try:
        # Validate store ownership and permissions
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(status_code=404, detail="No stores found for user")

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if request.shop_id not in store_ids:
            raise HTTPException(status_code=403, detail="Access denied: store does not belong to user")

        # Find the store to determine platform
        store_result = await db.execute(
            select(Store).filter(Store.id == request.shop_id, Store.owner_id == current_user.id)
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(status_code=404, detail="Store not found")

        # Use the service method for pushing
        result = await media_service.push_media_to_platforms(
            db=db,
            job_external_id=request.job_id,  # Changed from variant_external_id to job_external_id
            user_id=current_user.id,
            shop_domain=store.shop_domain,  # Pass the shop_domain from the store object
            publish_targets=request.publish_targets,
            publish_options=request.publish_options,
        )

        platform_name = store.platform.title()
        return MediaPushResponse(
            success=result["success"],
            results=result["results"],
            push_id=f"push_{request.job_id}_{request.product_id}",  # Changed from variant_id to job_id
            status="completed" if result["success"] else "failed",
            message=f"Push to {platform_name} {'completed' if result['success'] else 'failed'}",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error pushing to platform: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs", response_model=MediaJobListResponse)
async def list_jobs(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    product_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    List media generation jobs for the current user.

    Supports filtering by status and product ID, with pagination.
    """
    try:
        # Use the service method for consistency
        result = await media_service.list_user_jobs(
            db=db,
            user_id=current_user.id,
            page=page,
            per_page=per_page,
            status_filter=status_filter,
            product_id=product_id,
        )

        # Return the jobs as dictionaries (MediaJobListResponse expects List[Dict[str, Any]])
        return MediaJobListResponse(
            jobs=result["jobs"], total=result["total"], page=result["page"], per_page=result["per_page"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list jobs: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to list jobs: {str(e)}")


@router.post("/jobs/{job_id}/cancel")
async def cancel_job(job_id: str, current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """
    Cancel a media generation job.
    """
    try:
        success = await media_service.cancel_job_by_external_id(db, job_id, current_user.id)
        if not success:
            raise HTTPException(status_code=404, detail="Job not found or cannot be cancelled")

        return {"message": "Job cancelled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error cancelling job: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/jobs/{job_id}/retry")
async def retry_job(job_id: str, current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """
    Retry a failed media generation job.
    """
    try:
        new_job = await media_service.retry_job_by_external_id(db, job_id, current_user.id)
        if not new_job:
            raise HTTPException(status_code=404, detail="Job not found or cannot be retried")

        return {
            "message": "Job retry initiated successfully",
            "new_job_id": str(new_job.external_id),  # Return external_id instead of database ID
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error retrying job: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/providers/configs", response_model=ProviderConfigResponse)
async def get_provider_configs(
    current_user: User = Depends(get_current_user),
):
    """
    Get all provider configurations and their schemas.

    Returns all available provider configurations along with their
    configuration schemas for frontend consumption.
    """
    try:
        import json
        from pathlib import Path

        # Load all provider configurations
        configs_dir = Path(__file__).parent / "providers" / "configs"
        provider_configs = {}

        for config_file in configs_dir.glob("*.json"):
            provider_name = config_file.stem
            with open(config_file, "r") as f:
                provider_config = json.load(f)

                # Extract config schema if present
                config_schema = provider_config.pop("config_schema", {})

                provider_configs[provider_name] = provider_config

        return {"provider_configs": provider_configs}

    except Exception as e:
        logger.exception(f"Error getting provider configs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/assets", response_model=PaginatedAssetResponse)
async def list_media_assets(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=200),
    search: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Back-compat: expose assets under /api/media/assets to support older frontend code.
    Maps per_page -> limit and forwards to the assets service.
    """
    try:
        limit = per_page
        assets = await asset_service.get_assets_for_user(db, current_user.id, page, limit, search)
        total_count = await asset_service.get_total_assets_count(db, current_user.id, search)
        total_pages = (total_count + limit - 1) // limit
        return PaginatedAssetResponse(
            items=assets,
            total=total_count,
            page=page,
            limit=limit,
            total_pages=total_pages,
        )
    except Exception as e:
        logger.exception(f"Error listing media assets via back-compat route: {e}")
        raise HTTPException(status_code=500, detail=str(e))
