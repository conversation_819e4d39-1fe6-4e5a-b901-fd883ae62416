"""
Media Module
Provides AI-powered media services for ProductVideo platform.
Generic module that uses provider plugins for different AI services.
"""

# Lazy imports to avoid circular dependencies
def __getattr__(name):
    if name == "media_service":
        from .service import media_service
        return media_service
    elif name == "quality_engine":
        from .engines.quality_engine import quality_engine
        return quality_engine
    elif name == "models":
        import importlib
        return importlib.import_module(".models", __name__)
    elif name == "schemas":
        import importlib
        return importlib.import_module(".schemas", __name__)
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    # Services
    "media_service",

    # Engines
    "quality_engine",

    # Modules
    "models",
    "schemas",
]
