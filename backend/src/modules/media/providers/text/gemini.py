"""
Gemini AI Provider Plugin for Text Generation.
Provides comprehensive text generation and content creation using Google's Gemini AI.
Specialized for product descriptions, marketing copy, SEO content, and social media captions.
"""

import logging
from typing import Dict, List, Optional, Any
import time

import httpx
from google import genai
from google.genai import types

from ..base import TextProvider
from ...schemas import ProviderMediaRequest, ProviderMediaResult, MediaVariant
from core.config import get_settings

logger = logging.getLogger(__name__)


class GeminiProvider(TextProvider):
    """Gemini AI provider plugin for text generation and content creation."""

    def __init__(self):
        self.client: Optional[genai.Client] = None
        self.http_client: Optional[httpx.AsyncClient] = None
        self.config: Optional[Dict[str, Any]] = None
        self.model = None  # Will be set from config during initialization

    @property
    def provider_name(self) -> str:
        return "gemini"

    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the Gemini provider."""
        # Get API key from settings
        settings = get_settings()
        api_key = settings.GEMINI_API_KEY

        if not api_key:
            logger.error("GEMINI_API_KEY not configured in settings")
            return False

        try:
            # Initialize Google GenAI client
            self.client = genai.Client(api_key=api_key)

            # Additional Gemini-specific initialization
            import httpx

            self.http_client = httpx.AsyncClient(timeout=config.get("timeout", 120))

            # Store config and get model from config - required, no fallback
            self.config = config
            self.model = config.get("model")

            logger.info(f"Initialized Gemini provider with model: {self.model}")
            return True

        except KeyError as e:
            logger.exception(f"Missing required configuration: {e}")
            return False
        except Exception as e:
            logger.exception(f"Failed to initialize Gemini provider: {e}")
            return False

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate text content using Gemini AI."""
        if not self.client or not self.config:
            return ProviderMediaResult(success=False, error_message="Provider not initialized")

        try:
            if request.media_type == "text":
                return await self._generate_text_content(request)
            else:
                return ProviderMediaResult(
                    success=False,
                    error_message=f"Unsupported media type: {request.media_type}. This provider only supports text generation.",
                )

        except Exception as e:
            logger.exception(f"Gemini text generation failed: {e}")
            return ProviderMediaResult(success=False, error_message=str(e))

    def _build_generation_config(self, request: ProviderMediaRequest) -> types.GenerateContentConfig:
        """Build generation configuration from request and provider config."""
        # Get generation parameters from provider config with safe fallbacks
        provider_config = request.item.provider_config or {}
        default_config = self.config["config_schema"]

        # Extract parameters with defaults from config schema
        temperature = provider_config.get("temperature", default_config["temperature"]["default"])
        top_p = provider_config.get("top_p", default_config["top_p"]["default"])
        top_k = provider_config.get("top_k", default_config["top_k"]["default"])
        max_output_tokens = provider_config.get("max_output_tokens", default_config["max_output_tokens"]["default"])
        candidate_count = provider_config.get("candidate_count", default_config["candidate_count"]["default"])
        stop_sequences = provider_config.get("stop_sequences", default_config["stop_sequences"]["default"])
        seed = provider_config.get("seed", default_config["seed"]["default"])
        presence_penalty = provider_config.get("presence_penalty", default_config["presence_penalty"]["default"])
        frequency_penalty = provider_config.get("frequency_penalty", default_config["frequency_penalty"]["default"])
        response_logprobs = provider_config.get("response_logprobs", default_config["response_logprobs"]["default"])
        logprobs = provider_config.get("logprobs", default_config["logprobs"]["default"])
        thinking_config = provider_config.get("thinking_config", default_config["thinking_config"]["default"])
        response_modalities = provider_config.get(
            "response_modalities", default_config["response_modalities"]["default"]
        )

        # Extract additional unused parameters
        http_options = provider_config.get("http_options", default_config["http_options"]["default"])
        if http_options is not None and isinstance(http_options, dict):
            http_options = types.HttpOptions(**http_options)
        should_return_http_response = provider_config.get(
            "should_return_http_response", default_config["should_return_http_response"]["default"]
        )
        system_instruction = provider_config.get("system_instruction", default_config["system_instruction"]["default"])
        if system_instruction is not None and isinstance(system_instruction, dict):
            system_instruction = types.Content(**system_instruction)
        response_mime_type = provider_config.get("response_mime_type", default_config["response_mime_type"]["default"])
        response_schema = provider_config.get("response_schema", default_config["response_schema"]["default"])
        response_json_schema = provider_config.get(
            "response_json_schema", default_config["response_json_schema"]["default"]
        )
        routing_config = provider_config.get("routing_config", default_config["routing_config"]["default"])
        if routing_config is not None and isinstance(routing_config, dict):
            routing_config = types.RoutingConfig(**routing_config)
        model_selection_config = provider_config.get(
            "model_selection_config", default_config["model_selection_config"]["default"]
        )
        if model_selection_config is not None and isinstance(model_selection_config, dict):
            model_selection_config = types.ModelSelectionConfig(**model_selection_config)
        safety_settings = provider_config.get("safety_settings", default_config["safety_settings"]["default"])
        if safety_settings is not None and isinstance(safety_settings, list):
            safety_settings = [types.SafetySetting(**s) for s in safety_settings if isinstance(s, dict)]
        tools = provider_config.get("tools", default_config["tools"]["default"])
        if tools is not None and isinstance(tools, list):
            tools = [types.Tool(**t) for t in tools if isinstance(t, dict)]
        tool_config = provider_config.get("tool_config", default_config["tool_config"]["default"])
        if tool_config is not None and isinstance(tool_config, dict):
            tool_config = types.ToolConfig(**tool_config)
        labels = provider_config.get("labels", default_config["labels"]["default"])
        cached_content = provider_config.get("cached_content", default_config["cached_content"]["default"])
        automatic_function_calling = provider_config.get(
            "automatic_function_calling", default_config["automatic_function_calling"]["default"]
        )
        if automatic_function_calling is not None and isinstance(automatic_function_calling, dict):
            automatic_function_calling = types.AutomaticFunctionCallingConfig(**automatic_function_calling)

        # Create GenerateContentConfig object directly
        config = types.GenerateContentConfig()

        # Set all parameters directly (defaults are None anyway)
        config.temperature = temperature
        config.top_p = top_p
        config.top_k = top_k
        config.max_output_tokens = max_output_tokens
        config.candidate_count = candidate_count
        config.response_modalities = response_modalities
        config.stop_sequences = stop_sequences
        config.seed = seed
        config.presence_penalty = presence_penalty
        config.frequency_penalty = frequency_penalty
        config.response_logprobs = response_logprobs
        config.logprobs = logprobs
        config.http_options = http_options
        config.should_return_http_response = should_return_http_response
        config.system_instruction = system_instruction
        config.response_mime_type = response_mime_type
        config.response_schema = response_schema
        config.response_json_schema = response_json_schema
        config.routing_config = routing_config
        config.model_selection_config = model_selection_config
        config.safety_settings = safety_settings
        config.tools = tools
        config.tool_config = tool_config
        config.labels = labels
        config.cached_content = cached_content
        config.automatic_function_calling = automatic_function_calling

        # Handle special cases
        if thinking_config is not None and "gemini-2.5" in self.model:
            config.thinking_config = types.ThinkingConfig(**thinking_config)

        return config

    async def _generate_text_content(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate professional e-commerce text content using Gemini."""
        try:
            start_time = time.time()
            # Use the prompt directly from the request
            prompt = request.item.prompt

            contents = [types.Content(role="user", parts=[types.Part.from_text(text=prompt)])]

            # Build generation config using the extracted method
            config = self._build_generation_config(request)

            logger.info("Using Gemini generation config from extracted method")

            response = self.client.models.generate_content(model=self.model, contents=contents, config=config)

            # Extract request ID from response
            request_id = getattr(response, "responseId", f"gemini_text_{hash(request.item.product_id)}")

            # Extract text from Gemini response
            text_variants = []
            generated_text = ""
            last_finish_reason = None
            try:
                if response.candidates:
                    for candidate in response.candidates:
                        # Capture finish reason for error reporting
                        last_finish_reason = getattr(candidate, "finish_reason", None)
                        if candidate.content and candidate.content.parts:
                            for part in candidate.content.parts:
                                if hasattr(part, "text") and part.text:
                                    generated_text += part.text
                            if not generated_text.strip():
                                generated_text = "Error: No text was generated"

                            text_variants.append(
                                MediaVariant(
                                    job_name=f"variant_{len(text_variants)+1}",
                                    type="text",
                                    text=generated_text.strip(),
                                    word_count=len(generated_text.split()),
                                    character_count=len(generated_text),
                                    prompt_used=prompt,
                                    provider="gemini",
                                    generation_metadata={
                                        "provider": "gemini",
                                        "model": self.model,
                                    },
                                )
                            )

                if len(text_variants) == 0:
                    logger.error(f"Could not extract text from Gemini response: {response}")
                    generated_text = "Error: Could not extract generated text from response"

            except Exception as e:
                logger.exception(f"Failed to extract text from Gemini response: {e}")
                generated_text = f"Error: Could not extract generated text from response: {str(e)}"

                # Build detailed error message
                error_details = []
                error_details.append("🚨 EXACT ERROR: Failed to extract text from Gemini response!")
                error_details.append(f"🔍 Exception: {str(e)}")
                if last_finish_reason:
                    error_details.append(f"🔍 Finish Reason: {last_finish_reason}")
                error_details.append(f"📝 Prompt: {prompt}")
                error_details.append(f"🤖 Model: {self.model}")
                detailed_error = "\n".join(error_details)

                return ProviderMediaResult(
                    success=False,
                    error_message=detailed_error,
                    provider_job_id=request_id,
                    completion_time=time.time() - start_time,
                )

            # Extract usage metadata if available
            usage_metadata = getattr(response, "usageMetadata", None)
            metadata = {"usageMetadata": usage_metadata} if usage_metadata else None

            logger.info(
                f"🎯 Text Generation Summary: Generated {len(generated_text.split())} words. Usage Metadata: {usage_metadata}"
            )

            return ProviderMediaResult(
                success=True,
                provider_job_id=request_id,
                variants=text_variants,
                completion_time=time.time() - start_time,
                metadata=metadata,
            )

        except Exception as e:
            logger.exception(f"Error generating text with Gemini: {e}")
            return ProviderMediaResult(success=False, error_message=str(e))


    async def cleanup(self) -> None:
        """Cleanup Gemini provider resources."""
        if self.client:
            self.client = None
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None
        logger.info("Cleaned up Gemini provider")
