"""
Example Text Provider for demonstration purposes.
Returns test text content instead of calling external APIs.
"""

import os
import base64
import json
from pathlib import Path
from typing import Dict, List, Optional, Any

from ..base import TextProvider
from ...schemas import ProviderMediaRequest, ProviderMediaResult


class ExampleTextProvider(TextProvider):
    """Example text provider that returns test text content."""

    def __init__(self):
        super().__init__()

    @property
    def provider_name(self) -> str:
        return "example_text"

    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize example provider - always succeeds."""
        self.config = config
        return True

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate example text content."""
        try:
            # Read test text files
            test_texts = []
            text_files = [
                "gemini_text_product_description_0.txt",
                "gemini_text_marketing_copy_1.txt",
                "gemini_text_seo_snippet_3.txt",
                "gemini_text_social_caption_2.txt"
            ]

            local_dir = Path(__file__).parent

            # Generate as many variants as requested, cycling through available text files
            num_variants = request.variants_count or 4
            for i in range(num_variants):
                # Cycle through available text files
                filename = text_files[i % len(text_files)]
                text_file = local_dir / filename

                if text_file.exists():
                    with open(text_file, "r", encoding="utf-8") as f:
                        content = f.read().strip()

                    # Determine content type from filename
                    if "product_description" in filename:
                        content_type = "product_description"
                    elif "marketing_copy" in filename:
                        content_type = "marketing_copy"
                    elif "seo_snippet" in filename:
                        content_type = "seo_snippet"
                    elif "social_caption" in filename:
                        content_type = "social_caption"
                    else:
                        content_type = "text"

                    test_texts.append({
                        "content_type": content_type,
                        "text": content,
                        "word_count": len(content.split()),
                        "character_count": len(content),
                        "variant_name": f"variant_{i+1}",
                        "language": "en",
                        "prompt_used": f"Example prompt for {request.product_title}"
                    })
                else:
                    # If file doesn't exist, create a fallback
                    test_texts.append({
                        "content_type": "text",
                        "text": f"This is example text content for variant {i+1} of {request.product_title}",
                        "word_count": 10,
                        "character_count": 50,
                        "variant_name": f"variant_{i+1}",
                        "language": "en",
                        "prompt_used": f"Fallback prompt for {request.product_title}"
                    })

            return ProviderMediaResult(
                success=True,
                provider_job_id="example_text_job_456",
                variants=test_texts,
                estimated_completion_time=2,
                quality_score=0.95
            )

        except Exception as e:
            return ProviderMediaResult(
                success=False,
                error_message=f"Example text generation failed: {str(e)}"
            )