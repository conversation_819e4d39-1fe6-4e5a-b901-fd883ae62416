"""
Simple Media Provider Manager.
Direct access to providers by type with auto-initialization.
"""

import asyncio
import json
import logging
import os
from pathlib import Path
from typing import Dict, Optional, Any

from .base import BaseMediaProvider
from core.config import get_settings

# Direct imports of all provider classes
from .image.banana import Banana<PERSON>rovider
from .image.example_image import Example<PERSON><PERSON>Provider
from .video.veo3 import Veo3Provider
from .video.example_video import ExampleVideoProvider
from .text.gemini import GeminiProvider
from .text.example_text import ExampleTextProvider


# Get settings instance for environment variables
settings = get_settings()

logger = logging.getLogger(__name__)
# Provider override mappings for quick access
PROVIDER_OVERRIDES = {
    "image": settings.IMAGE_PROVIDER_OVERRIDE,
    "video": settings.VIDEO_PROVIDER_OVERRIDE,
    "text": settings.TEXT_PROVIDER_OVERRIDE,
}


def load_provider_config(provider_name: str) -> Dict[str, Any]:
    """Load configuration for a specific provider from its individual JSON file."""
    config_path = Path(__file__).parent / "configs" / f"{provider_name}.json"
    try:
        with open(config_path, "r") as f:
            config = json.load(f)

        return config
    except FileNotFoundError:
        logger.error(f"Configuration file not found for provider: {provider_name}")
        return {}


def _check_provider_override(media_type: str, requested_provider: str) -> str:
    """Check if there's a provider override for the given media type."""
    # Check environment variables directly for dynamic override checking
    env_var_name = f"{media_type.upper()}_PROVIDER_OVERRIDE"
    override_provider = os.environ.get(env_var_name)

    if override_provider:
        logger.info(f"🔧 Using {env_var_name}: {override_provider} for {media_type} generation")
        return override_provider

    return requested_provider

# Simple provider maps by type - initialized on first access
_text_providers: Dict[str, BaseMediaProvider] = {}
_image_providers: Dict[str, BaseMediaProvider] = {}
_video_providers: Dict[str, BaseMediaProvider] = {}

# Provider class mappings
TEXT_PROVIDER_CLASSES = {
    "gemini": GeminiProvider,
    "example_text": ExampleTextProvider,
}

IMAGE_PROVIDER_CLASSES = {
    "banana": BananaProvider,
    "example_image": ExampleImageProvider,
}

VIDEO_PROVIDER_CLASSES = {
    "veo3": Veo3Provider,
    "example_video": ExampleVideoProvider,
}


async def _initialize_provider(provider_name: str, provider_class: type) -> Optional[BaseMediaProvider]:
    """Initialize a single provider with its configuration and optional overrides."""
    try:
        # Get config for this provider
        provider_config_data = load_provider_config(provider_name)
        if not provider_config_data:
            logger.warning(f"No configuration found for provider: {provider_name}")
            return None

        # Create and initialize provider instance with raw config dict
        provider = provider_class()
        success = await provider.initialize(provider_config_data)

        if success:
            # Set storage service for direct uploads
            try:
                # Lazy import to avoid circular imports
                from ...storage.storage_service import media_storage_service

                provider.set_storage_service(media_storage_service)
            except Exception as e:
                logger.warning(f"Failed to set storage service for {provider_name}: {e}")
            logger.info(f"Successfully initialized provider: {provider_name}")
            return provider
        else:
            logger.error(f"Failed to initialize provider: {provider_name}")
            return None

    except Exception as e:
        logger.exception(f"Error initializing provider {provider_name}: {e}")
        return None


async def get_text_provider(provider_name: str) -> BaseMediaProvider:
    """Get a text provider by name, initializing if needed. Checks for overrides."""
    # Check for provider override
    resolved_provider_name = _check_provider_override("text", provider_name)

    if resolved_provider_name not in _text_providers:
        provider_class = TEXT_PROVIDER_CLASSES.get(resolved_provider_name)
        if not provider_class:
            available = list(TEXT_PROVIDER_CLASSES.keys())
            raise ValueError(f"Text provider '{resolved_provider_name}' not found. Available: {available}")

        provider = await _initialize_provider(resolved_provider_name, provider_class)
        if not provider:
            raise RuntimeError(f"Failed to initialize text provider: {resolved_provider_name}")

        _text_providers[resolved_provider_name] = provider

    return _text_providers[resolved_provider_name]


async def get_image_provider(
    provider_name: str
) -> BaseMediaProvider:
    """Get an image provider by name, initializing if needed. Checks for overrides."""
    # Check for provider override
    resolved_provider_name = _check_provider_override("image", provider_name)

    if resolved_provider_name not in _image_providers:
        provider_class = IMAGE_PROVIDER_CLASSES.get(resolved_provider_name)
        if not provider_class:
            available = list(IMAGE_PROVIDER_CLASSES.keys())
            raise ValueError(f"Image provider '{resolved_provider_name}' not found. Available: {available}")

        provider = await _initialize_provider(resolved_provider_name, provider_class)
        if not provider:
            raise RuntimeError(f"Failed to initialize image provider: {resolved_provider_name}")

        _image_providers[resolved_provider_name] = provider

    return _image_providers[resolved_provider_name]


async def get_video_provider(
    provider_name: str
) -> BaseMediaProvider:
    """Get a video provider by name, initializing if needed. Checks for overrides."""
    # Check for provider override
    resolved_provider_name = _check_provider_override("video", provider_name)
    logger.info(
        f"PROVIDER_RESOLUTION: get_video_provider - requested: {provider_name}, resolved: {resolved_provider_name}"
    )

    if resolved_provider_name not in _video_providers:
        provider_class = VIDEO_PROVIDER_CLASSES.get(resolved_provider_name)
        if not provider_class:
            available = list(VIDEO_PROVIDER_CLASSES.keys())
            raise ValueError(f"Video provider '{resolved_provider_name}' not found. Available: {available}")

        provider = await _initialize_provider(resolved_provider_name, provider_class)
        if not provider:
            raise RuntimeError(f"Failed to initialize video provider: {resolved_provider_name}")

        _video_providers[resolved_provider_name] = provider

    return _video_providers[resolved_provider_name]


async def get_provider(provider_name: str, media_type: str) -> BaseMediaProvider:
    """Get any provider by name and media type."""
    if media_type == "text":
        return await get_text_provider(provider_name)
    elif media_type == "image":
        return await get_image_provider(provider_name)
    elif media_type == "video":
        return await get_video_provider(provider_name)
    else:
        raise ValueError(f"Unsupported media type: {media_type}")
