"""
Media Providers
Simple AI provider access for media generation.
"""

from .manager import (
    get_text_provider,
    get_image_provider,
    get_video_provider,
    get_provider,
)
from .base import (
    BaseMediaProvider,
    ImageProvider,
    TextProvider,
    VideoProvider
)

__all__ = [
    # New simple interface
    "get_text_provider",
    "get_image_provider",
    "get_video_provider",
    "get_provider",
    # Base classes
    "BaseMediaProvider",
    "ImageProvider",
    "TextProvider",
    "VideoProvider",
]