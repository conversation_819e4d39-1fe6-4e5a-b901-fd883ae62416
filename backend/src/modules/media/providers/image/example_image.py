"""
Example Image Provider for demonstration purposes.
Returns test images instead of calling external APIs.
"""

import os
import base64
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

from ..base import ImageProvider
from ...schemas import ProviderMediaRequest, ProviderMediaResult

logger = logging.getLogger(__name__)


class ExampleImageProvider(ImageProvider):
    """Example image provider that returns test images."""

    def __init__(self):
        super().__init__()

    @property
    def provider_name(self) -> str:
        return "example_image"

    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize example provider - always succeeds."""
        self.config = config
        return True

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate example images."""
        try:
            logger.debug(f"ExampleImageProvider.generate_media called with request: media_type={request.media_type}, product_title={request.product_title}")

            # Read test images from local directory
            test_images = []
            local_dir = Path(__file__).parent
            logger.debug(f"Local directory: {local_dir}")

            # Generate as many variants as requested (up to available test images)
            num_variants = request.num_images or 4
            available_images = [f"banana_image_{i}.png" for i in range(2)]  # We have 2 test images
            logger.debug(f"Generating {num_variants} variants from {len(available_images)} available images")

            for i in range(num_variants):
                # Cycle through available test images if we need more than available
                image_filename = available_images[i % len(available_images)]
                image_file = local_dir / image_filename
                logger.debug(f"Processing variant {i+1}: {image_filename}, exists: {image_file.exists()}")

                if image_file.exists():
                    # Read image file
                    with open(image_file, "rb") as f:
                        image_data = f.read()
                    logger.debug(f"Read {len(image_data)} bytes from {image_filename}")

                    try:
                        # Upload to storage
                        filename = f"example_image_{i}.png"
                        logger.debug(f"Uploading to storage: filename={filename}, shop_id={request.shop_id}")

                        image_url = await self._upload_media_to_storage(
                            media_content=image_data,
                            filename=filename,
                            content_type="image/png",
                            shop_id=request.shop_id,
                            metadata={
                                "provider": "example_image",
                                "model": "example_model",
                                "style_type": "example"
                            }
                        )
                        logger.debug(f"Upload successful, URL: {image_url}")

                        # For now, use same URL for thumbnail
                        thumbnail_url = image_url

                        variant_data = {
                            "image_url": image_url,
                            "thumbnail_url": thumbnail_url,
                            "width": 1024,
                            "height": 1024,
                            "style": "example_style",
                            "variant_name": f"variant_{i+1}",
                            "prompt_used": f"Example prompt for {request.product_title}",
                            "generation_metadata": {
                                "provider": "example_image",
                                "model": "example_model",
                                "style_type": "example",
                                "target_audience": ["example"],
                                "usage_context": ["example"]
                            }
                        }
                        test_images.append(variant_data)
                        logger.debug(f"Added variant {i+1} to results")

                    except Exception as upload_error:
                        logger.exception(f"Failed to upload example image to storage: {upload_error}")
                        continue
                else:
                    logger.warning(f"Test image file not found: {image_file}")
                    continue

            logger.debug(f"Generated {len(test_images)} images successfully")
            return ProviderMediaResult(
                success=True,
                provider_job_id="example_image_job_123",
                variants=test_images,
                estimated_completion_time=5,
                quality_score=0.9
            )

        except Exception as e:
            logger.exception(f"Example image generation failed: {str(e)}")
            return ProviderMediaResult(
                success=False,
                error_message=f"Example image generation failed: {str(e)}"
            )