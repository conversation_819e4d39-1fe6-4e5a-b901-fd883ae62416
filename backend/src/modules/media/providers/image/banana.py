"""
Google Gemini Image Provider Plugin for E-commerce Media Generation.
Provides professional product image generation using Google's Gemini 2.5 Flash Image Preview.
Specialized for e-commerce product photography, lifestyle images, and promotional content.
"""

import asyncio
import base64
import logging
import mimetypes
import os
import time
from typing import Dict, List, Optional, Any
from io import BytesIO

import httpx

from google import genai
from google.genai import types
from PIL import Image

from ..base import ImageProvider
from core.config import get_settings
from ...schemas import ProviderMediaRequest, ProviderMediaResult, MediaVariant

logger = logging.getLogger(__name__)


class BananaProvider(ImageProvider):
    """Google Gemini provider plugin for image generation."""

    def __init__(self):
        self.client: Optional[genai.Client] = None
        self.config: Optional[Dict[str, Any]] = None
        self.model = None  # Will be set from config during initialization

    @property
    def provider_name(self) -> str:
        return "banana"

    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the Banana provider."""
        # Get API key from settings
        settings = get_settings()
        api_key = settings.BANANA_API_KEY

        if not api_key:
            logger.error("BANANA_API_KEY not configured in settings")
            return False

        try:
            # Initialize Google GenAI client
            self.client = genai.Client(api_key=api_key)

            # Store config and get model from config - required, no fallback
            self.config = config
            self.model = config.get("model")

            logger.info(f"Initialized Banana provider with model: {self.model}")
            return True

        except Exception as e:
            logger.exception(f"Failed to initialize Banana provider: {e}")
            return False

    def _build_generation_config(self, request: ProviderMediaRequest) -> types.GenerateContentConfig:
        """Build generation configuration from request and provider config."""
        # Get generation parameters from provider config with safe fallbacks
        provider_config = request.item.provider_config or {}
        default_config = self.config["config_schema"]

        # Extract parameters with defaults from config schema
        temperature = provider_config.get("temperature", default_config["temperature"]["default"])
        top_p = provider_config.get("top_p", default_config["top_p"]["default"])
        top_k = provider_config.get("top_k", default_config["top_k"]["default"])
        max_output_tokens = provider_config.get("max_output_tokens", default_config["max_output_tokens"]["default"])
        candidate_count = provider_config.get("candidate_count", default_config["candidate_count"]["default"])
        stop_sequences = provider_config.get("stop_sequences", default_config["stop_sequences"]["default"])
        seed = provider_config.get("seed", default_config["seed"]["default"])
        response_modalities = provider_config.get(
            "response_modalities", default_config["response_modalities"]["default"]
        )
        presence_penalty = provider_config.get("presence_penalty", default_config["presence_penalty"]["default"])
        frequency_penalty = provider_config.get("frequency_penalty", default_config["frequency_penalty"]["default"])
        response_logprobs = provider_config.get("response_logprobs", default_config["response_logprobs"]["default"])
        logprobs = provider_config.get("logprobs", default_config["logprobs"]["default"])
        thinking_config = provider_config.get("thinking_config", default_config["thinking_config"]["default"])

        # Extract additional unused parameters
        http_options = provider_config.get("http_options", default_config["http_options"]["default"])
        if http_options is not None and isinstance(http_options, dict):
            http_options = types.HttpOptions(**http_options)
        should_return_http_response = provider_config.get(
            "should_return_http_response", default_config["should_return_http_response"]["default"]
        )
        system_instruction = provider_config.get("system_instruction", default_config["system_instruction"]["default"])
        if system_instruction is not None and isinstance(system_instruction, dict):
            system_instruction = types.Content(**system_instruction)
        response_mime_type = provider_config.get("response_mime_type", default_config["response_mime_type"]["default"])
        response_schema = provider_config.get("response_schema", default_config["response_schema"]["default"])
        response_json_schema = provider_config.get(
            "response_json_schema", default_config["response_json_schema"]["default"]
        )
        routing_config = provider_config.get("routing_config", default_config["routing_config"]["default"])
        if routing_config is not None and isinstance(routing_config, dict):
            routing_config = types.RoutingConfig(**routing_config)
        model_selection_config = provider_config.get(
            "model_selection_config", default_config["model_selection_config"]["default"]
        )
        if model_selection_config is not None and isinstance(model_selection_config, dict):
            model_selection_config = types.ModelSelectionConfig(**model_selection_config)
        safety_settings = provider_config.get("safety_settings", default_config["safety_settings"]["default"])
        if safety_settings is not None and isinstance(safety_settings, list):
            safety_settings = [types.SafetySetting(**s) for s in safety_settings if isinstance(s, dict)]
        tools = provider_config.get("tools", default_config["tools"]["default"])
        if tools is not None and isinstance(tools, list):
            tools = [types.Tool(**t) for t in tools if isinstance(t, dict)]
        tool_config = provider_config.get("tool_config", default_config["tool_config"]["default"])
        if tool_config is not None and isinstance(tool_config, dict):
            tool_config = types.ToolConfig(**tool_config)
        labels = provider_config.get("labels", default_config["labels"]["default"])
        cached_content = provider_config.get("cached_content", default_config["cached_content"]["default"])
        # audio_timestamp = provider_config.get("audio_timestamp", default_config["audio_timestamp"]["default"])

        # Create GenerateContentConfig object directly
        config = types.GenerateContentConfig()

        # Set all parameters directly (defaults are None anyway)
        config.temperature = temperature
        config.top_p = top_p
        config.top_k = top_k
        config.max_output_tokens = max_output_tokens
        config.candidate_count = candidate_count
        config.response_modalities = response_modalities
        config.presence_penalty = presence_penalty
        config.frequency_penalty = frequency_penalty
        config.response_logprobs = response_logprobs
        config.stop_sequences = stop_sequences
        config.seed = seed
        config.logprobs = logprobs
        config.http_options = http_options
        config.should_return_http_response = should_return_http_response
        config.system_instruction = system_instruction
        config.response_mime_type = response_mime_type
        config.response_schema = response_schema
        config.response_json_schema = response_json_schema
        config.routing_config = routing_config
        config.model_selection_config = model_selection_config
        config.safety_settings = safety_settings
        config.tools = tools
        config.tool_config = tool_config
        config.labels = labels
        config.cached_content = cached_content
        # config.audio_timestamp = audio_timestamp # Not supported

        # Handle special cases
        if thinking_config is not None and "gemini-2.5" in self.model:
            config.thinking_config = types.ThinkingConfig(**thinking_config)

        return config

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate professional e-commerce images using Google Gemini."""
        if not self.client or not self.config:
            return ProviderMediaResult(success=False, error_message="Provider not initialized")


        try:
            start_time = time.time()
            prompt = request.item.prompt

            # Generate images for each prompt variant
            all_images = []
            last_finish_reason = None
            last_response_text = None

            # Create content parts
            content_parts = []

            # Add reference images first (if provided)
            if request.item.reference_images:
                for image_url in request.item.reference_images:
                    try:
                        # Download the image
                        async with httpx.AsyncClient() as client:
                            response = await client.get(image_url)
                            response.raise_for_status()
                            image_bytes = response.content

                        # Determine mime type
                        mime_type, _ = mimetypes.guess_type(image_url)
                        if not mime_type:
                            # Fallback based on common extensions or assume jpeg
                            if image_url.lower().endswith('.jpg') or image_url.lower().endswith('.jpeg'):
                                mime_type = "image/jpeg"
                            elif image_url.lower().endswith('.png'):
                                mime_type = "image/png"
                            elif image_url.lower().endswith('.avif'):
                                mime_type = "image/avif"
                            else:
                                mime_type = "image/jpeg"

                        # Create inline data part
                        inline_data = types.Blob(mime_type=mime_type, data=image_bytes)
                        content_parts.append(types.Part(inline_data=inline_data))
                        logger.info(f"Added reference image with MIME type: {mime_type}")
                    except Exception as e:
                        logger.exception(f"Failed to download reference image: {e}")
                        continue

            # Add text prompt
            content_parts.append(types.Part.from_text(text=prompt))

            # Create content for Gemini
            contents = [
                types.Content(
                    role="user",
                    parts=content_parts,
                ),
            ]


            # Build generation config using the extracted method
            generate_content_config = self._build_generation_config(request)

            logger.info("Using Banana generation config from extracted method")

            # Use non-streaming to get the full response and access request ID
            response = self.client.models.generate_content(
                model=self.model,
                contents=contents,
                config=generate_content_config,
            )

            # Extract request ID from response
            request_id = getattr(response, 'responseId', f"banana_{hash(request.item.product_id)}")

            # Process the response parts from all candidates
            if response.candidates:
                for candidate in response.candidates:
                    # Capture finish reason for error reporting
                    last_finish_reason = getattr(candidate, 'finish_reason', None)
                    if candidate.content and candidate.content.parts:
                        for part in candidate.content.parts:
                            # Capture text response for error reporting
                            if hasattr(part, "text") and part.text:
                                last_response_text = part.text
                            if hasattr(part, "inline_data") and part.inline_data and part.inline_data.data:
                                inline_data = part.inline_data
                                data_buffer = inline_data.data

                                # Upload to storage instead of creating data URL
                                file_extension = mimetypes.guess_extension(inline_data.mime_type) or ".png"
                                filename = f"banana_image_{len(all_images)}{file_extension}"

                                try:
                                    # Upload main image to storage
                                    image_url = await self._upload_media_to_storage(
                                        media_content=data_buffer,
                                        filename=filename,
                                        content_type=inline_data.mime_type,
                                        shop_id=request.shop_id,
                                        metadata={
                                            "provider": "gemini",
                                            "model": self.model,
                                            "prompt": prompt,
                                        },
                                    )

                                    # For now, use same URL for thumbnail (can be processed later)
                                    thumbnail_url = image_url

                                    all_images.append(MediaVariant(
                                            job_name=f"variant_{len(all_images)+1}",
                                            type="image",
                                            image_url=image_url,
                                            thumbnail_url=thumbnail_url,
                                            prompt_used=prompt,
                                            reference_images_used=len(request.item.reference_images) if request.item.reference_images else 0,
                                            provider="gemini",
                                            generation_metadata={
                                                "provider": "gemini",
                                                "model": self.model,
                                                "reference_images_used": (
                                                    len(request.item.reference_images) if request.item.reference_images else 0
                                                ),
                                            },
                                        ))
                                except Exception as upload_error:
                                    logger.exception(f"Failed to upload image to storage: {upload_error}")
                                    continue


            # Check if any images were actually generated
            if not all_images:
                # Try to get more details about why generation failed
                error_details = []
                error_details.append("🚨 EXACT ERROR: API call succeeded but no images were generated!")
                error_details.append(f"response: {response}")

                # Add captured response details
                if last_finish_reason:
                    error_details.append(f"🔍 Finish Reason: {last_finish_reason}")
                if last_response_text:
                    error_details.append(f"📝 API Response: {last_response_text}")

                # Add information about the request
                error_details.append(
                    f"🖼️  Reference images provided: {len(request.item.reference_images) if request.item.reference_images else 0}"
                )
                error_details.append(f"📝 Prompt: {prompt}")
                error_details.append(f"🤖 Model: {self.model}")

                detailed_error = "\n".join(error_details)

                logger.error(f"IMAGE GENERATION FAILED: {detailed_error}")
                return ProviderMediaResult(
                    success=False,
                    error_message=detailed_error,
                    provider_job_id=f"banana_{hash(request.item.product_id)}",
                    completion_time=time.time() - start_time,
                )


            # Extract usage metadata if available
            usage_metadata = getattr(response, 'usageMetadata', None)
            metadata = {"usageMetadata": usage_metadata} if usage_metadata else None
            
            logger.info(f"🎯 Generation Summary: Generated {len(all_images)} images. Total time: {time.time() - start_time:.2f}s, Usage Metadata: {usage_metadata}")

            return ProviderMediaResult(
                success=True,
                provider_job_id=request_id,
                variants=all_images,
                completion_time=time.time() - start_time,
                metadata=metadata,
            )

        except Exception as e:
            logger.exception(f"Gemini image generation failed: {e}")
            return ProviderMediaResult(success=False, error_message=str(e))


    async def cleanup(self) -> None:
        """Cleanup Gemini provider resources."""
        if self.client:
            # Google GenAI client doesn't need explicit cleanup
            self.client = None
            logger.info("Cleaned up Gemini provider")
