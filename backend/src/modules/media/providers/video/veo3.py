"""
Google Veo 3 Provider Plugin for E-commerce Video Generation.
Provides professional product video generation using Google's Veo 3 AI.
Specialized for product showcases, lifestyle videos, and social media content.
"""

import time
import logging
import asyncio
import os
import mimetypes
from typing import Dict, List, Optional, Any

import httpx

from google import genai
from google.genai import types

from ..base import VideoProvider
from core.config import get_settings
from ...schemas import ProviderMediaRequest, ProviderMediaResult, MediaVariant

logger = logging.getLogger(__name__)


class Veo3Provider(VideoProvider):
    """Google Veo 3 provider plugin for video generation."""

    def __init__(self):
        self.client: Optional[genai.Client] = None
        self.config: Optional[Dict[str, Any]] = None
        self.model = None  # Will be set from config during initialization

    @property
    def provider_name(self) -> str:
        return "veo3"

    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the Veo 3 provider."""
        # Get API key from settings
        settings = get_settings()
        api_key = settings.VEO3_API_KEY

        if not api_key:
            logger.error("VEO3_API_KEY not configured in settings")
            return False

        try:
            # Store config and get model from config - required, no fallback
            self.config = config
            self.model = config.get("model")

            # Additional Veo 3-specific initialization
            self.client = genai.Client(http_options={"api_version": "v1beta"}, api_key=api_key)

            logger.info(f"Initialized Veo 3 provider with model: {self.model}")
            return True

        except Exception as e:
            logger.exception(f"Failed to initialize Veo 3 provider: {e}")
            return False

    def _build_generation_config(self, request: ProviderMediaRequest) -> types.GenerateVideosConfig:
        """Build generation configuration from request and provider config."""
        # Get generation parameters from provider config with safe fallbacks
        provider_config = request.item.provider_config or {}
        default_config = self.config["config_schema"]

        # Get parameters with defaults from config schema
        negative_prompt = provider_config.get("negative_prompt", default_config["negative_prompt"]["default"])
        aspect_ratio = provider_config.get("aspect_ratio", default_config["aspect_ratio"]["default"])
        resolution = provider_config.get("resolution", default_config["resolution"]["default"])
        seed = provider_config.get("seed", default_config["seed"]["default"])
        duration_seconds = provider_config.get("duration_seconds", default_config["duration_seconds"]["default"])
        number_of_videos = provider_config.get("number_of_videos", default_config["number_of_videos"]["default"])
        compression_quality = provider_config.get(
            "compression_quality", default_config["compression_quality"]["default"]
        )

        # Extract additional unused parameters
        http_options = provider_config.get("http_options", default_config["http_options"]["default"])
        if http_options is not None and isinstance(http_options, dict):
            http_options = types.HttpOptions(**http_options)
        output_gcs_uri = provider_config.get("output_gcs_uri", default_config["output_gcs_uri"]["default"])
        pubsub_topic = provider_config.get("pubsub_topic", default_config["pubsub_topic"]["default"])
        last_frame = provider_config.get("last_frame", default_config["last_frame"]["default"])
        if last_frame is not None and isinstance(last_frame, dict):
            last_frame = types.Image(**last_frame)
        reference_images = provider_config.get("reference_images", default_config["reference_images"]["default"])
        if reference_images is not None and isinstance(reference_images, list):
            reference_images = [types.Image(**r) for r in reference_images if isinstance(r, dict)]

        # Create GenerateVideosConfig object directly
        config = types.GenerateVideosConfig()

        # Set all parameters directly (defaults are None anyway)
        config.aspect_ratio = aspect_ratio
        config.resolution = resolution
        config.number_of_videos = number_of_videos
        config.duration_seconds = duration_seconds
        config.negative_prompt = negative_prompt
        config.compression_quality = compression_quality
        config.seed = seed
        config.http_options = http_options
        config.output_gcs_uri = output_gcs_uri
        config.pubsub_topic = pubsub_topic
        config.last_frame = last_frame
        config.reference_images = reference_images

        return config

    async def _reference_images(self, request: ProviderMediaRequest) -> types.Image:
        """Build reference images from request."""
        image = None
        if request.item.reference_images and len(request.item.reference_images) > 0:
            try:
                image_url = request.item.reference_images[0]

                # Download the image
                async with httpx.AsyncClient() as client:
                    response = await client.get(image_url)
                    response.raise_for_status()
                    image_data = response.content

                # Determine mime type
                mime_type, _ = mimetypes.guess_type(image_url)
                if not mime_type:
                    mime_type = "image/jpeg"  # fallback to jpeg if unable to determine

                # Create Image object with the binary data and mime type
                input_image = types.Image(image_bytes=image_data, mime_type=mime_type)

                image = input_image
                logger.info(f"Using downloaded image for video generation (mime_type: {mime_type})")
            except Exception as e:
                logger.exception(f"Failed to download reference image: {e}")
                # Continue without image input
        return image

    async def _reference_videos(self, request: ProviderMediaRequest) -> types.Video:
        """Build reference videos from request."""
        video = None
        if request.item.reference_videos and len(request.item.reference_videos) > 0:
            try:
                video_url = request.item.reference_videos[0]

                # Download the video
                async with httpx.AsyncClient() as client:
                    response = await client.get(video_url)
                    response.raise_for_status()
                    video_data = response.content

                # Determine mime type
                mime_type, _ = mimetypes.guess_type(video_url)
                if not mime_type:
                    mime_type = "video/mp4"  # fallback to mp4 if unable to determine

                # Create Video object with the binary data and mime type
                input_video = types.Video(video_bytes=video_data, mime_type=mime_type)

                video = input_video
                logger.info(f"Using downloaded video for video generation (mime_type: {mime_type})")
            except Exception as e:
                logger.exception(f"Failed to download reference video: {e}")
                # Continue without video input
        return video

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate professional e-commerce videos using Google Veo 3."""
        if not self.client or not self.config:
            return ProviderMediaResult(success=False, error_message="Provider not initialized")

        try:
            start_time = time.time()
            # Use the prompt directly from the request
            prompt = request.item.prompt
            # Build generation config using the extracted method
            video_config = self._build_generation_config(request)

            logger.info("Using Veo3 generation config from extracted method")

            image = await self._reference_images(request)
            video = await self._reference_videos(request)
            # Add image input if provided
            if request.item.reference_images and len(request.item.reference_images) > 0:
                try:
                    image_url = request.item.reference_images[0]

                    # Download the image
                    async with httpx.AsyncClient() as client:
                        response = await client.get(image_url)
                        response.raise_for_status()
                        image_data = response.content

                    # Determine mime type
                    mime_type, _ = mimetypes.guess_type(image_url)
                    if not mime_type:
                        mime_type = "image/jpeg"  # fallback to jpeg if unable to determine

                    # Create Image object with the binary data and mime type
                    input_image = types.Image(image_bytes=image_data, mime_type=mime_type)

                    image = input_image
                    logger.info(f"Using downloaded image for video generation (mime_type: {mime_type})")
                except Exception as e:
                    logger.exception(f"Failed to download reference image: {e}")
                    # Continue without image input

            # Generate video using Google GenAI SDK
            source = types.GenerateVideosSource(prompt=prompt, image=image, video=video)
            operation = self.client.models.generate_videos(model=self.model, source=source, config=video_config)

            # Wait for the video(s) to be generated
            while not operation.done:
                logger.info("Video has not been generated yet. Check again in 10 seconds...")
                time.sleep(10)
                operation = self.client.operations.get(operation)

            # Extract request ID and usage metadata if available
            request_id = f"veo3_{hash(request.item.product_id)}"
            usage_metadata = (
                getattr(operation.response, "usageMetadata", None) if hasattr(operation, "response") else None
            )

            # Access the generated videos from the operation response
            generated_videos = operation.response.generated_videos

            logger.info(f"Generated {len(generated_videos)} video(s). Usage Metadata: {usage_metadata}")
            all_variants = []
            for i, generated_video in enumerate(generated_videos):
                video_url = generated_video.video.uri
                logger.info(f"Video has been generated: {video_url}")

                # Download the video using SDK's built-in method
                try:
                    # Use SDK's download method (more reliable than HTTP download)
                    video_bytes = self.client.files.download(file=generated_video.video)
                    mime_type = generated_video.video.mime_type
                    filename = f"veo3_video_{i}_{len(generated_videos)}.mp4"

                    # Upload to storage
                    video_storage_url = await self._upload_media_to_storage(
                        media_content=video_bytes,
                        filename=filename,
                        content_type=mime_type,
                        shop_id=request.shop_id,
                        metadata={
                            "provider": "veo3",
                            "model": self.model,
                            "prompt": prompt,
                            "duration_seconds": video_config.duration_seconds,
                            "aspect_ratio": video_config.aspect_ratio,
                        },
                    )
                    logger.info(f"Video uploaded to storage: {video_storage_url}")

                    video_variant = MediaVariant(
                        job_name=f"variant_{i+1}",
                        type="video",
                        video_url=video_storage_url,
                        duration_seconds=video_config.duration_seconds,
                        aspect_ratio=video_config.aspect_ratio,
                        prompt_used=prompt,
                        provider="veo3",
                        generation_metadata={
                            "provider": "veo3",
                            "model": self.model,
                        },
                    )
                    all_variants.append(video_variant)
                except Exception as e:
                    logger.exception(f"Failed to process video {video_url}: {e}")
                    continue

            # Check if any videos were actually generated
            if not all_variants:
                logger.error("No videos were generated.")

                # Build detailed error message
                error_details = []
                error_details.append("❌ No videos were generated by Veo3 API")
                error_details.append(f"🤖 Model: {self.model}")
                error_details.append(f"📝 Prompt: {request.item.prompt}")
                error_details.append(f"⏱️ Duration: {video_config.duration_seconds}s")
                error_details.append(f"📐 Aspect Ratio: {video_config.aspect_ratio}")
                detailed_error = "\n".join(error_details)

                return ProviderMediaResult(
                    success=False,
                    error_message=detailed_error,
                    provider_job_id=f"veo3_{hash(request.item.product_id)}",
                    completion_time=time.time() - start_time,
                )

            # Extract usage metadata from the last operation (or aggregate if needed)
            metadata = {"usageMetadata": usage_metadata} if usage_metadata else None

            logger.info(
                f"🎯 Video Generation Summary: Generated {len(all_variants)} videos. Usage Metadata: {usage_metadata}"
            )

            return ProviderMediaResult(
                success=True,
                provider_job_id=request_id,
                variants=all_variants,
                completion_time=time.time() - start_time,
                metadata=metadata,
            )

        except Exception as e:
            logger.exception(f"Veo 3 generation failed: {e}")
            return ProviderMediaResult(success=False, error_message=str(e))

    async def cleanup(self) -> None:
        """Cleanup Veo 3 provider resources."""
        if self.client:
            # Google GenAI client doesn't need explicit cleanup
            self.client = None
            logger.info("Cleaned up Veo 3 provider")
