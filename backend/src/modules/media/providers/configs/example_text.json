{"type": "text", "timeout": 30, "model": "example_model", "capabilities": {"supported_formats": ["text"], "content_types": ["product_description", "marketing_copy", "social_caption", "seo_snippet"], "supported_languages": ["en"]}, "limits": {"max_variants_per_request": 4, "requests_per_minute": 100, "requests_per_hour": 1000, "token_limits": {"product_description": 300, "marketing_copy": 400, "social_caption": 100, "seo_snippet": 200, "default": 300}}, "costs": {"cost_per_unit": 0.0, "currency": "USD"}, "quality": {"quality_score": 0.95, "average_generation_time": 2}, "features": ["Example text generation", "Test data provider", "No API key required", "Fast response times"], "text_config": {"temperature_settings": {"default": 0.7}, "generation_params": {"temperature": 0.7, "top_p": 0.9, "top_k": 50, "max_tokens": 1000, "presence_penalty": 0.0, "frequency_penalty": 0.0, "stop_sequences": []}, "estimated_completion_time_per_variant": 2}, "metadata": {"provider_name": "Example Text Provider", "description": "Example provider for testing and demonstration purposes"}}