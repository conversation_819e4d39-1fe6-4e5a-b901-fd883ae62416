{"type": "image", "timeout": 30, "model": "example_model", "capabilities": {"supported_formats": ["image"], "supported_styles": ["example_style", "test_style"], "supported_categories": ["test", "demo"]}, "limits": {"max_variants_per_request": 2, "requests_per_minute": 100, "requests_per_hour": 1000}, "costs": {"cost_per_unit": 0.0, "currency": "USD"}, "quality": {"quality_score": 0.9, "average_generation_time": 5}, "features": ["Example image generation", "Test data provider", "No API key required", "Fast response times"], "image_config": {"aspect_ratios": {"square": {"width": 1024, "height": 1024}, "landscape": {"width": 1920, "height": 1080}}, "generation_params": {"quality": "high", "style": "example", "negative_prompt": "", "guidance_scale": 1.0, "num_inference_steps": 1}}, "metadata": {"provider_name": "Example Image Provider", "description": "Example provider for testing and demonstration purposes"}}