{"type": "video", "model": "veo-3.0-fast-generate-001", "config_schema": {"http_options": {"type": "object", "default": {"timeout": 800000}, "description": "Used to override HTTP request options in milliseconds."}, "number_of_videos": {"type": "integer", "enum": [1], "default": 1, "description": "Number of output videos."}, "output_gcs_uri": {"type": "string", "default": null, "description": "The gcs bucket where to save the generated videos."}, "fps": {"type": "integer", "enum": [24], "default": 24, "description": "Frames per second for video generation."}, "duration_seconds": {"type": "integer", "enum": [8], "default": 8, "description": "Duration of the clip for video generation in seconds."}, "seed": {"type": "integer", "min": 0, "default": null, "description": "The RNG seed. If RNG seed is exactly same for each request with unchanged inputs, the prediction results will be consistent. Otherwise, a random RNG seed will be used each time to produce a different result."}, "aspect_ratio": {"type": "string", "enum": ["16:9", "9:16"], "default": "16:9", "description": "The aspect ratio for the generated video. 16:9 (landscape) and 9:16 (portrait) are supported."}, "resolution": {"type": "string", "enum": ["720p", "1080p"], "default": "720p", "description": "The resolution for the generated video. 720p and 1080p are supported."}, "person_generation": {"type": "string", "enum": ["dont_allow", "allow_adult"], "default": "allow_adult", "description": "Whether allow to generate person videos, and restrict to specific ages. Supported values are: dont_allow, allow_adult."}, "pubsub_topic": {"type": "string", "default": null, "description": "The pubsub topic where to publish the video generation progress."}, "negative_prompt": {"type": "string", "default": "cartoon, drawing, low quality, blurry, distorted, amateur, watermark, text overlay", "description": "Explicitly state what should not be included in the generated videos."}, "enhance_prompt": {"type": "boolean", "default": true, "description": "Whether to use the prompt rewriting logic."}, "generate_audio": {"type": "boolean", "default": true, "description": "Whether to generate audio along with the video."}, "last_frame": {"type": "object", "default": null, "description": "Image to use as the last frame of generated videos. Only supported for image to video use cases."}, "reference_images": {"type": "array", "items": {"type": "object"}, "default": null, "description": "The images to use as the references to generate the videos. If this field is provided, the text prompt field must also be provided. The image, video, or last_frame field are not supported. Each image must be associated with a type. Veo 2 supports up to 3 asset images *or* 1 style image."}, "compression_quality": {"type": "string", "enum": ["OPTIMIZED", "LOSSLESS"], "default": null, "description": "Compression quality of the generated videos."}}}