{"type": "image", "model": "gemini-2.5-flash-image-preview", "config_schema": {"http_options": {"type": "object", "properties": {"timeout": {"type": "integer", "min": 1, "description": "Timeout in milliseconds."}}, "default": {"timeout": 300000}, "description": "Used to override HTTP request options."}, "should_return_http_response": {"type": "boolean", "default": null, "description": "If true, the raw HTTP response will be returned in the 'sdk_http_response' field."}, "system_instruction": {"type": "object", "default": null, "description": "Instructions for the model to steer it toward better performance. For example, \"Answer as concisely as possible\" or \"Don't use technical terms in your response\"."}, "temperature": {"type": "number", "min": 0, "max": 2, "default": 1, "description": "Value that controls the degree of randomness in token selection. Lower temperatures are good for prompts that require a less open-ended or creative response, while higher temperatures can lead to more diverse or creative results."}, "top_p": {"type": "number", "min": 0, "max": 1, "default": 0.95, "description": "Tokens are selected from the most to least probable until the sum of their probabilities equals this value. Use a lower value for less random responses and a higher value for more random responses."}, "top_k": {"type": "number", "min": 1, "default": 40, "description": "For each token selection step, the top_k tokens with the highest probabilities are sampled. Then tokens are further filtered based on top_p with the final token selected using temperature sampling. Use a lower number for less random responses and a higher number for more random responses."}, "candidate_count": {"type": "integer", "min": 1, "max": 8, "default": 1, "description": "Number of response variations to return."}, "max_output_tokens": {"type": "integer", "min": 1, "default": 8192, "description": "Maximum number of tokens that can be generated in the response."}, "stop_sequences": {"type": "array", "items": {"type": "string"}, "maxItems": 5, "default": [], "description": "List of strings that tells the model to stop generating text if one of the strings is encountered in the response."}, "response_logprobs": {"type": "boolean", "default": false, "description": "Whether to return the log probabilities of the tokens that were chosen by the model at each step."}, "logprobs": {"type": "integer", "min": 1, "max": 5, "default": null, "description": "Number of top candidate tokens to return the log probabilities for at each generation step."}, "presence_penalty": {"type": "number", "min": -2, "max": 2, "default": 0, "description": "Positive values penalize tokens that already appear in the generated text, increasing the probability of generating more diverse content."}, "frequency_penalty": {"type": "number", "min": -2, "max": 2, "default": 0, "description": "Positive values penalize tokens that repeatedly appear in the generated text, increasing the probability of generating more diverse content."}, "seed": {"type": "integer", "min": 0, "default": null, "description": "When seed is fixed to a specific number, the model makes a best effort to provide the same response for repeated requests. By default, a random number is used."}, "response_mime_type": {"type": "string", "default": null, "description": "Output response mimetype of the generated candidate text. Supported mimetype: - text/plain: (default) Text output. - application/json: JSON response in the candidates. The model needs to be prompted to output the appropriate response type, otherwise the behavior is undefined. This is a preview feature."}, "response_schema": {"type": "object", "default": null, "description": "The Schema object allows the definition of input and output data types. These types can be objects, but also primitives and arrays. Represents a select subset of an OpenAPI 3.0 schema object. If set, a compatible response_mime_type must also be set. Compatible mimetypes: application/json: Schema for JSON response."}, "response_json_schema": {"type": "object", "default": null, "description": "Optional. Output schema of the generated response. This is an alternative to response_schema that accepts JSON Schema. If set, response_schema must be omitted, but response_mime_type is required."}, "routing_config": {"type": "object", "default": null, "description": "Configuration for model router requests."}, "model_selection_config": {"type": "object", "default": null, "description": "Configuration for model selection."}, "safety_settings": {"type": "array", "items": {"type": "object", "properties": {"threshold": {"type": "string", "enum": ["HARM_BLOCK_THRESHOLD_UNSPECIFIED", "BLOCK_LOW_AND_ABOVE", "BLOCK_MEDIUM_AND_ABOVE", "BLOCK_ONLY_HIGH", "BLOCK_NONE", "OFF"], "description": "The category of harm to block."}}}, "default": null, "description": "Safety settings in the request to block unsafe content in the response."}, "tools": {"type": "array", "items": {"type": "object"}, "default": null, "description": "Code that enables the system to interact with external systems to perform an action outside of the knowledge and scope of the model."}, "tool_config": {"type": "object", "default": null, "description": "Associates model output to a specific function call."}, "labels": {"type": "object", "default": null, "description": "Labels with user-defined metadata to break down billed charges."}, "cached_content": {"type": "string", "default": null, "description": "Resource name of a context cache that can be used in subsequent requests."}, "response_modalities": {"type": "array", "items": {"type": "string", "enum": ["TEXT", "IMAGE", "AUDIO"]}, "default": ["IMAGE"], "description": "The requested modalities of the response. Represents the set of modalities that the model can return."}, "media_resolution": {"type": "string", "enum": ["MEDIA_RESOLUTION_LOW", "MEDIA_RESOLUTION_MEDIUM", "MEDIA_RESOLUTION_HIGH"], "default": "MEDIA_RESOLUTION_LOW", "description": "If specified, the media resolution specified will be used."}, "automatic_function_calling": {"type": "object", "default": null, "description": "The configuration for automatic function calling."}, "thinking_config": {"type": "object", "properties": {"thinking_budget": {"type": "integer", "min": 0, "default": null, "description": "Number of thinking tokens for Gemini 2.5 models. 0 disables thinking, null uses model default."}, "include_thoughts": {"type": "boolean", "default": null, "description": "Whether to include thoughts in the response. null uses model default."}}, "default": null, "description": "The thinking features configuration."}}}