{"type": "text", "model": "gemini-2.5-flash", "config_schema": {"http_options": {"type": "object", "default": {"timeout": 300000}, "description": "Used to override HTTP request options."}, "should_return_http_response": {"type": "boolean", "default": null, "description": "If true, the raw HTTP response will be returned in the 'sdk_http_response' field."}, "system_instruction": {"type": "object", "default": null, "description": "Instructions for the model to steer it toward better performance. For example, \"Answer as concisely as possible\" or \"Don't use technical terms in your response\"."}, "temperature": {"type": "number", "min": 0, "max": 2, "default": 1, "description": "Value that controls the degree of randomness in token selection. Lower temperatures are good for prompts that require a less open-ended or creative response, while higher temperatures can lead to more diverse or creative results. A temperature of 0.0 means that the highest probability tokens are always selected. For most use cases, try starting with a temperature of 0.9 or 0.95. Range: [0.0, 2.0]"}, "top_p": {"type": "number", "min": 0, "max": 1, "default": 0.95, "description": "Tokens are selected from the most to least probable until the sum of their probabilities equals this value. Use a lower value for less random responses and a higher value for more random responses. Range: [0.0, 1.0]"}, "top_k": {"type": "integer", "min": 1, "default": 40, "description": "For each token selection step, the top_k tokens with the highest probabilities are sampled. Then tokens are further filtered based on top_p with the final token selected using temperature sampling. Use a lower number for less random responses and a higher number for more random responses. Range: [1, 1000]"}, "candidate_count": {"type": "integer", "min": 1, "max": 8, "default": 1, "description": "Number of response candidates to return. Currently only 1 is supported."}, "max_output_tokens": {"type": "integer", "min": 1, "default": 8192, "description": "Maximum number of tokens that can be generated in the response. Specify a lower value for shorter responses and a higher value for longer responses. A token is approximately four characters. 100 tokens correspond to roughly 60-80 words. Range: [1, 8192]"}, "stop_sequences": {"type": "array", "items": {"type": "string"}, "maxItems": 5, "default": [], "description": "Up to 5 character sequences that will stop output generation. If specified, the API will stop at the first appearance of a stop sequence. The stop sequence will not be included as part of the response."}, "seed": {"type": "integer", "min": 0, "default": null, "description": "When seed is fixed to a specific number, the model makes a best effort to provide the same response for repeated requests. By default, a random number is used. If seed is not specified, different requests will have different responses. Note that even with the same seed, different model versions or requests can still change the response."}, "presence_penalty": {"type": "number", "min": -2, "max": 2, "default": 0, "description": "Penalty for tokens that have already appeared. Positive values discourage repetition. Range: [-2.0, 2.0]"}, "frequency_penalty": {"type": "number", "min": -2, "max": 2, "default": 0, "description": "Penalty proportional to token frequency. Positive values reduce vocabulary repetition. Range: [-2.0, 2.0]"}, "response_mime_type": {"type": "string", "default": null, "description": "MIME type of the generated candidate. Supported MIME types: - `text/plain`: Text output - `application/json`: JSON response in the candidates - `text/x.enum`: ENUM in the candidates"}, "response_schema": {"type": "object", "default": null, "description": "Schema for the response. If set, the response will be validated against the schema and only valid responses will be returned. If not set, no schema validation will be performed."}, "response_json_schema": {"type": "object", "default": null, "description": "Optional. Output schema of the generated response. This is an alternative to response_schema that accepts JSON Schema. If set, response_schema must be omitted, but response_mime_type is required."}, "routing_config": {"type": "object", "default": null, "description": "Configuration for model router requests."}, "model_selection_config": {"type": "object", "default": null, "description": "Configuration for model selection."}, "safety_settings": {"type": "array", "items": {"type": "object"}, "default": null, "description": "Safety settings in the request to block unsafe content in the response."}, "tools": {"type": "array", "items": {"type": "object"}, "default": null, "description": "Code that enables the system to interact with external systems to perform an action outside of the knowledge and scope of the model."}, "tool_config": {"type": "object", "default": null, "description": "Associates model output to a specific function call."}, "labels": {"type": "object", "default": null, "description": "Labels with user-defined metadata to break down billed charges."}, "cached_content": {"type": "string", "default": null, "description": "Resource name of a context cache that can be used in subsequent requests."}, "thinking_config": {"type": "object", "properties": {"thinking_budget": {"type": "integer", "min": 0, "default": null, "description": "Number of thinking tokens for Gemini 2.5 models. 0 disables thinking, null uses model default."}}, "default": null, "description": "Configuration for thinking/reasoning capabilities."}, "response_logprobs": {"type": "boolean", "default": false, "description": "Whether to return log probabilities of the output tokens in the response. This option is only supported for Gemini 1.5 Pro and 1.5 Flash models."}, "logprobs": {"type": "integer", "min": 1, "max": 5, "default": null, "description": "Number of top logprobs to return at each step. This option is only supported for Gemini 1.5 Pro and 1.5 Flash models. Range: [1, 5]"}, "response_modalities": {"type": "array", "items": {"type": "string", "enum": ["TEXT", "IMAGE", "AUDIO"]}, "default": ["TEXT"], "description": "The requested modalities of the response. Represents the set of modalities that the model can return. Defaults to TEXT if not specified."}, "speech_config": {"type": "object", "default": null, "description": "The speech generation configuration."}, "media_resolution": {"type": "string", "enum": ["MEDIA_RESOLUTION_LOW", "MEDIA_RESOLUTION_MEDIUM", "MEDIA_RESOLUTION_HIGH"], "default": "MEDIA_RESOLUTION_MEDIUM", "description": "If specified, the media resolution specified will be used."}, "automatic_function_calling": {"type": "object", "default": null, "description": "The configuration for automatic function calling."}}}