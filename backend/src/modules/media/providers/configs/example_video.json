{"type": "video", "timeout": 60, "model": "example_model", "capabilities": {"supported_formats": ["video"], "supported_aspect_ratios": ["16:9", "1:1", "9:16"], "max_duration_seconds": 10, "supported_person_generation": []}, "limits": {"max_variants_per_request": 1, "requests_per_minute": 50, "requests_per_hour": 500}, "costs": {"cost_per_unit": 0.0, "currency": "USD"}, "quality": {"quality_score": 0.88, "average_generation_time": 10}, "features": ["Example video generation", "Test data provider", "No API key required", "Fast response times"], "video_config": {"variant_aspect_ratios": {"square": "1:1", "vertical": "9:16", "horizontal": "16:9"}, "aspect_resolutions": {"1:1": "1024x1024", "9:16": "1080x1920", "16:9": "1920x1080"}, "generation_params": {"resolution": "1080p", "fps": 30, "duration_seconds": 8, "codec": "H.264", "bitrate": "high"}}, "metadata": {"provider_name": "Example Video Provider", "description": "Example provider for testing and demonstration purposes"}}