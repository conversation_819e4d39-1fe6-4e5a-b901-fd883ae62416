"""
Base provider classes for media generation.
Provides common functionality for Image, Text, and Video providers.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod

from ..schemas import ProviderMediaRequest, ProviderMediaResult
# Removed imports for create_product_context and create_brand_context as they use non-schema fields
# Config classes not needed in base - providers handle their own config parsing
from ...storage.storage_service import MediaStorageService

logger = logging.getLogger(__name__)



class BaseMediaProvider(ABC):
    """Base class for all media providers."""

    def __init__(self):
        self.client = None
        self.config: Optional[Dict[str, Any]] = None
        self.storage_service: Optional[MediaStorageService] = None

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Return the provider name."""
        pass

    @property
    @abstractmethod
    def supported_media_types(self) -> List[str]:
        """Return list of supported media types."""
        pass

    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the provider."""
        pass

    @abstractmethod
    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate media content."""
        pass

    def set_storage_service(self, storage_service: MediaStorageService) -> None:
        """Set the storage service for media uploads."""
        self.storage_service = storage_service
        logger.info(f"Storage service set for {self.provider_name}")

    async def cleanup(self) -> None:
        """Cleanup provider resources."""
        if self.client:
            self.client = None
            logger.info(f"Cleaned up {self.provider_name}")


class ImageProvider(BaseMediaProvider):
    """Base class for image generation providers."""

    @property
    def supported_media_types(self) -> List[str]:
        return ["image"]

    async def _upload_media_to_storage(
        self,
        media_content: bytes,
        filename: str,
        content_type: str,
        shop_id: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Upload media content to storage and return the public URL."""
        if not self.storage_service:
            raise ValueError("Storage service not configured")

        media_file = await self.storage_service.upload_media(
            #TODO: Update to use shop_id external_id
            shop_id=str(shop_id),
            media_content=media_content,
            filename=filename,
            content_type=content_type,
            metadata=metadata
        )

        return media_file.public_url


class TextProvider(BaseMediaProvider):
    """Base class for text generation providers."""

    @property
    def supported_media_types(self) -> List[str]:
        return ["text"]


class VideoProvider(BaseMediaProvider):
    """Base class for video generation providers."""

    @property
    def supported_media_types(self) -> List[str]:
        return ["video"]

    async def _upload_media_to_storage(
        self,
        media_content: bytes,
        filename: str,
        content_type: str,
        shop_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Upload media content to storage and return the public URL."""
        if not self.storage_service:
            raise ValueError("Storage service not configured")

        media_file = await self.storage_service.upload_media(
            shop_id=shop_id,
            media_content=media_content,
            filename=filename,
            content_type=content_type,
            metadata=metadata
        )

        return media_file.public_url
