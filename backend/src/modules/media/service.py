"""
Media Generation Service - Core business logic for ProductVideo
Generic service that uses provider plugins for different AI services.
"""

import logging
from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from core.services.base_service import BaseService
from core.config import get_settings
from .models import MediaJob, MediaJobStatus
from .schemas import MediaGenerateRequest, ProviderMediaRequest, ProviderMediaResult

from .providers.manager import (
    get_provider,
    _check_provider_override,
)


logger = logging.getLogger(__name__)
settings = get_settings()


class MediaGenerationService(BaseService[MediaJob, dict, dict]):
    """Service for media generation operations using provider plugins."""

    def __init__(self):
        super().__init__(MediaJob)

    async def get_by_external_id(self, db: AsyncSession, external_id: str) -> Optional[MediaJob]:
        """Get media job by external ID."""
        result = await db.execute(select(MediaJob).where(MediaJob.external_id == external_id))
        return result.scalar_one_or_none()

    async def generate_media_with_provider(
        self, provider_name: str, request: ProviderMediaRequest
    ) -> ProviderMediaResult:
        """Generate media using a specific provider plugin. No fallbacks."""
        logger.info(f"generate_media_with_provider called with provider_name: {provider_name}")

        # Try the specified provider (override checking is now handled in the manager)
        logger.info(f"Calling _try_provider with: {provider_name}")
        result = await self._try_provider(provider_name, request)

        return result

    async def _try_provider(self, provider_name: str, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Try to generate media with a specific provider."""
        try:
            # Provider override checking is now handled in the manager
            resolved_provider_name = provider_name

            logger.info(f"Getting provider: {resolved_provider_name} for media type: {request.media_type}")

            # Use the new interface to get provider with configuration overrides
            provider = await get_provider(resolved_provider_name, request.media_type)

            # Update resolved_provider_name to the actual provider name (may be overridden)
            actual_provider_name = provider.provider_name
            if actual_provider_name != resolved_provider_name:
                logger.info(f"Provider {resolved_provider_name} was resolved to {actual_provider_name} due to override")

            logger.info(f"Successfully got provider {actual_provider_name}: {type(provider)}")

            # Check if provider supports the requested media type
            if request.media_type not in provider.supported_media_types:
                logger.warning(f"Provider {actual_provider_name} does not support {request.media_type}")
                return ProviderMediaResult(
                    success=False,
                    error_message=f"Provider {actual_provider_name} does not support {request.media_type}",
                )

            logger.info(f"Calling generate_media on provider {actual_provider_name}")
            result = await provider.generate_media(request)
            logger.info(
                f"PROVIDER_RESULT: Provider {actual_provider_name} returned success: {result.success}, variants: {len(result.variants or [])}"
            )

            return result

        except Exception as e:
            logger.exception(
                f"Error with provider {actual_provider_name if 'actual_provider_name' in locals() else resolved_provider_name}: {e}"
            )
            logger.exception(f"Exception type: {type(e)}")
            import traceback

            logger.exception(f"Traceback: {traceback.format_exc()}")
            raise

    async def create_generation_job(self, db: AsyncSession, user_id: int, request: MediaGenerateRequest) -> MediaJob:
        """Create media generation job for a single product."""

        # Store the full request payload
        full_payload = request.model_dump()

        # Get the single item
        item = request.item
        product_id_str = item.product_id
        quantity = item.quantity  # Use quantity from item

        # Look up the product to get the external_id
        from modules.products.models import Product
        from sqlalchemy import select
        product_result = await db.execute(select(Product).filter(Product.id == int(product_id_str)))
        product = product_result.scalar_one_or_none()
        if not product:
            raise ValueError(f"Product with id {product_id_str} not found")

        product_external_id = product.external_id

        logger.debug(
            f"Creating MediaJob for product {product_id_str} (external_id: {product_external_id}), quantity: {quantity}, request.shop_id: {request.shop_id}"
        )

        # Resolve provider using override if set
        resolved_provider = _check_provider_override(request.media_type, request.provider)
        final_provider = resolved_provider or request.provider

        # Create main job
        import uuid
        job = MediaJob(
            user_id=user_id,
            product_external_id=product_external_id,
            external_id = uuid.uuid4(),
            status=MediaJobStatus.PENDING,
            media_type=request.media_type,
            provider=request.provider,  # Keep requested provider
            resolved_provider=final_provider,  # Store resolved provider
            shop_id=request.shop_id,  # Add shop_id from request
            full_payload=full_payload,  # Store complete payload
        )

        logger.debug(f"Created MediaJob with external_id {job.external_id}, shop_id: {job.shop_id}")

        return job

    async def get_job_by_external_id(self, db: AsyncSession, external_id: str) -> Optional[MediaJob]:
        """Get job by external_id with automatic freshness."""
        from core.config import get_settings
        from uuid import UUID

        settings = get_settings()
        logger.debug(f"Database URL: {settings.DATABASE_URL}")
        logger.debug(f"Executing database query for job external_id {external_id}")

        # Force fresh data by expiring all cached objects before query
        logger.debug(f"Calling db.expire_all() to force fresh data")
        db.expire_all()

        try:
            external_uuid = UUID(external_id)
        except ValueError:
            logger.warning(f"Invalid external_id format: {external_id}")
            return None

        query = select(MediaJob).where(MediaJob.external_id == external_uuid)
        result = await db.execute(query)
        job = result.scalar_one_or_none()

        if job:
            logger.debug(
                f"Retrieved job {external_id} from DB - status: {job.status}"
            )
        else:
            logger.warning(f"Job {external_id} not found in database")

        return job

    async def list_user_jobs(
        self,
        db: AsyncSession,
        user_id: int,
        page: int = 1,
        per_page: int = 20,
        status_filter: Optional[str] = None,
        product_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """List media generation jobs for a user with pagination and filters."""
        try:
            from sqlalchemy import select, func
            from modules.media.models import MediaJob

            # Build query
            query = select(MediaJob).filter(MediaJob.user_id == user_id)

            # Apply filters
            if status_filter:
                try:
                    from modules.media.models import MediaJobStatus

                    status_enum = MediaJobStatus(status_filter)
                    query = query.filter(MediaJob.status == status_enum)
                except ValueError:
                    raise ValueError(f"Invalid status filter: {status_filter}")

            if product_id:
                from uuid import UUID

                try:
                    product_uuid = UUID(product_id)
                    query = query.filter(MediaJob.product_external_id == product_uuid)
                except ValueError:
                    raise ValueError(f"Invalid product_id format: {product_id}")

            # Get total count
            total = (await db.execute(select(func.count()).select_from(query.subquery()))).scalar_one()

            # Apply pagination
            offset = (page - 1) * per_page
            jobs = (
                (await db.execute(query.order_by(MediaJob.created_at.desc()).offset(offset).limit(per_page)))
                .scalars()
                .all()
            )

            # Convert to response format
            job_responses = []
            for job in jobs:
                job_response = {
                    "job_id": str(job.external_id),  # Use external_id instead of database ID
                    "product_id": str(job.product_external_id),
                    "status": job.status.value,
                    "media_type": job.media_type,
                    "created_at": job.created_at.isoformat() if job.created_at else None,
                    "variants": job.variants,
                    "error_message": job.error_message,
                }
                job_responses.append(job_response)

            return {"jobs": job_responses, "total": total, "page": page, "per_page": per_page}

        except Exception as e:
            logger.exception(f"Failed to list user jobs: {e}")
            raise

    async def cancel_job(self, db: AsyncSession, job_id: int, user_id: int) -> bool:
        """Cancel a media generation job."""
        try:
            from sqlalchemy import select, update
            from modules.media.models import MediaJob, MediaJobStatus

            # Get job with ownership check
            job = await db.execute(select(MediaJob).filter(MediaJob.id == job_id, MediaJob.user_id == user_id))
            job = job.scalar_one_or_none()

            if not job:
                return False

            # Only cancel if job is in pending or processing state
            if job.status not in [MediaJobStatus.PENDING, MediaJobStatus.PROCESSING]:
                return False

            # Revoke Celery task if it exists
            if job.celery_task_id:
                try:
                    from modules.queue.queue_service import celery_service

                    celery_service.revoke_task(job.celery_task_id)
                    logger.info(f"Revoked Celery task {job.celery_task_id} for job {job_id}")
                except Exception as e:
                    logger.warning(f"Failed to revoke Celery task {job.celery_task_id}: {e}")

            # Update job status
            job.status = MediaJobStatus.CANCELLED

            await db.commit()
            logger.info(f"Cancelled job {job_id} for user {user_id}")
            return True

        except Exception as e:
            logger.exception(f"Failed to cancel job {job_id}: {e}")
            await db.rollback()
            return False

    async def cancel_job_by_external_id(self, db: AsyncSession, external_id: str, user_id: int) -> bool:
        """Cancel a media generation job by external_id."""
        try:
            from sqlalchemy import select, update
            from modules.media.models import MediaJob, MediaJobStatus
            from uuid import UUID

            try:
                external_uuid = UUID(external_id)
            except ValueError:
                logger.warning(f"Invalid external_id format: {external_id}")
                return False

            # Get job with ownership check
            job = await db.execute(
                select(MediaJob).filter(MediaJob.external_id == external_uuid, MediaJob.user_id == user_id)
            )
            job = job.scalar_one_or_none()

            if not job:
                return False

            # Only cancel if job is in pending or processing state
            if job.status not in [MediaJobStatus.PENDING, MediaJobStatus.PROCESSING]:
                return False

            # Revoke Celery task if it exists
            if job.celery_task_id:
                try:
                    from modules.queue.queue_service import celery_service

                    celery_service.revoke_task(job.celery_task_id)
                    logger.info(f"Revoked Celery task {job.celery_task_id} for job {external_id}")
                except Exception as e:
                    logger.warning(f"Failed to revoke Celery task {job.celery_task_id}: {e}")

            # Update job status
            job.status = MediaJobStatus.CANCELLED

            await db.commit()
            logger.info(f"Cancelled job {external_id} for user {user_id}")
            return True

        except Exception as e:
            logger.exception(f"Failed to cancel job {external_id}: {e}")
            await db.rollback()
            return False

    async def retry_job(self, db: AsyncSession, job_id: int, user_id: int) -> Optional[MediaJob]:
        """Retry a failed media generation job."""
        try:
            from sqlalchemy import select
            from modules.media.models import MediaJob, MediaJobStatus

            # Get job with ownership check
            job = await db.execute(select(MediaJob).filter(MediaJob.id == job_id, MediaJob.user_id == user_id))
            job = job.scalar_one_or_none()

            if not job:
                return None

            # Only retry if job is in failed or cancelled state
            if job.status not in [MediaJobStatus.FAILED, MediaJobStatus.CANCELLED]:
                return None

            # Reset job status
            job.status = MediaJobStatus.PENDING

            # Re-enqueue the job using Celery
            try:
                from modules.queue.queue_service import celery_service

                celery_task_id = celery_service.enqueue_media_generation(
                    user_id=job.user_id,
                    job_id=str(job.external_id),
                    payload=job.full_payload,
                )
                job.celery_task_id = celery_task_id
                logger.info(f"Re-enqueued job {job_id} with Celery task {celery_task_id}")
            except Exception as e:
                logger.warning(f"Failed to re-enqueue job {job_id}: {e}")

            await db.commit()
            logger.info(f"Retrying job {job_id} for user {user_id}")
            return job

        except Exception as e:
            logger.exception(f"Failed to retry job {job_id}: {e}")
            await db.rollback()
            return None

    async def retry_job_by_external_id(self, db: AsyncSession, external_id: str, user_id: int) -> Optional[MediaJob]:
        """Retry a failed media generation job by external_id."""
        try:
            from sqlalchemy import select
            from modules.media.models import MediaJob, MediaJobStatus
            from uuid import UUID

            try:
                external_uuid = UUID(external_id)
            except ValueError:
                logger.warning(f"Invalid external_id format: {external_id}")
                return None

            # Get job with ownership check
            job = await db.execute(
                select(MediaJob).filter(MediaJob.external_id == external_uuid, MediaJob.user_id == user_id)
            )
            job = job.scalar_one_or_none()

            if not job:
                return None

            # Only retry if job is in failed or cancelled state
            if job.status not in [MediaJobStatus.FAILED, MediaJobStatus.CANCELLED]:
                return None

            # Reset job status
            job.status = MediaJobStatus.PENDING

            # Re-enqueue the job using Celery
            try:
                from modules.queue.queue_service import celery_service

                celery_task_id = celery_service.enqueue_media_generation(
                    user_id=job.user_id,
                    job_id=str(job.external_id),
                    payload=job.full_payload,
                )
                job.celery_task_id = celery_task_id
                logger.info(f"Re-enqueued job {external_id} with Celery task {celery_task_id}")
            except Exception as e:
                logger.warning(f"Failed to re-enqueue job {external_id}: {e}")

            await db.commit()
            logger.info(f"Retrying job {external_id} for user {user_id}")
            return job

        except Exception as e:
            logger.exception(f"Failed to retry job {external_id}: {e}")
            await db.rollback()
            return None

    async def push_media_to_platforms(
        self,
        db: AsyncSession,
        job_external_id: str,
        user_id: int,
        shop_domain: str,
        publish_targets: List[str],
        publish_options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Push media job to specified platforms using external ID."""
        try:
            from sqlalchemy import select
            from plugins.media_service import media_plugin_manager
            from uuid import UUID


            # Convert external ID to UUID
            try:
                job_uuid = UUID(job_external_id)
            except ValueError:
                raise ValueError(f"Invalid job external_id format")

            # Get job with ownership check
            job = await db.execute(
                select(MediaJob).filter(
                    MediaJob.external_id == job_uuid,
                    MediaJob.user_id == user_id,
                    MediaJob.status == MediaJobStatus.COMPLETED,
                )
            )
            job = job.scalar_one_or_none()

            logger.debug(f"Job query result: {job is not None}")
            if job:
                logger.debug(f"Job details: external_id={job.external_id}, user_id={job.user_id}, image_url={bool(job.image_url)}, video_url={bool(job.video_url)}")

            if not job:
                logger.debug(f"Job not found or not completed for job_external_id={job_external_id}, user_id={user_id}")
                raise ValueError("Job not found or not completed")

            # Get media URL from the variants list
            media_url = None
            if job.variants and len(job.variants) > 0:
                # Use the first variant URL (could be enhanced to select by type)
                media_url = job.variants[0].get("url")
            logger.debug(f"Media URL: {media_url}")
            if not media_url:
                raise ValueError("No media URL available for this job")

            # Get product_id from job
            product_id = str(job.product_external_id)
            logger.debug(f"Product ID: {product_id}")

            results = {}

            for target in publish_targets:
                try:
                    logger.debug(f"Pushing to {target}")
                    # Push to platform
                    push_result = await media_plugin_manager.push_media_to_product(
                        store_type=target,
                        shop_domain=shop_domain,
                        product_id=product_id,
                        media_url=media_url,
                        alt_text=publish_options.alt_text if publish_options else None,
                    )

                    logger.debug(f"Push result for {target}: {push_result}")

                    results[target] = {
                        "success": push_result.get("success", False),
                        "platform_id": push_result.get("platform_id"),
                        "url": push_result.get("url"),
                        "error": push_result.get("error"),
                    }

                except Exception as e:
                    print(f"Failed to push to {target}: {e}")
                    results[target] = {"success": False, "error": str(e)}

            final_result = {"success": any(r.get("success", False) for r in results.values()), "results": results}
            logger.debug(f"Final result: {final_result}")
            return final_result

        except Exception as e:
            print(f"Failed to push media to platforms: {e}")
            raise


    async def fail_job(self, db: AsyncSession, job_id: int, error_message: str):
        """Mark a job as failed with error message."""
        try:
            from sqlalchemy import select, update

            # Update job status
            await db.execute(
                update(MediaJob)
                .filter(MediaJob.id == job_id)
                .values(status=MediaJobStatus.FAILED, error_message=error_message)
            )

            await db.commit()
            logger.info(f"Marked job {job_id} as failed: {error_message}")

        except Exception as e:
            logger.exception(f"Failed to fail job: {e}")
            await db.rollback()
            raise


# Create service instance
media_service = MediaGenerationService()
