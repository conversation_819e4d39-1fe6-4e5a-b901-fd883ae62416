import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class MediaCleanupProcessor:
    """Processor for cleaning up old media files and jobs."""

    async def process(self, days_old: int = 90) -> Dict[str, Any]:
        """
        Clean up old generated media files to save storage space.

        Args:
            days_old: Number of days old media to keep
        """
        from core.db.database import get_session_factory
        from datetime import datetime, timezone, timedelta
        import os

        session_factory = get_session_factory()
        async with session_factory() as db:
            try:
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)

                # Query old media records
                from sqlalchemy import select, and_
                from modules.media.models import MediaJob
                from modules.media.models import MediaJobStatus
                from modules.storage.storage_service import media_storage_service

                # Find old completed jobs
                old_jobs_result = await db.execute(
                    select(MediaJob).filter(
                        and_(MediaJob.completed_at < cutoff_date, MediaJob.status == MediaJobStatus.COMPLETED)
                    )
                )
                old_jobs = old_jobs_result.scalars().all()

                cleaned_files = 0
                cleaned_jobs = 0

                for job in old_jobs:
                    try:
                        # Delete media files from storage
                        try:
                            if job.variants:
                                for media_entry in job.variants:
                                    media_url = media_entry.get("url")
                                    if media_url and "storage" in media_url:
                                        # Extract storage path and delete
                                        storage_path = media_url.split("/storage/")[-1]
                                        await media_storage_service.delete_media(storage_path)
                                        cleaned_files += 1

                        except Exception as e:
                            logger.warning(f"Failed to delete media file for job {job.id}: {e}")

                        # Delete database record
                        await db.delete(job)
                        cleaned_jobs += 1

                    except Exception as e:
                        logger.exception(f"Failed to cleanup job {job.id}: {e}")

                await db.commit()

                logger.info(f"Cleaned up {cleaned_jobs} jobs and {cleaned_files} media files older than {cutoff_date}")

                return {
                    "status": "completed",
                    "message": f"Cleaned up {cleaned_jobs} jobs and {cleaned_files} files",
                    "cleaned_jobs": cleaned_jobs,
                    "cleaned_files": cleaned_files,
                }

            except Exception as e:
                logger.exception(f"Error cleaning up old media: {e}", exc_info=True)
                raise