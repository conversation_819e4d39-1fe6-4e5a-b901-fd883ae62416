import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from contextlib import asynccontextmanager

from core.db.database import get_session_factory
from core.config import get_settings
from modules.media.models import  MediaJob
from modules.media.schemas import MediaVariant
from plugins import get_media_service
from core.metrics import media_push_duration, media_push_failures

logger = logging.getLogger(__name__)


class MediaPushProcessor:
    """Enhanced media push processor with retry logic, monitoring, and multi-platform support."""

    def __init__(self):
        self.max_retries = 3
        self.retry_delay_base = 2  # Base delay in seconds for exponential backoff

    @asynccontextmanager
    async def get_db_session(self):
        """Context manager for database sessions."""
        session_factory = get_session_factory()
        db = session_factory()
        try:
            yield db
        finally:
            await db.close()

    def process(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process media push job with enhanced error handling and monitoring.

        Args:
            job_data: Job data dictionary

        Returns:
            Job result with detailed status
        """
        import asyncio

        # Handle event loop issues in forked worker processes
        # In Celery forked workers, we need to ensure we create a fresh event loop
        return asyncio.run(self._process_async(job_data))

    async def _process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async implementation with comprehensive error handling and retry logic.
        Pushes ALL variants in a MediaJob to the specified platform.
        """
        job_id = job_data.get("job_id") or job_data.get("variant_external_id")  # Support both parameter names
        product_id = job_data.get("product_id")
        shop_domain = job_data.get("shop_domain")
        user_id = job_data.get("user_id")
        settings = get_settings()
        store_type_override = settings.PLATFORM_PUSH_OVERRIDE
        if store_type_override:
            store_type = store_type_override
            logger.info(f"Using PLATFORM_PUSH_OVERRIDE: {store_type}")
        else:
            store_type = job_data.get("store_type", "example")

        if not all([job_id, product_id, shop_domain]):
            raise ValueError("Missing required parameters: job_id, product_id, shop_domain")

        start_time = datetime.now(timezone.utc)
        logger.info(f"Processing media push job for job {job_id} to {store_type} ({shop_domain})")

        try:
            # Get media service for the platform
            media_service = get_media_service()
            if not media_service:
                raise ValueError(f"No media service available for store type: {store_type}")

            async with self.get_db_session() as db:
                # Get and validate media job by external ID
                from sqlalchemy import select
                from uuid import UUID

                try:
                    job_uuid = UUID(job_id)
                except ValueError:
                    raise ValueError(f"Invalid job ID format: {job_id}")

                result = await db.execute(
                    select(MediaJob)
                    .where(MediaJob.external_id == job_uuid)
                )
                job = result.scalar_one_or_none()
                if not job:
                    raise ValueError(f"Media job {job_id} not found")

                # Check if already pushed
                if job.push_status == "completed":
                    logger.info(f"Job {job_id} already pushed to platform")
                    return {
                        "success": True,
                        "job_id": job_id,
                        "status": "already_pushed"
                    }

                # Check if this is a text job (which doesn't have URLs)
                if job.media_type == "text":
                    logger.info(f"Skipping push for text job {job_id} - text jobs don't have media URLs")
                    return {
                        "success": True,
                        "job_id": job_id,
                        "status": "skipped_text_job",
                        "message": "Text jobs cannot be pushed to platforms"
                    }

                # Check if job has variants
                if not job.variants or len(job.variants) == 0:
                    raise ValueError(f"Media job {job_id} has no variants to push")

                # Update job status to pushing
                job.push_status = "pushing"
                job.push_error_message = None
                await db.commit()

                # Push all variants in the job
                push_results = []
                success_count = 0

                for i, variant in enumerate(job.variants):
                    variant_name = variant.get('filename', f'variant_{i+1}')
                    media_url = variant.get('url') or variant.get('video_url') or variant.get('image_url')

                    if not media_url:
                        logger.warning(f"Variant {variant_name} in job {job_id} has no media URL, skipping")
                        continue

                    logger.info(f"Pushing variant {variant_name} ({i+1}/{len(job.variants)}) for job {job_id}")

                    # Attempt to push this variant with retry logic
                    variant_push_result = await self._push_with_retry(
                        media_service, store_type, shop_domain, product_id,
                        media_url, variant_name, job_data
                    )

                    push_results.append({
                        "variant_name": variant_name,
                        "media_url": media_url,
                        "success": variant_push_result["success"],
                        "media_id": variant_push_result.get("media_id"),
                        "error": variant_push_result.get("error")
                    })

                    if variant_push_result["success"]:
                        success_count += 1
                    else:
                        logger.error(f"Failed to push variant {variant_name}: {variant_push_result.get('error')}")

                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()

                # Determine overall success (at least one variant must succeed)
                overall_success = success_count > 0

                if overall_success:
                    # Update job with success
                    job.push_status = "completed"
                    job.pushed_at = end_time
                    await db.commit()

                    # Record success metrics
                    media_push_duration.labels(
                        platform=store_type,
                        status="success"
                    ).observe(duration)

                    logger.info(f"Successfully pushed {success_count}/{len(job.variants)} variants for job {job_id} to {store_type}")

                    return {
                        "success": True,
                        "job_id": job_id,
                        "variants_pushed": success_count,
                        "total_variants": len(job.variants),
                        "platform": store_type,
                        "duration_seconds": duration,
                        "status": "completed",
                        "results": push_results
                    }
                else:
                    # Update job with failure
                    job.push_status = "failed"
                    job.push_error_message = f"Failed to push any variants: {len(push_results)} attempts"
                    await db.commit()

                    # Record failure metrics
                    media_push_failures.labels(
                        platform=store_type,
                        failure_reason="all_variants_failed"
                    ).inc()

                    logger.error(f"Failed to push any variants for job {job_id} to {store_type}")

                    return {
                        "success": False,
                        "job_id": job_id,
                        "error": "Failed to push any variants",
                        "platform": store_type,
                        "duration_seconds": duration,
                        "status": "failed",
                        "results": push_results
                    }

        except Exception as e:
            logger.exception(f"Critical error in media push job for job {job_id}: {e}")

            # Update job status on critical error
            try:
                async with self.get_db_session() as db:
                    from sqlalchemy import select
                    from uuid import UUID

                    try:
                        job_uuid = UUID(job_id)
                    except ValueError:
                        logger.exception(f"Invalid job ID format in error handler: {job_id}")
                        raise e

                    result = await db.execute(
                        select(MediaJob)
                        .where(MediaJob.external_id == job_uuid)
                    )
                    job = result.scalar_one_or_none()
                    if job:
                        job.push_status = "failed"
                        job.push_error_message = f"Critical error: {str(e)}"
                        await db.commit()
            except Exception as db_error:
                logger.exception(f"Failed to update job status: {db_error}")

            # Record critical failure metrics
            media_push_failures.labels(
                platform=store_type,
                failure_reason="critical_error"
            ).inc()

            raise e

    def _get_media_url(self, job: MediaJob) -> Optional[str]:
        """Extract the first available media URL from job's variants (legacy method)."""
        if not job.variants:
            return None

        # Find the first variant with a media URL
        for variant in job.variants:
            if variant.get('url'):
                return variant['url']
            elif variant.get('video_url'):
                return variant['video_url']
            elif variant.get('image_url'):
                return variant['image_url']

        return None

    async def _push_with_retry(
        self,
        media_service,
        store_type: str,
        shop_domain: str,
        product_id: str,
        media_url: str,
        variant_name: str,
        job_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Push media with retry logic and exponential backoff.

        Returns:
            Push result dictionary
        """
        last_error = None
        last_failure_reason = "unknown"

        for attempt in range(self.max_retries):
            try:
                logger.info(f"Push attempt {attempt + 1}/{self.max_retries} for {media_url}")

                # Prepare push parameters
                push_params = {
                    "store_type": store_type,
                    "shop_domain": shop_domain,
                    "product_id": product_id,
                    "media_url": media_url,
                    "alt_text": job_data.get("alt_text", f"Product media - {variant_name}")
                }

                # Add optional parameters
                if "position" in job_data:
                    push_params["position"] = job_data["position"]

                # Execute push
                push_result = await media_service.push_media_to_product(**push_params)

                if push_result.get("success"):
                    return push_result

                # Handle specific error types
                error_msg = push_result.get("error", "Unknown error")
                last_error = error_msg

                # Check for rate limiting
                if "rate limit" in error_msg.lower() or "429" in error_msg:
                    last_failure_reason = "rate_limited"
                    if attempt < self.max_retries - 1:
                        # Longer delay for rate limiting
                        delay = self.retry_delay_base * (3 ** attempt)
                        logger.warning(f"Rate limited, waiting {delay}s before retry")
                        import asyncio
                        await asyncio.sleep(delay)
                        continue

                # Check for temporary errors
                elif any(keyword in error_msg.lower() for keyword in ["timeout", "connection", "temporary"]):
                    last_failure_reason = "temporary_error"
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delay_base ** attempt
                        logger.warning(f"Temporary error, waiting {delay}s before retry")
                        import asyncio
                        await asyncio.sleep(delay)
                        continue

                # Permanent error - don't retry
                last_failure_reason = "permanent_error"
                break

            except Exception as e:
                last_error = str(e)
                last_failure_reason = "exception"
                logger.exception(f"Push attempt {attempt + 1} failed with exception: {e}")

                if attempt < self.max_retries - 1:
                    delay = self.retry_delay_base ** attempt
                    logger.warning(f"Exception occurred, waiting {delay}s before retry")
                    import asyncio
                    await asyncio.sleep(delay)
                else:
                    break

        return {
            "success": False,
            "error": last_error or "All retry attempts failed",
            "failure_reason": last_failure_reason
        }