import logging
from typing import Dict, Any, Optional, List

from modules.media.schemas import ProviderMediaResult
from modules.media.models import MediaJob, MediaJobStatus

logger = logging.getLogger(__name__)


class MediaGenerateProcessor:
    """Processor for handling media generation tasks."""

    def _is_valid_uuid(self, uuid_string: str) -> bool:
        """Check if a string is a valid UUID format."""
        try:
            from uuid import UUID
            UUID(uuid_string)
            return True
        except (ValueError, TypeError):
            return False

    async def _update_job_with_result(self, db, job_external_id: str, result_data: ProviderMediaResult, status):
        """Update job with generation results using ProviderMediaResult Pydantic model."""
        from sqlalchemy import select
        from modules.media.models import MediaJob
        from datetime import datetime

        # Get the job by external_id
        job_result = await db.execute(select(MediaJob).filter(MediaJob.external_id == job_external_id))
        job = job_result.scalar_one_or_none()
        if not job:
            logger.error(f"Job with external_id {job_external_id} not found")
            return

        logger.debug(f"Updating job {job_external_id} with status {status}")

        # Update job status and timestamps
        job.status = status
        job.updated_at = datetime.now()

        if status.name == "COMPLETED":
            job.completed_at = datetime.now()

            # Use the ProviderMediaResult Pydantic model properties
            provider_variants = result_data.variants or []

            
            variants = []
            for i, provider_variant in enumerate(provider_variants):
                variant_dict = provider_variant.model_dump()
                variant_dict["index"] = i
                variants.append(variant_dict)

                logger.debug(f"Added variant for job {job_external_id}: {variant_dict}")

            job.variants = variants if variants else None

            # Set additional metadata from ProviderMediaResult
            if result_data.quality_score:
                job.quality_score = result_data.quality_score
            if result_data.metadata:
                job.provider_metadata = result_data.metadata

        elif status.name == "FAILED":
            job.error_message = result_data.error_message or "Generation failed"
            logger.debug(f"Set error for job {job_external_id}: {job.error_message}")

        await db.commit()
        logger.debug(f"Finished updating job {job_external_id}")

    async def process(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process media generation task.

        Args:
            task_data: Complete task data including full payload

        Returns:
            Generation results with comprehensive metadata
        """
        from core.db.database import get_session_factory
        from sqlalchemy import select
        from modules.media.service import media_service
        from modules.media.schemas import ProviderMediaRequest
        from datetime import datetime

        session_factory = get_session_factory()
        async with session_factory() as db:
            job = None
            job_id = task_data.get("job_id")
            payload = task_data.get("payload")

            if not job_id:
                return {"job_id": job_id, "status": "failed", "error": "Job ID is required"}

            # Validate job_id format
            if not self._is_valid_uuid(job_id):
                return {"job_id": job_id, "status": "failed", "error": f"Invalid job ID format: {job_id}"}

            try:
                # Retrieve and validate job
                from uuid import UUID
                job_result = await db.execute(select(MediaJob).filter(MediaJob.external_id == UUID(job_id)))
                job = job_result.scalar_one_or_none()

                if not job:
                    return {"job_id": job_id, "status": "failed", "error": f"Job {job_id} not found"}

                db_job_id = job.id

                # Update job status to processing
                logger.debug(f"Setting job {job_id} status to PROCESSING")
                job.status = MediaJobStatus.PROCESSING
                job.started_at = datetime.now()
                await db.commit()
                logger.debug(f"Committed job {job_id} as PROCESSING")

            except Exception as e:
                logger.exception(f"Failed to retrieve/update job {job_id}: {e}")
                if job:
                    try:
                        job.status = MediaJobStatus.FAILED
                        job.ended_at = datetime.now()
                        await db.commit()
                        logger.debug(f"Set job {job_id} to FAILED due to retrieval error")
                    except Exception as db_error:
                        logger.exception(f"Failed to update job status after retrieval error: {db_error}")
                return {"job_id": job_id, "status": "failed", "error": f"Job retrieval failed: {str(e)}"}

            logger.info(f"Starting media generation for job {job_id} (db_id: {db_job_id})")

            try:
                # Generate media
                model = job.resolved_provider
                media_request = ProviderMediaRequest(**payload)
                result = await media_service.generate_media_with_provider(model, media_request)

                if result is None:
                    logger.error(f"Media generation failed for job {job_id}. generate_media_with_provider returned None.")
                    failed_result = ProviderMediaResult(success=False, error_message="Media generation failed")
                    await self._update_job_with_result(db, job_id, failed_result, MediaJobStatus.FAILED)
                    return {"job_id": job_id, "status": "failed", "error": "Media generation failed"}

                if result.success:
                    await self._update_job_with_result(db, job_id, result, MediaJobStatus.COMPLETED)
                    return {"job_id": job_id, "status": "completed", "result": result.model_dump()}
                else:
                    await self._update_job_with_result(db, job_id, result, MediaJobStatus.FAILED)
                    return {"job_id": job_id, "status": "failed", "error": result.error_message}

            except Exception as e:
                logger.exception(f"Media generation failed for job {job_id}: {e}")
                try:
                    failed_result = ProviderMediaResult(success=False, error_message=f"Generation failed: {str(e)}")
                    await self._update_job_with_result(db, job_id, failed_result, MediaJobStatus.FAILED)
                except Exception as update_error:
                    logger.exception(f"Failed to update job status after generation error: {update_error}")
                return {"job_id": job_id, "status": "failed", "error": f"Generation failed: {str(e)}"}