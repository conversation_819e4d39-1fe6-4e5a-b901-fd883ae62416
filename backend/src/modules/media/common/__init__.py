"""
Common utilities for media module.
Contains shared functionality to reduce code duplication across providers and engines.
"""

from .language_utils import get_language_instruction, LANGUAGE_INSTRUCTIONS
from modules.prompts.context_creation import create_product_context, create_brand_context, validate_prompt_request, enrich_prompt_request_with_defaults
from .error_handlers import handle_provider_error, create_error_result, classify_error, should_retry_error, get_retry_delay, create_timeout_error, create_rate_limit_error, log_provider_metrics, validate_provider_response

__all__ = [
    # Language utilities
    "get_language_instruction",
    "LANGUAGE_INSTRUCTIONS",

    # Context creation utilities
    "create_product_context",
    "create_brand_context",
    "validate_request_context",
    "enrich_request_with_defaults",
    "create_provider_context",

    # Error handlers
    "handle_provider_error",
    "create_error_result",
    "classify_error",
    "should_retry_error",
    "get_retry_delay",
    "create_timeout_error",
    "create_rate_limit_error",
    "log_provider_metrics",
    "validate_provider_response"
]