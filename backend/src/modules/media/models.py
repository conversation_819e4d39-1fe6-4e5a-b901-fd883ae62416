"""
ProductVideo platform data models.
Multi-tenant models for video generation, analytics, and billing.
"""

from sqlalchemy import <PERSON>umn, BigInteger, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
from enum import Enum as PyEnum

from core.db.database import Base


class MediaJobStatus(PyEnum):
    """Media job status enumeration."""

    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class PushStatus(PyEnum):
    """Push status enumeration."""

    PENDING = "PENDING"
    PUSHING = "PUSHING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class PlanTier(PyEnum):
    """Billing plan tiers."""

    FREE = "free"
    STARTER = "starter"
    GROWTH = "growth"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class BaseMediaProvider:
    """Base class for media generation providers."""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = None  # httpx.AsyncClient or similar

    async def generate(self, request):
        """Generate media. To be implemented by subclasses."""
        raise NotImplementedError

    async def get_job_status(self, job_id: str):
        """Get job status. To be implemented by subclasses."""
        raise NotImplementedError



class MediaJob(Base):
    """
    Media generation job tracking.
    Each job represents a request to generate media for a product.
    Simplified to contain media URLs directly without variants.
    """

    __tablename__ = "media_jobs"

    id = Column(BigInteger, primary_key=True, index=True)
    external_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True, nullable=False)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False)
    product_external_id = Column(UUID(as_uuid=True), nullable=False)
    status = Column(Enum(MediaJobStatus), default=MediaJobStatus.PENDING, nullable=False)
    media_type = Column(String, nullable=False)  # 'video', 'image', 'text'
    provider = Column(String, nullable=False)  # Requested provider from API
    resolved_provider = Column(String, nullable=True)  # Actual provider used after override resolution

    # Full payload storage for complex requests
    full_payload = Column(JSON, nullable=True)  # Store complete request payload

    items = Column(JSON, nullable=True)  # List of ProductItem as JSON
    shop_id = Column(BigInteger, nullable=True)  # Shop identifier
    celery_task_id = Column(String, nullable=True)  # Celery task ID

    error_message = Column(Text, nullable=True)

    # Storage URLs  - now a list of variants
    variants = Column(JSON, nullable=True)  # List of variants: [{"url": "https://...", "type": "video|image|thumbnail", "filename": "..."}]

    # Provider-specific data 
    provider_media_id = Column(String, nullable=True)
    provider_metadata = Column(JSON, nullable=True)

    # User interaction 
    is_favorite = Column(Boolean, default=False)
    user_rating = Column(Integer, nullable=True)  # 1-5 stars

    # Platform integration 
    push_status = Column(Enum(PushStatus), default=PushStatus.PENDING, nullable=False)
    pushed_at = Column(DateTime(timezone=True), nullable=True)
    push_error_message = Column(Text, nullable=True)

    # Quality and review 
    quality_score = Column(Float, nullable=True)  # AI-generated quality score (0-100)
    needs_manual_review = Column(Boolean, default=False)
    qa_metadata = Column(JSON, nullable=True)

    # Legal and safety 
    brand_safety_checked = Column(Boolean, default=False)
    copyright_validated = Column(Boolean, default=False)
    content_flags = Column(JSON, nullable=True)  # List of content flags

    # Timestamps
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="media_jobs")

    def __repr__(self):
        return f"<MediaJob(id={self.id}, product_external_id='{self.product_external_id}', status='{self.status.value}')>"


