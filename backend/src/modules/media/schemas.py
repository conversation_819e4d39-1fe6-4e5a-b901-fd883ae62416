"""
Media Generation Schemas for E-commerce Media Generation
Simplified schemas that accept prompts generated by the prompts module.
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from enum import Enum

from core.schemas.base_schemas import BaseSchema
from typing import TypeAlias


class MediaType(str, Enum):
    """Media types for generation."""

    IMAGE = "image"
    VIDEO = "video"
    TEXT = "text"


# Provider configuration type alias
ProviderConfig = Dict[str, Any]
"""Provider configuration container for media generation.

This holds provider-specific configuration parameters that are fetched
from the /providers/configs endpoint. The config dict contains all the
parameters needed for a specific provider (temperature, aspect_ratio, etc.).

The frontend should:
1. Call GET /providers/configs to get available provider configurations
2. Select a provider and use its config_schema as a template
3. Pass the configured parameters in the config dict
4. Send this ProviderConfig in MediaItem.provider_config
"""


class MediaItem(BaseModel):
    """Individual media item for generation using prompts from prompts module."""

    product_id: str = Field(..., description="Product identifier")

    # Single prompt field (consolidated from prompt_id, prompt_text, prompt)
    prompt: str = Field(..., description="Prompt text for media generation")

    # Provider configuration for this specific item
    provider_config: Optional[ProviderConfig] = Field(None, description="Provider-specific configuration for this item")

    # Optional reference images
    reference_images: Optional[List[str]] = Field(None, description="Reference images")

    # Optional reference videos
    reference_videos: Optional[List[str]] = Field(None, description="Reference videos")

    # Quantity
    quantity: int = Field(1, description="Quantity of items")


class MediaGenerateRequest(BaseSchema):
    """Simplified request to generate media using prompts from prompts module.

    Provider configuration is now specified per-item in the MediaItem object.
    """

    # Core generation parameters
    media_type: MediaType = Field(..., description="Type of media to generate")

    # Provider selection (optional, can be overridden per-item)
    provider: Optional[str] = Field(None, description="Default provider to use: 'banana', 'veo3', 'gemini', etc.")

    # Single media item with prompt and provider config
    item: MediaItem = Field(..., description="Media item with prompt and provider configuration")

    # Shop context (only for ownership validation)
    shop_id: Optional[int] = Field(None, description="Shop identifier for ownership validation")


class MediaJobInfo(BaseModel):
    """Job information in generate response."""

    product_id: str  # Changed to str to use external_id (UUID)
    job_id: str  # Changed to str to use external_id (UUID)
    status: str
    celery_task_id: Optional[str] = None  # Add Celery task ID


class MediaGenerateResponse(BaseSchema):
    """Response from generate endpoint."""

    jobs: List[MediaJobInfo]


class MediaJobStatusResponse(BaseSchema):
    """Job status response."""

    job_id: str  # Changed to str to use external_id (UUID)
    status: str
    variants: Optional[List[Dict[str, Any]]] = None  # List of variants with metadata
    error_message: Optional[str] = None


class PublishOptions(BaseModel):
    """Options for publishing media."""

    alt_text: Optional[str] = None
    position: Optional[int] = None
    replace_existing: bool = False


class MediaPushRequest(BaseSchema):
    """Request to push media to Store."""

    shop_id: int  # Keep as int since this is a database relationship
    product_id: str  # Changed to str since this is external product ID from e-commerce platform
    job_id: str  # Changed from variant_id to job_id since we're removing variants
    publish_targets: List[str] = ["shopify"]  # e.g. shopify, tiktok, youtube
    publish_options: Optional[PublishOptions] = None


class MediaPushResponse(BaseSchema):
    """Response from push endpoint."""

    success: bool
    results: Optional[Dict[str, Any]] = None
    push_id: str
    status: str
    message: str


class MediaJobListResponse(BaseSchema):
    """Response for listing media jobs."""

    jobs: List[Dict[str, Any]]  # Jobs now include variants list instead of individual URL fields
    total: int
    page: int
    per_page: int


class ProviderMediaRequest(MediaGenerateRequest):
    """Provider-specific request format for media generation."""


class MediaVariant(BaseModel):
    """Standardized media jobs object for all provider types."""

    # Core identification
    job_name: str = Field(..., description="Unique name for this job")
    type: str = Field(..., description="Media type: 'image', 'video', 'text', 'thumbnail'")

    # Media URLs (conditional based on type)
    image_url: Optional[str] = Field(None, description="URL for image media")
    video_url: Optional[str] = Field(None, description="URL for video media")
    thumbnail_url: Optional[str] = Field(None, description="URL for thumbnail/preview")

    # Text content (for text jobs)
    text: Optional[str] = Field(None, description="Generated text content")
    content_type: Optional[str] = Field(None, description="Type of text content (description, caption, etc.)")
    word_count: Optional[int] = Field(None, description="Word count for text content")
    character_count: Optional[int] = Field(None, description="Character count for text content")

    # Video/Image metadata
    duration_seconds: Optional[float] = Field(None, description="Duration for video content")
    resolution: Optional[str] = Field(None, description="Resolution (e.g., '1920x1080')")
    aspect_ratio: Optional[str] = Field(None, description="Aspect ratio (e.g., '16:9')")
    width: Optional[int] = Field(None, description="Width in pixels")
    height: Optional[int] = Field(None, description="Height in pixels")
    file_size_bytes: Optional[int] = Field(None, description="File size in bytes")

    # Generation metadata
    prompt_used: Optional[str] = Field(None, description="The prompt that was used to generate this job")
    style: Optional[str] = Field(None, description="Style applied to the generation")
    style_type: Optional[str] = Field(None, description="Type of style (product_showcase, lifestyle, etc.)")
    target_audience: Optional[str] = Field(None, description="Target audience for the content")
    usage_context: Optional[str] = Field(None, description="Context where content will be used")

    # Provider-specific data
    provider: Optional[str] = Field(None, description="Provider that generated this job")
    provider_job_id: Optional[str] = Field(None, description="Provider-specific job ID")
    provider_metadata: Optional[Dict[str, Any]] = Field(None, description="Provider-specific metadata")

    # Quality and processing
    quality_score: Optional[float] = Field(None, description="AI-generated quality score (0-100)")
    generation_metadata: Optional[Dict[str, Any]] = Field(None, description="Detailed generation metadata")

    # Reference data
    reference_images_used: Optional[int] = Field(None, description="Number of reference images used")
    style_guidance: Optional[str] = Field(None, description="Style guidance applied")


class ProviderMediaResult(BaseModel):
    """Result format from provider media generation."""

    success: bool = Field(..., description="Whether the generation was successful")
    provider_job_id: Optional[str] = Field(None, description="Provider-specific job ID")
    variants: Optional[List[MediaVariant]] = Field(None, description="Generated media variants as standardized objects")
    completion_time: Optional[float] = Field(None, description="Estimated completion time in seconds")
    quality_score: Optional[float] = Field(None, description="Quality score of generated content")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    error_message: Optional[str] = Field(None, description="Error message if generation failed")
    error_code: Optional[str] = Field(None, description="Error code if generation failed")


class ProviderConfigResponse(BaseSchema):
    """Response for provider configuration endpoints."""

    provider_configs: Optional[Dict[str, Dict[str, Any]]] = None
