from datetime import datetime
from typing import Optional, List, Any
from pydantic import BaseModel, field_serializer
from uuid import UUID

from core.schemas.base_schemas import BaseCreateSchema, BaseResponseSchema, BaseUpdateSchema


class CustomerAddressBase(BaseModel):
    """Base customer address schema."""

    external_id: Optional[UUID] = None

    @field_serializer('external_id')
    def serialize_external_id(self, value: Optional[UUID]) -> Optional[str]:
        """Convert UUID to string for JSON serialization."""
        return str(value) if value else None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    company: Optional[str] = None
    address1: Optional[str] = None
    address2: Optional[str] = None
    city: Optional[str] = None
    province: Optional[str] = None
    province_code: Optional[str] = None
    zip: Optional[str] = None
    country: Optional[str] = None
    country_code: Optional[str] = None
    phone: Optional[str] = None


class CustomerAddressResponse(CustomerAddressBase):
    """Customer address response schema."""

    customer_id: int


class CustomerBase(BaseModel):
    """Base customer schema."""

    external_id: UUID

    @field_serializer('external_id')
    def serialize_external_id(self, value: UUID) -> str:
        """Convert UUID to string for JSON serialization."""
        return str(value)
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    accepts_marketing: bool = False
    tax_exempt: bool = False
    verified_email: bool = False

    # Address information
    address1: Optional[str] = None
    address2: Optional[str] = None
    city: Optional[str] = None
    province: Optional[str] = None
    province_code: Optional[str] = None
    zip: Optional[str] = None
    country: Optional[str] = None
    country_code: Optional[str] = None

    # Additional fields
    tags: Optional[str] = None
    note: Optional[str] = None
    total_spent: float = 0.0
    orders_count: int = 0
    state: str = "enabled"


class CustomerCreate(BaseCreateSchema, CustomerBase):
    """Schema for creating a customer."""

    store_id: int
    addresses: Optional[List[CustomerAddressBase]] = None


class CustomerUpdate(BaseUpdateSchema):
    """Schema for updating a customer."""

    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    accepts_marketing: Optional[bool] = None
    tax_exempt: Optional[bool] = None
    verified_email: Optional[bool] = None

    # Address information
    address1: Optional[str] = None
    address2: Optional[str] = None
    city: Optional[str] = None
    province: Optional[str] = None
    province_code: Optional[str] = None
    zip: Optional[str] = None
    country: Optional[str] = None
    country_code: Optional[str] = None

    # Additional fields
    tags: Optional[str] = None
    note: Optional[str] = None
    total_spent: Optional[float] = None
    orders_count: Optional[int] = None
    state: Optional[str] = None


class CustomerResponse(CustomerBase):
    """Customer response schema."""

    store_id: int
    addresses: Optional[List[CustomerAddressResponse]] = None