"""
Video transcoding service for ProductVideo platform.
Handles MP4/HLS transcoding, thumbnail generation, and subtitle creation.
"""

import asyncio
import logging
import os
import subprocess
import tempfile
from typing import List, Optional, Dict, Any
from pathlib import Path

import httpx
from PIL import Image

from core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class VideoTranscodingService:
    """
    Service for video transcoding and processing.
    Handles MP4/HLS conversion, thumbnail generation, and subtitle creation.
    """
    
    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "video_processing"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Check for required tools
        self._check_dependencies()
        
        logger.info("Initialized video transcoding service")
    
    def _check_dependencies(self):
        """Check if required tools are available."""
        required_tools = ['ffmpeg', 'ffprobe']
        
        for tool in required_tools:
            try:
                subprocess.run([tool, '-version'], 
                             capture_output=True, check=True)
                logger.info(f"Found {tool}")
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.warning(f"Missing required tool: {tool}")
    
    async def process_video(
        self,
        video_url: str,
        output_formats: List[str] = None,
        generate_thumbnails: bool = True,
        generate_subtitles: bool = True,
        thumbnail_count: int = 3
    ) -> Dict[str, Any]:
        """
        Process video with transcoding, thumbnails, and subtitles.
        
        Args:
            video_url: URL of the source video
            output_formats: List of output formats ['mp4', 'hls']
            generate_thumbnails: Whether to generate thumbnails
            generate_subtitles: Whether to generate subtitles
            thumbnail_count: Number of thumbnails to generate
            
        Returns:
            Dictionary with processed video information
        """
        if output_formats is None:
            output_formats = ['mp4', 'hls']
        
        try:
            # Download source video
            source_path = await self._download_video(video_url)
            
            # Get video info
            video_info = await self._get_video_info(source_path)
            
            results = {
                'source_info': video_info,
                'outputs': {},
                'thumbnails': [],
                'subtitles': None
            }
            
            # Process each output format
            for format_type in output_formats:
                if format_type == 'mp4':
                    output_path = await self._transcode_to_mp4(source_path)
                    results['outputs']['mp4'] = output_path
                elif format_type == 'hls':
                    output_path = await self._transcode_to_hls(source_path)
                    results['outputs']['hls'] = output_path
            
            # Generate thumbnails
            if generate_thumbnails:
                thumbnails = await self._generate_thumbnails(
                    source_path, thumbnail_count
                )
                results['thumbnails'] = thumbnails
            
            # Generate subtitles (mock implementation)
            if generate_subtitles:
                subtitles = await self._generate_subtitles(source_path)
                results['subtitles'] = subtitles
            
            # Cleanup source file
            os.unlink(source_path)
            
            return results
            
        except Exception as e:
            logger.exception(f"Error processing video {video_url}: {e}")
            raise
    
    async def _download_video(self, video_url: str) -> str:
        """Download video from URL to temporary file."""
        temp_file = self.temp_dir / f"source_{os.urandom(8).hex()}.mp4"
        
        async with httpx.AsyncClient() as client:
            async with client.stream('GET', video_url) as response:
                response.raise_for_status()
                
                with open(temp_file, 'wb') as f:
                    async for chunk in response.aiter_bytes():
                        f.write(chunk)
        
        logger.info(f"Downloaded video to {temp_file}")
        return str(temp_file)
    
    async def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """Get video information using ffprobe."""
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            video_path
        ]
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            raise RuntimeError(f"ffprobe failed: {stderr.decode()}")
        
        import json
        info = json.loads(stdout.decode())
        
        # Extract relevant information
        video_stream = next(
            (s for s in info['streams'] if s['codec_type'] == 'video'),
            None
        )
        
        return {
            'duration': float(info['format']['duration']),
            'size': int(info['format']['size']),
            'width': video_stream['width'] if video_stream else None,
            'height': video_stream['height'] if video_stream else None,
            'codec': video_stream['codec_name'] if video_stream else None,
            'fps': eval(video_stream['r_frame_rate']) if video_stream else None
        }
    
    async def _transcode_to_mp4(self, source_path: str) -> str:
        """Transcode video to MP4 format."""
        output_path = self.temp_dir / f"output_{os.urandom(8).hex()}.mp4"
        
        cmd = [
            'ffmpeg',
            '-i', source_path,
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'aac',
            '-b:a', '128k',
            '-movflags', '+faststart',
            '-y',  # Overwrite output file
            str(output_path)
        ]
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            raise RuntimeError(f"MP4 transcoding failed: {stderr.decode()}")
        
        logger.info(f"Transcoded to MP4: {output_path}")
        return str(output_path)
    
    async def _transcode_to_hls(self, source_path: str) -> str:
        """Transcode video to HLS format."""
        output_dir = self.temp_dir / f"hls_{os.urandom(8).hex()}"
        output_dir.mkdir(exist_ok=True)
        
        playlist_path = output_dir / "playlist.m3u8"
        
        cmd = [
            'ffmpeg',
            '-i', source_path,
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'aac',
            '-b:a', '128k',
            '-hls_time', '10',
            '-hls_playlist_type', 'vod',
            '-hls_segment_filename', str(output_dir / 'segment_%03d.ts'),
            '-y',
            str(playlist_path)
        ]
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            raise RuntimeError(f"HLS transcoding failed: {stderr.decode()}")
        
        logger.info(f"Transcoded to HLS: {playlist_path}")
        return str(playlist_path)
    
    async def _generate_thumbnails(
        self, 
        video_path: str, 
        count: int = 3
    ) -> List[str]:
        """Generate thumbnails from video."""
        thumbnails = []
        
        # Get video duration first
        video_info = await self._get_video_info(video_path)
        duration = video_info['duration']
        
        # Generate thumbnails at evenly spaced intervals
        for i in range(count):
            timestamp = (duration / (count + 1)) * (i + 1)
            thumbnail_path = self.temp_dir / f"thumb_{os.urandom(8).hex()}.jpg"
            
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-ss', str(timestamp),
                '-vframes', '1',
                '-q:v', '2',
                '-y',
                str(thumbnail_path)
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                thumbnails.append(str(thumbnail_path))
                logger.info(f"Generated thumbnail: {thumbnail_path}")
            else:
                logger.warning(f"Failed to generate thumbnail at {timestamp}s: {stderr.decode()}")
        
        return thumbnails
    
    async def _generate_subtitles(self, video_path: str) -> Optional[str]:
        """
        Generate subtitles from video audio.
        This is a mock implementation - in production, you'd use a speech-to-text service.
        """
        try:
            # Extract audio
            audio_path = self.temp_dir / f"audio_{os.urandom(8).hex()}.wav"
            
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-vn',  # No video
                '-acodec', 'pcm_s16le',
                '-ar', '16000',
                '-ac', '1',
                '-y',
                str(audio_path)
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.warning(f"Failed to extract audio: {stderr.decode()}")
                return None
            
            # Mock subtitle generation
            # In production, you'd send the audio to a speech-to-text service
            subtitle_path = self.temp_dir / f"subtitles_{os.urandom(8).hex()}.srt"
            
            mock_subtitles = """1
00:00:00,000 --> 00:00:05,000
Welcome to our amazing product!

2
00:00:05,000 --> 00:00:10,000
This video showcases the key features.

3
00:00:10,000 --> 00:00:15,000
Click below to learn more and purchase.
"""
            
            with open(subtitle_path, 'w') as f:
                f.write(mock_subtitles)
            
            # Cleanup audio file
            os.unlink(audio_path)
            
            logger.info(f"Generated subtitles: {subtitle_path}")
            return str(subtitle_path)
            
        except Exception as e:
            logger.warning(f"Failed to generate subtitles: {e}")
            return None
    
    def cleanup_temp_files(self, file_paths: List[str]):
        """Clean up temporary files."""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    if os.path.isdir(file_path):
                        import shutil
                        shutil.rmtree(file_path)
                    else:
                        os.unlink(file_path)
                    logger.info(f"Cleaned up: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to cleanup {file_path}: {e}")


# Create service instance
video_transcoding_service = VideoTranscodingService()
