"""
Core prompt generation logic.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
import re

from .models import PromptRequest, PromptContext, GeneratedPrompt, PromptTemplate, MediaType, Platform
from .templates import template_manager
from .context_engine import ProductContext, BrandContext, MarketContext, ContentStyle

logger = logging.getLogger(__name__)


class PromptGenerator:
    """Core prompt generation engine."""

    def __init__(self):
        self.style_mappings = self._load_style_mappings()
        self.platform_specs = self._load_platform_specs()

    def _load_style_mappings(self) -> Dict[str, Dict[str, str]]:
        """Load style mappings for different content styles."""
        return {
            "minimalist": {
                "photography": "clean, minimal, white background, professional lighting",
                "video": "clean transitions, minimal text overlay, professional",
                "copy": "concise, clear, direct messaging",
            },
            "luxury": {
                "photography": "premium lighting, elegant composition, sophisticated styling",
                "video": "cinematic quality, smooth transitions, premium feel",
                "copy": "sophisticated tone, premium language, exclusive messaging",
            },
            "modern": {
                "photography": "contemporary styling, dynamic angles, vibrant colors",
                "video": "dynamic cuts, modern graphics, energetic pacing",
                "copy": "contemporary language, trendy references, engaging tone",
            },
            "vintage": {
                "photography": "retro styling, warm tones, classic composition",
                "video": "vintage filters, classic transitions, nostalgic feel",
                "copy": "classic language, timeless appeal, heritage messaging",
            },
        }

    def _load_platform_specs(self) -> Dict[Platform, Dict[str, Any]]:
        """Load platform-specific specifications."""
        return {
            Platform.INSTAGRAM: {
                "aspect_ratios": ["1:1", "4:5", "9:16"],
                "max_caption_length": 2200,
                "hashtag_limit": 30,
                "video_max_duration": 60,
            },
            Platform.TIKTOK: {
                "aspect_ratios": ["9:16"],
                "max_caption_length": 150,
                "video_max_duration": 180,
                "trending_elements": ["trending sounds", "effects", "challenges"],
            },
            Platform.FACEBOOK: {
                "aspect_ratios": ["16:9", "1:1", "4:5"],
                "max_caption_length": 63206,
                "video_max_duration": 240,
            },
            Platform.PINTEREST: {
                "aspect_ratios": ["2:3", "1:1"],
                "max_description_length": 500,
                "pin_title_length": 100,
            },
        }

    async def generate_prompt(self, request: PromptRequest) -> GeneratedPrompt:
        """Generate a comprehensive prompt based on the request."""
        try:
            # Get or select template
            template = self._get_template(request)

            # Create prompt context
            prompt_context = self._create_prompt_context(request)

            # Generate base prompt
            base_prompt = await self._generate_base_prompt(request, template, prompt_context)

            # Apply style modifiers
            style_modifiers = self._get_style_modifiers(request, template, prompt_context)

            # Apply platform optimizations
            platform_optimizations = self._get_platform_optimizations(request, template, prompt_context)

            # Combine all elements
            main_prompt = self._combine_prompt_elements(base_prompt, style_modifiers, platform_optimizations)

            # Generate negative prompt if applicable
            negative_prompt = self._generate_negative_prompt(request, template)

            # Extract technical specs
            technical_specs = self._extract_technical_specs(request, template)

            # Calculate quality score
            quality_score = self._calculate_quality_score(request, template)

            # Extract customizable parts
            customizable_parts = self._extract_customizable_parts(main_prompt, template, request)

            return GeneratedPrompt(
                main_prompt=main_prompt,
                negative_prompt=negative_prompt,
                style_modifiers=style_modifiers,
                technical_specs=technical_specs,
                estimated_quality_score=quality_score,
                target_keywords=self._extract_keywords(request),
                template_id=template.id if template else None,
                customizable_parts=customizable_parts,
            )

        except Exception as e:
            logger.error(f"Failed to generate prompt: {e}")
            raise  # Fail instead of falling back to low-quality content

    def _get_template(self, request: PromptRequest) -> Optional[PromptTemplate]:
        """Get the appropriate template for the request."""
        if request.template_id:
            return template_manager.get_template(request.template_id)

        # Auto-select best template based on media type
        templates = template_manager.get_templates_for_media_type(request.media_type)
        if templates:
            return templates[0]  # Return first available template

        return None

    def _create_prompt_context(self, request: PromptRequest) -> PromptContext:
        """Create prompt context from request."""
        return PromptContext(
            media_type=request.media_type,
            platform=request.platform,
            aspect_ratio=request.aspect_ratio,
            duration_seconds=request.duration_seconds,
            style_preference=request.style_preference,
            campaign_theme=request.campaign_theme,
            call_to_action=request.call_to_action,
        )

    async def _generate_base_prompt(
        self, request: PromptRequest, template: Optional[PromptTemplate], context: PromptContext
    ) -> str:
        """Generate the base prompt."""
        if template and template.base_template:
            # Use template
            base_prompt = template.base_template
        else:
            # Generate default prompt based on media type
            base_prompt = self._get_default_prompt_for_media_type(request.media_type)

        # Replace placeholders
        base_prompt = self._replace_placeholders(base_prompt, request)

        return base_prompt

    def _get_default_prompt_for_media_type(self, media_type: MediaType) -> str:
        """Get default prompt template for media type."""
        defaults = {
            MediaType.PRODUCT_PHOTOGRAPHY: "Professional photograph of {product_title}",
            MediaType.LIFESTYLE_PHOTOGRAPHY: "Lifestyle photograph featuring {product_title}",
            MediaType.PRODUCT_VIDEO: "Professional product video showcasing {product_title}",
            MediaType.LIFESTYLE_VIDEO: "Lifestyle video featuring {product_title}",
            MediaType.SOCIAL_VIDEO: "Engaging social media video about {product_title}",
            MediaType.MARKETING_COPY: "Write compelling marketing copy for {product_title}",
            MediaType.SOCIAL_CAPTION: "Create engaging social media caption for {product_title}",
            MediaType.PRODUCT_DESCRIPTION: "Write detailed product description for {product_title}",
        }
        return defaults.get(media_type, "Create content for {product_title}")

    def _replace_placeholders(self, prompt: str, request: PromptRequest) -> str:
        """Replace placeholders in prompt with actual values."""
        replacements = {
            "product_title": request.product_title,
            "product_description": request.product_description or "",
            "campaign_theme": request.campaign_theme or "",
            "call_to_action": request.call_to_action or "",
        }

        # Add product context fields if available
        if request.product_context:
            replacements.update(
                {
                    "category": getattr(request.product_context, "category", ""),
                    "brand": getattr(request.product_context, "brand", ""),
                    "price": str(getattr(request.product_context, "price", "")),
                    "key_features": ", ".join(getattr(request.product_context, "key_features", [])),
                    "materials": ", ".join(getattr(request.product_context, "materials", [])),
                    "colors": ", ".join(getattr(request.product_context, "colors", [])),
                }
            )

        # Add brand context fields if available
        if request.brand_context:
            replacements.update(
                {
                    "brand_name": getattr(request.brand_context, "name", ""),
                    "brand_voice": getattr(request.brand_context, "brand_voice", ""),
                    "brand_values": ", ".join(getattr(request.brand_context, "brand_values", [])),
                }
            )

        # Replace placeholders
        for key, value in replacements.items():
            prompt = prompt.replace(f"{{{key}}}", str(value))

        return prompt

    def _get_style_modifiers(
        self, request: PromptRequest, template: Optional[PromptTemplate], context: PromptContext
    ) -> List[str]:
        """Get style modifiers for the prompt."""
        modifiers = []

        # Add template style modifiers
        if template and template.style_modifiers:
            modifiers.extend(template.style_modifiers)

        # Add style preference modifiers
        if request.style_preference:
            style_key = request.style_preference.value.lower()
            media_category = self._get_media_category(request.media_type)

            if style_key in self.style_mappings:
                style_modifier = self.style_mappings[style_key].get(media_category, "")
                if style_modifier:
                    modifiers.append(style_modifier)

        return modifiers

    def _get_media_category(self, media_type: MediaType) -> str:
        """Get media category for style mapping."""
        if media_type in [MediaType.PRODUCT_PHOTOGRAPHY, MediaType.LIFESTYLE_PHOTOGRAPHY]:
            return "photography"
        elif media_type in [MediaType.PRODUCT_VIDEO, MediaType.LIFESTYLE_VIDEO, MediaType.SOCIAL_VIDEO]:
            return "video"
        else:
            return "copy"

    def _get_platform_optimizations(
        self, request: PromptRequest, template: Optional[PromptTemplate], context: PromptContext
    ) -> List[str]:
        """Get platform-specific optimizations."""
        optimizations = []

        if not request.platform:
            return optimizations

        # Add template platform optimizations
        if template and template.platform_optimizations:
            platform_opt = template.platform_optimizations.get(request.platform)
            if platform_opt:
                optimizations.append(platform_opt)

        # Add platform-specific requirements
        platform_specs = self.platform_specs.get(request.platform, {})

        if request.platform == Platform.INSTAGRAM:
            optimizations.append("Instagram-optimized, engaging, hashtag-friendly")
        elif request.platform == Platform.TIKTOK:
            optimizations.append("TikTok-style, trendy, short-form content")
        elif request.platform == Platform.PINTEREST:
            optimizations.append("Pinterest-optimized, visually appealing, pin-worthy")

        return optimizations

    def _combine_prompt_elements(
        self, base_prompt: str, style_modifiers: List[str], platform_optimizations: List[str]
    ) -> str:
        """Combine all prompt elements into final prompt."""
        elements = [base_prompt]

        if style_modifiers:
            elements.extend(style_modifiers)

        if platform_optimizations:
            elements.extend(platform_optimizations)

        return ". ".join(filter(None, elements))

    def _generate_negative_prompt(self, request: PromptRequest, template: Optional[PromptTemplate]) -> Optional[str]:
        """Generate negative prompt for image generation."""
        if request.media_type not in [MediaType.PRODUCT_PHOTOGRAPHY, MediaType.LIFESTYLE_PHOTOGRAPHY]:
            return None

        negative_elements = [
            "blurry",
            "low quality",
            "distorted",
            "watermark",
            "text overlay",
            "poor lighting",
            "cluttered background",
        ]

        return ", ".join(negative_elements)

    def _extract_technical_specs(self, request: PromptRequest, template: Optional[PromptTemplate]) -> Dict[str, Any]:
        """Extract technical specifications."""
        specs = {"aspect_ratio": request.aspect_ratio, "media_type": request.media_type.value}

        if request.duration_seconds:
            specs["duration_seconds"] = request.duration_seconds

        if request.platform:
            platform_specs = self.platform_specs.get(request.platform, {})
            specs.update(platform_specs)

        return specs

    def _calculate_quality_score(self, request: PromptRequest, template: Optional[PromptTemplate]) -> float:
        """Calculate estimated quality score."""
        score = 0.5  # Base score

        # Boost for using template
        if template:
            score += 0.2

        # Boost for detailed product context
        if request.product_context:
            score += 0.1

        # Boost for brand context
        if request.brand_context:
            score += 0.1

        # Boost for style preference
        if request.style_preference:
            score += 0.05

        # Boost for platform optimization
        if request.platform:
            score += 0.05

        return min(score, 1.0)

    def _extract_keywords(self, request: PromptRequest) -> List[str]:
        """Extract target keywords from request."""
        keywords = []

        if request.product_title:
            # Extract keywords from product title
            title_words = re.findall(r"\b\w+\b", request.product_title.lower())
            keywords.extend([word for word in title_words if len(word) > 3])

        if request.product_context and hasattr(request.product_context, "key_features"):
            keywords.extend(request.product_context.key_features or [])

        return list(set(keywords))  # Remove duplicates

    def _extract_customizable_parts(
        self, prompt: str, template: Optional[PromptTemplate], request: PromptRequest
    ) -> Dict[str, str]:
        """Extract customizable parts of the prompt."""
        customizable = {}

        if template and template.customizable_fields:
            for field in template.customizable_fields:
                # Extract current value for this field
                if field == "style":
                    customizable["style"] = str(
                        request.style_preference.value if request.style_preference else "default"
                    )
                elif field == "background":
                    customizable["background"] = "professional"  # Default
                elif field == "lighting":
                    customizable["lighting"] = "professional"  # Default
                elif field == "tone":
                    customizable["tone"] = (
                        request.brand_context.brand_voice if request.brand_context else "professional"
                    )

        return customizable



# Global prompt generator instance
prompt_generator = PromptGenerator()
