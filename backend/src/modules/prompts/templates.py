"""
Template management for prompt generation.
"""

import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

from .models import PromptTemplate, MediaType, Platform

logger = logging.getLogger(__name__)


class TemplateManager:
    """Manages prompt templates and template-based generation."""

    def __init__(self):
        self.templates: Dict[str, PromptTemplate] = {}
        self.templates_by_media_type: Dict[MediaType, List[PromptTemplate]] = {}
        self._load_templates()

    def _load_templates(self):
        """Load templates from JSON files."""
        try:
            # Load from the media/prompts directory
            prompts_dir = Path(__file__).parent.parent / "media" / "prompts"

            # Load image templates
            self._load_image_templates(prompts_dir)

            # Load video templates
            self._load_video_templates(prompts_dir)

            # Load text templates
            self._load_text_templates(prompts_dir)

            # Organize templates by media type
            self._organize_templates_by_media_type()

            logger.info(f"Loaded {len(self.templates)} prompt templates")

        except Exception as e:
            logger.error(f"Failed to load templates: {e}")
            self._create_default_templates()

    def _load_image_templates(self, prompts_dir: Path):
        """Load image templates from JSON."""
        image_templates_path = prompts_dir / "image_templates.json"
        if image_templates_path.exists():
            with open(image_templates_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Handle the existing structure
            for category, category_data in data.items():
                if isinstance(category_data, dict):
                    template_id = f"image_{category}"

                    # Extract base template
                    base_template = category_data.get(
                        "base_template", f"Professional {category.replace('_', ' ')} of {{product_title}}"
                    )

                    # Extract style modifiers
                    style_modifiers = []
                    if "style_modifiers" in category_data:
                        style_modifiers = list(category_data["style_modifiers"].values())

                    # Extract quality modifiers
                    if "quality_modifiers" in category_data:
                        style_modifiers.extend(category_data["quality_modifiers"])

                    self.templates[template_id] = PromptTemplate(
                        id=template_id,
                        name=category.replace("_", " ").title(),
                        description=f"Template for {category.replace('_', ' ')}",
                        media_type=MediaType.PRODUCT_PHOTOGRAPHY,
                        base_template=base_template,
                        style_modifiers=style_modifiers,
                        customizable_fields=["style", "background", "lighting"],
                        metadata=category_data,
                    )

    def _load_video_templates(self, prompts_dir: Path):
        """Load video templates from JSON."""
        video_templates_path = prompts_dir / "video_templates.json"
        if video_templates_path.exists():
            with open(video_templates_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            for category, templates in data.get("video_templates", {}).items():
                for template_name, template_data in templates.items():
                    template_id = f"video_{category}_{template_name}"

                    self.templates[template_id] = PromptTemplate(
                        id=template_id,
                        name=template_data.get("name", template_name),
                        description=template_data.get("description", ""),
                        media_type=MediaType.PRODUCT_VIDEO,
                        base_template=template_data.get("base_template", ""),
                        style_modifiers=template_data.get("style_modifiers", []),
                        platform_optimizations=template_data.get("platform_optimizations", {}),
                        customizable_fields=template_data.get("customizable_fields", []),
                        metadata=template_data.get("metadata", {}),
                    )

    def _load_text_templates(self, prompts_dir: Path):
        """Load text templates from JSON."""
        text_templates_path = prompts_dir / "text_templates.json"
        if text_templates_path.exists():
            with open(text_templates_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            for category, templates in data.get("text_templates", {}).items():
                for template_name, template_data in templates.items():
                    template_id = f"text_{category}_{template_name}"

                    self.templates[template_id] = PromptTemplate(
                        id=template_id,
                        name=template_data.get("name", template_name),
                        description=template_data.get("description", ""),
                        media_type=MediaType.MARKETING_COPY,
                        base_template=template_data.get("base_template", ""),
                        style_modifiers=template_data.get("style_modifiers", []),
                        platform_optimizations=template_data.get("platform_optimizations", {}),
                        customizable_fields=template_data.get("customizable_fields", []),
                        metadata=template_data.get("metadata", {}),
                    )

    def _organize_templates_by_media_type(self):
        """Organize templates by media type for easy lookup."""
        for template in self.templates.values():
            if template.media_type not in self.templates_by_media_type:
                self.templates_by_media_type[template.media_type] = []
            self.templates_by_media_type[template.media_type].append(template)

    def _create_default_templates(self):
        """Create default templates if loading fails."""
        # Default image template
        self.templates["default_image"] = PromptTemplate(
            id="default_image",
            name="Default Product Photography",
            description="Basic product photography template",
            media_type=MediaType.PRODUCT_PHOTOGRAPHY,
            base_template="Professional photograph of {product_title}",
            customizable_fields=["style", "background", "lighting"],
        )

        # Default video template
        self.templates["default_video"] = PromptTemplate(
            id="default_video",
            name="Default Product Video",
            description="Basic product video template",
            media_type=MediaType.PRODUCT_VIDEO,
            base_template="Professional product video showcasing {product_title}",
            customizable_fields=["style", "duration", "shots"],
        )

        # Default text template
        self.templates["default_text"] = PromptTemplate(
            id="default_text",
            name="Default Marketing Copy",
            description="Basic marketing copy template",
            media_type=MediaType.MARKETING_COPY,
            base_template="Write compelling marketing copy for {product_title}",
            customizable_fields=["tone", "length", "focus"],
        )

        self._organize_templates_by_media_type()

    def get_template(self, template_id: str) -> Optional[PromptTemplate]:
        """Get a template by ID."""
        return self.templates.get(template_id)

    def get_templates_for_media_type(self, media_type: MediaType) -> List[PromptTemplate]:
        """Get all templates for a specific media type."""
        return self.templates_by_media_type.get(media_type, [])

    def get_all_templates(self) -> List[PromptTemplate]:
        """Get all available templates."""
        return list(self.templates.values())

    def search_templates(self, query: str, media_type: Optional[MediaType] = None) -> List[PromptTemplate]:
        """Search templates by name or description."""
        query_lower = query.lower()
        results = []

        templates_to_search = self.get_templates_for_media_type(media_type) if media_type else self.get_all_templates()

        for template in templates_to_search:
            if query_lower in template.name.lower() or query_lower in template.description.lower():
                results.append(template)

        return results


# Global template manager instance
template_manager = TemplateManager()
