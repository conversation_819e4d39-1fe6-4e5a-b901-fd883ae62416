"""
Prompt Generation Schemas for E-commerce Media Generation
Comprehensive schemas for professional-grade prompt generation for e-commerce stores.
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime

from core.schemas.base_schemas import BaseSchema
from .context_engine import ProductCategory


class TargetAudience(str, Enum):
    """Target audience segments for personalized content."""
    GEN_Z = "gen_z"  # 18-24
    MILLENNIALS = "millennials"  # 25-40
    GEN_X = "gen_x"  # 41-56
    BABY_BOOMERS = "baby_boomers"  # 57+
    LUXURY_BUYERS = "luxury_buyers"
    BUDGET_CONSCIOUS = "budget_conscious"
    EARLY_ADOPTERS = "early_adopters"
    PROFESSIONALS = "professionals"


class ContentStyle(str, Enum):
    """Content style preferences for brand consistency."""
    MINIMALIST = "minimalist"
    LUXURY = "luxury"
    LIFESTYLE = "lifestyle"
    EDITORIAL = "editorial"
    SOCIAL_VIRAL = "social_viral"
    PROFESSIONAL = "professional"
    ARTISTIC = "artistic"
    COMMERCIAL = "commercial"
    PLAYFUL = "playful"
    VINTAGE = "vintage"
    MODERN = "modern"


class UsageContext(str, Enum):
    """Product usage contexts for lifestyle generation."""
    OFFICE = "office"
    OUTDOOR = "outdoor"
    FITNESS = "fitness"
    CASUAL = "casual"
    FORMAL = "formal"
    TRAVEL = "travel"
    HOME = "home"
    PARTY = "party"
    BEACH = "beach"
    WINTER = "winter"
    ECOMMERCE_LISTING = "ecommerce_listing"
    SOCIAL_MEDIA = "social_media"
    ADVERTISING = "advertising"
    WEBSITE_BANNER = "website_banner"
    EMAIL_MARKETING = "email_marketing"
    PRINT_CATALOG = "print_catalog"


class ShopBranding(BaseModel):
    """Shop branding information for consistent media generation."""
    shop_name: str
    brand_voice: Optional[str] = None  # e.g., "professional", "friendly", "luxury"
    color_palette: Optional[List[str]] = None  # Brand colors
    logo_url: Optional[str] = None
    visual_style: Optional[ContentStyle] = None
    brand_values: Optional[List[str]] = None  # e.g., ["sustainable", "quality", "innovation"]
    style_guide: Optional[str] = None  # Style guide description


class ProductContext(BaseModel):
    """Comprehensive product context for professional media generation."""

    # Basic product information
    title: str = Field(..., description="Product title/name", min_length=1)
    description: Optional[str] = Field(None, description="Product description")
    category: Optional[ProductCategory] = Field(None, description="Product category")
    brand: Optional[str] = Field(None, description="Brand name")

    # Physical attributes
    colors: Optional[List[str]] = Field(None, description="Available colors")
    materials: Optional[List[str]] = Field(None, description="Materials used")
    sizes: Optional[List[str]] = Field(None, description="Available sizes")

    # Market positioning
    price: Optional[float] = Field(None, description="Product price", ge=0)
    currency: Optional[str] = Field("USD", description="Price currency")
    price_tier: Optional[str] = Field(None, description="budget, mid, premium, luxury")

    # Target audience and style
    target_audience: Optional[List[TargetAudience]] = Field(None, description="Target customer segments")
    style_keywords: Optional[List[str]] = Field(None, description="Style descriptors")
    usage_context: Optional[List[UsageContext]] = Field(None, description="Usage scenarios")

    # Features and benefits
    key_features: Optional[List[str]] = Field(None, description="Key product features")
    benefits: Optional[List[str]] = Field(None, description="Customer benefits")
    use_cases: Optional[List[str]] = Field(None, description="Use case scenarios")

    # Style preferences
    style_preferences: Optional[ContentStyle] = Field(None, description="Style preferences")

    # Shopify metadata
    shopify_product_id: Optional[str] = Field(None, description="Shopify product ID")
    shopify_tags: Optional[List[str]] = Field(None, description="Shopify product tags")
    shopify_collections: Optional[List[str]] = Field(None, description="Shopify collections")
    shopify_vendor: Optional[str] = Field(None, description="Shopify vendor")

    # Existing media
    existing_images: Optional[List[str]] = Field(None, description="Existing product images")
    existing_videos: Optional[List[str]] = Field(None, description="Existing product videos")


class PromptRequest(BaseSchema):
    """Request to generate prompts for products with comprehensive e-commerce context."""

    # Core generation parameters
    media_type: str = Field(..., description="Type of media to generate prompts for: 'product_photography', 'lifestyle_photography', 'product_video', 'lifestyle_video', 'social_video', 'marketing_copy', 'social_caption', 'product_description'", pattern="^(product_photography|lifestyle_photography|product_video|lifestyle_video|social_video|marketing_copy|social_caption|product_description)$")

    # Product context
    product_context: ProductContext = Field(..., description="Detailed product information")

    # Shop branding
    brand_context: Optional[ShopBranding] = Field(None, description="Shop branding information")

    # Generation preferences
    style_preference: Optional[ContentStyle] = Field(None, description="Style preference")
    aspect_ratio: Optional[str] = Field("1:1", description="Aspect ratio for media")
    locale: Optional[str] = Field("en", description="Language/locale")

    # Template and customization
    template_id: Optional[str] = Field(None, description="Template identifier")
    custom_instructions: Optional[str] = Field(None, description="Custom generation instructions")

    # Platform targeting
    target_platforms: Optional[List[str]] = Field(None, description="Target platforms: instagram, tiktok, facebook, etc.")

    # Campaign context
    campaign_theme: Optional[str] = Field(None, description="Campaign theme or message")
    call_to_action: Optional[str] = Field(None, description="Specific call to action")

    # Idempotency and versioning
    idempotency_key: Optional[str] = Field(None, description="Idempotency key to prevent duplicate generations")
    product_version: Optional[str] = Field(None, description="Product data version hash")

    def generate_idempotency_key(self) -> str:
        """Generate idempotency key based on product data and template."""
        import hashlib
        import json

        key_data = {
            "media_type": self.media_type,
            "product_title": self.product_context.title,
            "template_id": self.template_id,
            "style_preference": self.style_preference.value if self.style_preference else None,
            "aspect_ratio": self.aspect_ratio,
            "locale": self.locale
        }

        # Create hash of key data
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_string.encode()).hexdigest()[:16]


class PromptResponse(BaseSchema):
    """Response from prompt generation."""

    prompt_id: str = Field(..., description="Unique prompt identifier")
    generated_prompt: str = Field(..., description="The generated prompt text")
    media_type: str = Field(..., description="Type of media this prompt is for")
    template_used: Optional[str] = Field(None, description="Template identifier used")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class PromptTemplateInfo(BaseModel):
    """Information about available prompt templates."""

    template_id: str = Field(..., description="Template identifier")
    name: str = Field(..., description="Human-readable template name")
    description: str = Field(..., description="Template description")
    media_type: str = Field(..., description="Media type this template is for")
    customizable_fields: List[str] = Field(..., description="Fields that can be customized")


class PromptTemplatesResponse(BaseSchema):
    """Response listing available prompt templates."""

    templates: List[PromptTemplateInfo] = Field(..., description="List of available templates")
    total: int = Field(..., description="Total number of templates")


class PromptGenerationRequest(BaseSchema):
    """Request to generate multiple prompts."""

    requests: List[PromptRequest] = Field(..., description="List of prompt generation requests")


class PromptGenerationResponse(BaseSchema):
    """Response from batch prompt generation."""

    prompts: List[PromptResponse] = Field(..., description="Generated prompts")
    total_generated: int = Field(..., description="Total number of prompts generated")