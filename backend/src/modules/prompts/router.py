"""
API router for prompt management endpoints.
"""

import logging
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from .models import PromptRequest, PromptResponse, PromptTemplate, MediaType, Platform
from .service import prompt_service
from modules.auth.router import get_current_user
from core.db.database import get_db
from modules.auth.models import User

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/prompt", tags=["prompt"])


class PromptGenerateRequest(BaseModel):
    """API request for prompt generation."""

    product_title: str
    product_description: Optional[str] = None
    media_type: MediaType
    platform: Optional[Platform] = None
    aspect_ratio: str = "1:1"
    duration_seconds: Optional[int] = None
    style_preference: Optional[str] = None
    campaign_theme: Optional[str] = None
    call_to_action: Optional[str] = None
    template_id: Optional[str] = None
    prompt: Optional[str] = None


class PromptCustomizeRequest(BaseModel):
    """API request for prompt customization."""

    original_request: PromptGenerateRequest
    customizations: Dict[str, str]


class TemplateSearchRequest(BaseModel):
    """API request for template search."""

    query: str
    media_type: Optional[MediaType] = None


@router.post("/generate", response_model=PromptResponse)
async def generate_prompt(request: PromptGenerateRequest, current_user: User = Depends(get_current_user)):
    """
    Generate a prompt for media creation.

    This endpoint creates a professional prompt based on the provided parameters.
    The generated prompt can be used directly or customized further.
    """
    try:
        # Convert API request to internal request
        prompt_request = PromptRequest(
            product_title=request.product_title,
            product_description=request.product_description,
            media_type=request.media_type,
            platform=request.platform,
            aspect_ratio=request.aspect_ratio,
            duration_seconds=request.duration_seconds,
            campaign_theme=request.campaign_theme,
            call_to_action=request.call_to_action,
            template_id=request.template_id,
            prompt=request.prompt,
        )

        # Handle style preference conversion
        if request.style_preference:
            from ..media.engines.context_engine import ContentStyle

            try:
                prompt_request.style_preference = ContentStyle(request.style_preference.lower())
            except ValueError:
                logger.warning(f"Invalid style preference: {request.style_preference}")

        # Generate prompt
        response = await prompt_service.generate_prompt(prompt_request)

        return response

    except Exception as e:
        logger.error(f"Failed to generate prompt: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/customize", response_model=PromptResponse)
async def customize_prompt(request: PromptCustomizeRequest, current_user: User = Depends(get_current_user)):
    """
    Customize an existing prompt.

    This endpoint allows users to modify specific aspects of a generated prompt
    while maintaining the overall structure and quality.
    """
    try:
        # Convert API request to internal request
        original_prompt_request = PromptRequest(
            product_title=request.original_request.product_title,
            product_description=request.original_request.product_description,
            media_type=request.original_request.media_type,
            platform=request.original_request.platform,
            aspect_ratio=request.original_request.aspect_ratio,
            duration_seconds=request.original_request.duration_seconds,
            campaign_theme=request.original_request.campaign_theme,
            call_to_action=request.original_request.call_to_action,
            template_id=request.original_request.template_id,
            prompt=request.original_request.prompt,
        )

        # Apply customizations
        response = await prompt_service.customize_prompt(original_prompt_request, request.customizations)

        return response

    except Exception as e:
        logger.error(f"Failed to customize prompt: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates", response_model=List[PromptTemplate])
async def get_templates(
    media_type: Optional[MediaType] = None,
    platform: Optional[Platform] = None,
    current_user: User = Depends(get_current_user),
):
    """
    Get available prompt templates.

    Returns a list of templates that can be used for prompt generation,
    optionally filtered by media type and platform.
    """
    try:
        templates = prompt_service.get_available_templates(media_type, platform)
        return templates

    except Exception as e:
        logger.error(f"Failed to get templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/templates/search", response_model=List[PromptTemplate])
async def search_templates(request: TemplateSearchRequest, current_user: User = Depends(get_current_user)):
    """
    Search for prompt templates.

    Search templates by name, description, or other criteria.
    """
    try:
        templates = prompt_service.search_templates(request.query, request.media_type)
        return templates

    except Exception as e:
        logger.error(f"Failed to search templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates/{template_id}", response_model=PromptTemplate)
async def get_template(template_id: str, current_user: User = Depends(get_current_user)):
    """
    Get a specific template by ID.
    """
    try:
        template = prompt_service.get_template(template_id)
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")

        return template

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get template: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/suggestions")
async def get_prompt_suggestions(
    product_title: str,
    media_type: MediaType,
    platform: Optional[Platform] = None,
    current_user: User = Depends(get_current_user),
):
    """
    Get prompt suggestions for a product and media type.

    Returns a list of suggested prompts that can be used as starting points.
    """
    try:
        suggestions = await prompt_service.get_prompt_suggestions(product_title, media_type, platform)

        return {"suggestions": suggestions}

    except Exception as e:
        logger.error(f"Failed to get suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate")
async def validate_prompt(prompt: str, media_type: MediaType, current_user: User = Depends(get_current_user)):
    """
    Validate a prompt and provide feedback.

    Checks the prompt for common issues and provides suggestions for improvement.
    """
    try:
        validation_result = await prompt_service.validate_prompt(prompt, media_type)
        return validation_result

    except Exception as e:
        logger.error(f"Failed to validate prompt: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/media-types")
async def get_media_types():
    """Get available media types."""
    return {
        "media_types": [
            {"value": media_type.value, "label": media_type.value.replace("_", " ").title()} for media_type in MediaType
        ]
    }


@router.get("/platforms")
async def get_platforms():
    """Get available platforms."""
    return {"platforms": [{"value": platform.value, "label": platform.value.title()} for platform in Platform]}
