"""
Data models for the prompt module.
"""

from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from pydantic import BaseModel, Field

from .context_engine import ProductContext, BrandContext, MarketContext, ContentStyle, TargetAudience


class MediaType(str, Enum):
    """Types of media content."""

    PRODUCT_PHOTOGRAPHY = "product_photography"
    LIFESTYLE_PHOTOGRAPHY = "lifestyle_photography"
    PRODUCT_VIDEO = "product_video"
    LIFESTYLE_VIDEO = "lifestyle_video"
    SOCIAL_VIDEO = "social_video"
    MARKETING_COPY = "marketing_copy"
    SOCIAL_CAPTION = "social_caption"
    PRODUCT_DESCRIPTION = "product_description"


class Platform(str, Enum):
    """Target platforms for content."""

    INSTAGRAM = "instagram"
    TIKTOK = "tiktok"
    FACEBOOK = "facebook"
    PINTEREST = "pinterest"
    YOUTUBE = "youtube"
    WEBSITE = "website"
    EMAIL = "email"
    PRINT = "print"


@dataclass
class PromptContext:
    """Context for prompt generation."""

    media_type: MediaType
    platform: Optional[Platform] = None
    aspect_ratio: str = "1:1"
    duration_seconds: Optional[int] = None
    style_preference: Optional[ContentStyle] = None
    campaign_theme: Optional[str] = None
    call_to_action: Optional[str] = None
    brand_mentions: bool = True
    price_display: bool = False


@dataclass
class GeneratedPrompt:
    """Generated prompt with metadata."""

    main_prompt: str
    negative_prompt: Optional[str] = None
    style_modifiers: List[str] = field(default_factory=list)
    technical_specs: Dict[str, Any] = field(default_factory=dict)
    estimated_quality_score: float = 0.0
    target_keywords: List[str] = field(default_factory=list)
    template_id: Optional[str] = None
    customizable_parts: Dict[str, str] = field(default_factory=dict)


@dataclass
class PromptTemplate:
    """Template for prompt generation."""

    id: str
    name: str
    description: str
    media_type: MediaType
    base_template: str
    style_modifiers: List[str] = field(default_factory=list)
    platform_optimizations: Dict[Platform, str] = field(default_factory=dict)
    customizable_fields: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class PromptRequest(BaseModel):
    """Request for prompt generation."""

    # Product context
    product_title: str = Field(..., description="Product title")
    product_description: Optional[str] = Field(None, description="Product description")
    product_context: Optional[ProductContext] = Field(None, description="Detailed product context")

    # Media context
    media_type: MediaType = Field(..., description="Type of media to generate")
    platform: Optional[Platform] = Field(None, description="Target platform")
    aspect_ratio: str = Field("1:1", description="Aspect ratio")
    duration_seconds: Optional[int] = Field(None, description="Duration for video content")

    # Style and branding
    style_preference: Optional[ContentStyle] = Field(None, description="Content style preference")
    campaign_theme: Optional[str] = Field(None, description="Campaign theme")
    call_to_action: Optional[str] = Field(None, description="Call to action")
    brand_context: Optional[BrandContext] = Field(None, description="Brand context")

    # Template and customization
    template_id: Optional[str] = Field(None, description="Template to use")
    prompt: Optional[str] = Field(None, description="Custom prompt override")
    prompt_customizations: Optional[Dict[str, str]] = Field(None, description="Prompt customizations")

    # Additional context
    target_audience: Optional[List[TargetAudience]] = Field(None, description="Target audience")
    market_context: Optional[MarketContext] = Field(None, description="Market context")


class PromptResponse(BaseModel):
    """Response from prompt generation."""

    generated_prompt: GeneratedPrompt
    template_used: Optional[PromptTemplate] = None
    customizable_parts: Dict[str, str] = Field(default_factory=dict)
    suggestions: List[str] = Field(default_factory=list)
    quality_score: float = Field(0.0, description="Estimated quality score")


class PromptCustomization(BaseModel):
    """Customization options for a prompt."""

    field_name: str = Field(..., description="Name of the field to customize")
    current_value: str = Field(..., description="Current value")
    suggested_values: List[str] = Field(default_factory=list, description="Suggested alternative values")
    description: str = Field("", description="Description of what this field controls")
    field_type: str = Field("text", description="Type of field (text, select, textarea)")
