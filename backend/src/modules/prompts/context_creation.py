"""
Common context creation utilities for prompt generation.
Consolidates duplicate context creation logic across providers.
"""

import logging
from typing import Optional, Any, List

from .context_engine import ProductContext, BrandContext, ContentStyle, TargetAudience, ProductCategory
from .models import PromptRequest

logger = logging.getLogger(__name__)


def create_product_context(request: PromptRequest) -> ProductContext:
    """
    Create product context from request data - common across all providers.

    Args:
        request: Prompt request

    Returns:
        ProductContext instance
    """

    # Use category from request's product_context if available, otherwise default
    category = ProductCategory.FASHION_APPAREL  # Default fallback
    if request.product_context and request.product_context.category:
        category = request.product_context.category

    # Get product context data safely
    product_ctx = request.product_context
    key_features: List[str] = product_ctx.key_features if product_ctx and product_ctx.key_features is not None else []
    materials: List[str] = product_ctx.materials if product_ctx and product_ctx.materials is not None else []
    colors: List[str] = product_ctx.colors if product_ctx and product_ctx.colors is not None else []
    price_tier: str = product_ctx.price_tier if product_ctx and product_ctx.price_tier is not None else "mid"
    style_keywords: List[str] = product_ctx.style_keywords if product_ctx and product_ctx.style_keywords is not None else []

    return ProductContext(
        title=request.product_title,
        description=request.product_description or "",
        price=product_ctx.price if product_ctx and product_ctx.price else None,
        category=category,
        target_audience=request.target_audience or [],
        key_features=key_features,
        materials=materials,
        colors=colors,
        price_tier=price_tier,
        style_keywords=style_keywords,
        use_cases=getattr(request, 'usage_context', None) or []
    )


def create_brand_context(request: PromptRequest) -> Optional['BrandContext']:
    """
    Create brand context from request data - common across all providers.

    Args:
        request: Prompt request

    Returns:
        BrandContext instance or None if no branding provided
    """
    if not request.brand_context:
        return None

    # Map string visual style to enum
    visual_style_str = getattr(request.brand_context, 'visual_style', 'minimalist')
    try:
        visual_style = ContentStyle(visual_style_str.lower())
    except ValueError:
        visual_style = ContentStyle.MINIMALIST

    return BrandContext(
        name=getattr(request.brand_context, 'name', 'Brand'),
        industry="e-commerce",  # Default for media providers
        brand_voice=getattr(request.brand_context, 'brand_voice', 'professional'),
        color_palette=getattr(request.brand_context, 'color_palette', []),
        typography_style="modern",  # Default typography
        visual_style=visual_style,
        brand_values=getattr(request.brand_context, 'brand_values', []),
        target_demographics=request.brand_context.target_demographics if hasattr(request.brand_context, 'target_demographics') else [],
        content_guidelines=getattr(request.brand_context, 'content_guidelines', {})
    )


def validate_prompt_request(request: PromptRequest) -> tuple[bool, Optional[str]]:
    """
    Validate that prompt request has necessary context for generation.

    Args:
        request: Prompt request

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not request.product_title:
        return False, "Product title is required"

    if not request.media_type:
        return False, "Media type is required"

    # Validate media type is supported
    supported_types = ["product_photography", "lifestyle_photography", "product_video", "lifestyle_video", "social_video", "marketing_copy", "social_caption", "product_description"]
    if request.media_type.value not in supported_types:
        return False, f"Unsupported media type: {request.media_type.value}. Supported: {supported_types}"

    # Validate aspect ratio format if provided
    if request.aspect_ratio:
        if not _is_valid_aspect_ratio(request.aspect_ratio):
            return False, f"Invalid aspect ratio format: {request.aspect_ratio}. Expected format: 'width:height'"

    return True, None


def _is_valid_aspect_ratio(aspect_ratio: str) -> bool:
    """
    Validate aspect ratio format.

    Args:
        aspect_ratio: Aspect ratio string (e.g., "16:9", "1:1")

    Returns:
        True if valid format, False otherwise
    """
    if not aspect_ratio:
        return False

    parts = aspect_ratio.split(':')
    if len(parts) != 2:
        return False

    try:
        width, height = int(parts[0]), int(parts[1])
        return width > 0 and height > 0
    except ValueError:
        return False


def enrich_prompt_request_with_defaults(request: PromptRequest) -> PromptRequest:
    """
    Enrich prompt request with default values where missing.

    Args:
        request: Prompt request

    Returns:
        Enriched request
    """
    # Set default aspect ratio
    if not request.aspect_ratio:
        request.aspect_ratio = "1:1"

    # Set default style preference
    if not request.style_preference:
        from .context_engine import ContentStyle
        request.style_preference = ContentStyle.PROFESSIONAL

    return request