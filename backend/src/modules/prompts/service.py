"""
Prompt service - main interface for prompt generation and management.
"""

import logging
from typing import Dict, List, Optional, Any

from .models import (
    PromptRequest,
    PromptResponse,
    PromptTemplate,
    PromptCustomization,
    GeneratedPrompt,
    MediaType,
    Platform,
)
from .generator import prompt_generator
from .templates import template_manager
from .context_engine import ProductContext, BrandContext, MarketContext

logger = logging.getLogger(__name__)


class PromptService:
    """Main service for prompt generation and management."""

    def __init__(self):
        self.generator = prompt_generator
        self.template_manager = template_manager

    async def generate_prompt(self, request: PromptRequest) -> PromptResponse:
        """
        Generate a prompt based on the request.

        This is the main entry point for prompt generation.
        """
        try:
            # Generate the prompt
            generated_prompt = await self.generator.generate_prompt(request)

            # Get template used
            template_used = None
            if generated_prompt.template_id:
                template_used = self.template_manager.get_template(generated_prompt.template_id)

            # Generate customization options
            customizable_parts = self._create_customization_options(generated_prompt, template_used, request)

            # Generate suggestions
            suggestions = await self._generate_suggestions(request, generated_prompt)

            return PromptResponse(
                generated_prompt=generated_prompt,
                template_used=template_used,
                customizable_parts=customizable_parts,
                suggestions=suggestions,
                quality_score=generated_prompt.estimated_quality_score,
            )

        except Exception as e:
            logger.error(f"Failed to generate prompt: {e}")
            # Return fallback response
            fallback_prompt = GeneratedPrompt(
                main_prompt=f"Create {request.media_type.value.replace('_', ' ')} for {request.product_title}",
                estimated_quality_score=0.3,
            )
            return PromptResponse(generated_prompt=fallback_prompt, quality_score=0.3)

    async def customize_prompt(self, original_request: PromptRequest, customizations: Dict[str, str]) -> PromptResponse:
        """
        Apply customizations to a prompt.
        """
        try:
            # Create new request with customizations
            customized_request = self._apply_customizations(original_request, customizations)

            # Generate new prompt
            return await self.generate_prompt(customized_request)

        except Exception as e:
            logger.error(f"Failed to customize prompt: {e}")
            # Fall back to original request
            return await self.generate_prompt(original_request)

    def get_available_templates(
        self, media_type: Optional[MediaType] = None, platform: Optional[Platform] = None
    ) -> List[PromptTemplate]:
        """Get available templates, optionally filtered by media type and platform."""
        if media_type:
            templates = self.template_manager.get_templates_for_media_type(media_type)
        else:
            templates = self.template_manager.get_all_templates()

        # Filter by platform if specified
        if platform:
            filtered_templates = []
            for template in templates:
                if not template.platform_optimizations or platform in template.platform_optimizations:
                    filtered_templates.append(template)
            templates = filtered_templates

        return templates

    def search_templates(self, query: str, media_type: Optional[MediaType] = None) -> List[PromptTemplate]:
        """Search for templates by query."""
        return self.template_manager.search_templates(query, media_type)

    def get_template(self, template_id: str) -> Optional[PromptTemplate]:
        """Get a specific template by ID."""
        return self.template_manager.get_template(template_id)

    async def get_prompt_suggestions(
        self, product_title: str, media_type: MediaType, platform: Optional[Platform] = None
    ) -> List[str]:
        """Get prompt suggestions for a product and media type."""
        suggestions = []

        # Get templates for this media type
        templates = self.get_available_templates(media_type, platform)

        # Generate suggestions from templates
        for template in templates[:3]:  # Limit to top 3
            suggestion = template.base_template.replace("{product_title}", product_title)
            suggestions.append(suggestion)

        return suggestions

    def _create_customization_options(
        self, generated_prompt: GeneratedPrompt, template: Optional[PromptTemplate], request: PromptRequest
    ) -> Dict[str, str]:
        """Create customization options for the UI."""
        customizations = {}

        if template and template.customizable_fields:
            for field in template.customizable_fields:
                if field == "style":
                    customizations["style"] = self._get_style_options(request)
                elif field == "background":
                    customizations["background"] = self._get_background_options()
                elif field == "lighting":
                    customizations["lighting"] = self._get_lighting_options()
                elif field == "tone":
                    customizations["tone"] = self._get_tone_options()

        return customizations

    def _get_style_options(self, request: PromptRequest) -> str:
        """Get style customization options."""
        current_style = request.style_preference.value if request.style_preference else "default"
        return current_style

    def _get_background_options(self) -> str:
        """Get background customization options."""
        return "professional"  # Default

    def _get_lighting_options(self) -> str:
        """Get lighting customization options."""
        return "professional"  # Default

    def _get_tone_options(self) -> str:
        """Get tone customization options."""
        return "professional"  # Default

    async def _generate_suggestions(self, request: PromptRequest, generated_prompt: GeneratedPrompt) -> List[str]:
        """Generate suggestions for improving the prompt."""
        suggestions = []

        # Suggest adding style if not specified
        if not request.style_preference:
            suggestions.append("Consider adding a style preference for better results")

        # Suggest platform optimization if not specified
        if not request.platform:
            suggestions.append("Specify a target platform for optimized content")

        # Suggest brand context if not provided
        if not request.brand_context:
            suggestions.append("Add brand context for more consistent results")

        # Suggest call to action if not provided for marketing content
        if request.media_type in [MediaType.MARKETING_COPY, MediaType.SOCIAL_CAPTION] and not request.call_to_action:
            suggestions.append("Add a call-to-action for better engagement")

        return suggestions

    def _apply_customizations(self, original_request: PromptRequest, customizations: Dict[str, str]) -> PromptRequest:
        """Apply customizations to create a new request."""
        # Create a copy of the original request
        request_dict = original_request.model_dump()

        # Apply customizations
        for field, value in customizations.items():
            if field == "style" and value:
                from .context_engine import ContentStyle

                try:
                    request_dict["style_preference"] = ContentStyle(value.lower())
                except ValueError:
                    pass  # Keep original value
            elif field == "campaign_theme":
                request_dict["campaign_theme"] = value
            elif field == "call_to_action":
                request_dict["call_to_action"] = value
            # Add more customization fields as needed

        return PromptRequest(**request_dict)

    async def validate_prompt(self, prompt: str, media_type: MediaType) -> Dict[str, Any]:
        """Validate a prompt and provide feedback."""
        validation_result = {"is_valid": True, "issues": [], "suggestions": [], "score": 0.8}

        # Basic validation
        if len(prompt.strip()) < 10:
            validation_result["is_valid"] = False
            validation_result["issues"].append("Prompt is too short")
            validation_result["score"] = 0.3

        if len(prompt) > 1000:
            validation_result["issues"].append("Prompt might be too long for some providers")
            validation_result["score"] -= 0.1

        # Media type specific validation
        if media_type in [MediaType.PRODUCT_PHOTOGRAPHY, MediaType.LIFESTYLE_PHOTOGRAPHY]:
            if "photograph" not in prompt.lower() and "image" not in prompt.lower():
                validation_result["suggestions"].append("Consider adding 'photograph' or 'image' for clarity")

        return validation_result


# Global prompt service instance
prompt_service = PromptService()
