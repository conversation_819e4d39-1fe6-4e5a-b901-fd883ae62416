"""
Prompt Module for E-commerce Media Generation.

This module handles all prompt-related functionality including:
- Prompt generation and customization
- Template management
- Context-aware prompt creation
- Platform-specific optimizations
"""

from .models import (
    PromptRequest,
    PromptResponse,
    PromptContext,
    GeneratedPrompt,
    PromptTemplate,
    PromptCustomization
)

from .service import PromptService
from .generator import PromptGenerator
from .templates import TemplateManager

__all__ = [
    "PromptRequest",
    "PromptResponse", 
    "PromptContext",
    "GeneratedPrompt",
    "PromptTemplate",
    "PromptCustomization",
    "PromptService",
    "PromptGenerator",
    "TemplateManager"
]
