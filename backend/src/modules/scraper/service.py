"""
Web scraper service for e-commerce data extraction.
"""

import logging
import uuid
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse
from datetime import datetime

from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from modules.scraper.models import (
    ScrapedDocument, ScrapingJob, ScrapedProduct, ScrapedCollection,
    ScrapingPlatform, ScrapingStatus, JobStatus
)
from modules.scraper.schemas import (
    StartScrapingRequest, ValidateUrlResponse, ScrapedDocumentInfo,
    ScrapingJobInfo, ScrapedProductInfo, ScrapedCollectionInfo,
    ScraperStatsResponse, PlatformInfo
)

logger = logging.getLogger(__name__)


class ScraperService:
    """Service for web scraping operations."""

    async def validate_url(self, url: str) -> ValidateUrlResponse:
        """Validate a URL for scraping."""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            # Basic validation
            if not parsed.scheme or not parsed.netloc:
                return ValidateUrlResponse(
                    valid=False,
                    domain=domain,
                    errors=["Invalid URL format"]
                )
            
            # Check if domain is supported
            platform = await self._detect_platform(domain)
            
            warnings = []
            if not platform:
                warnings.append("Platform not officially supported, using generic scraper")
            
            return ValidateUrlResponse(
                valid=True,
                domain=domain,
                platform=platform,
                estimated_products=None,  # TODO: Implement estimation
                warnings=warnings
            )
            
        except Exception as e:
            logger.exception(f"Error validating URL {url}: {e}")
            return ValidateUrlResponse(
                valid=False,
                domain="",
                errors=[str(e)]
            )

    async def start_scraping(
        self,
        db: AsyncSession,
        workspace_id: int,
        request: StartScrapingRequest
    ) -> Dict[str, str]:
        """Start scraping a URL."""
        try:
            # Validate URL first
            validation = await self.validate_url(request.url)
            if not validation.valid:
                raise ValueError(f"Invalid URL: {validation.errors}")
            
            # Create document
            document_id = str(uuid.uuid4())
            document = ScrapedDocument(
                id=document_id,
                workspace_id=workspace_id,
                url=request.url,
                domain=validation.domain,
                title=f"Scraping {validation.domain}",
                status=ScrapingStatus.PENDING
            )
            db.add(document)
            await db.flush()
            
            # Create job
            job_id = str(uuid.uuid4())
            job = ScrapingJob(
                id=job_id,
                document_id=document_id,
                url=request.url,
                status=JobStatus.QUEUED,
                metadata={
                    "deep_scrape": request.deep_scrape,
                    "max_pages": request.max_pages,
                    "include_variants": request.include_variants,
                    "include_images": request.include_images,
                    "domain": validation.domain,
                    "platform": validation.platform
                }
            )
            db.add(job)
            await db.commit()
            
            # TODO: Queue the scraping job for background processing
            logger.info(f"Queued scraping job {job_id} for URL {request.url}")
            
            return {
                "job_id": job_id,
                "message": "Scraping job queued successfully"
            }
            
        except Exception as e:
            logger.exception(f"Error starting scraping for {request.url}: {e}")
            raise

    async def get_stats(
        self,
        db: AsyncSession,
        workspace_id: int
    ) -> ScraperStatsResponse:
        """Get scraper statistics."""
        try:
            # Get total documents
            total_docs_result = await db.execute(
                select(func.count()).select_from(
                    select(ScrapedDocument).filter(
                        ScrapedDocument.workspace_id == workspace_id
                    ).subquery()
                )
            )
            total_documents = total_docs_result.scalar_one()
            
            # Get total products
            total_products_result = await db.execute(
                select(func.count()).select_from(
                    select(ScrapedProduct).join(ScrapedDocument).filter(
                        ScrapedDocument.workspace_id == workspace_id
                    ).subquery()
                )
            )
            total_products = total_products_result.scalar_one()
            
            # Get unique domains
            domains_result = await db.execute(
                select(ScrapedDocument.domain, func.count().label('count'))
                .filter(ScrapedDocument.workspace_id == workspace_id)
                .group_by(ScrapedDocument.domain)
            )
            domains_data = domains_result.all()
            
            # Get active jobs
            active_jobs_result = await db.execute(
                select(func.count()).select_from(
                    select(ScrapingJob).join(ScrapedDocument).filter(
                        ScrapedDocument.workspace_id == workspace_id,
                        ScrapingJob.status.in_([JobStatus.QUEUED, JobStatus.RUNNING])
                    ).subquery()
                )
            )
            active_jobs = active_jobs_result.scalar_one()
            
            domains = [
                {
                    "domain": domain,
                    "count": count,
                    "last_scraped": datetime.now().isoformat()  # TODO: Get actual last scraped
                }
                for domain, count in domains_data
            ]
            
            return ScraperStatsResponse(
                total_documents=total_documents,
                total_products=total_products,
                total_domains=len(domains),
                active_jobs=active_jobs,
                domains=domains
            )
            
        except Exception as e:
            logger.exception(f"Error getting scraper stats: {e}")
            raise

    async def get_documents(
        self,
        db: AsyncSession,
        workspace_id: int,
        limit: int = 20,
        offset: int = 0,
        domain: Optional[str] = None,
        status: Optional[str] = None,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get scraped documents."""
        try:
            # Build query
            query = select(ScrapedDocument).filter(
                ScrapedDocument.workspace_id == workspace_id
            )
            
            if domain:
                query = query.filter(ScrapedDocument.domain == domain)
            
            if status:
                query = query.filter(ScrapedDocument.status == status)
            
            if search:
                query = query.filter(
                    ScrapedDocument.title.ilike(f"%{search}%") |
                    ScrapedDocument.url.ilike(f"%{search}%")
                )
            
            # Get total count
            total_result = await db.execute(
                select(func.count()).select_from(query.subquery())
            )
            total = total_result.scalar_one()
            
            # Apply pagination
            documents_result = await db.execute(
                query.order_by(ScrapedDocument.created_at.desc())
                .offset(offset)
                .limit(limit)
            )
            documents = documents_result.scalars().all()
            
            items = [
                ScrapedDocumentInfo(
                    id=doc.id,
                    url=doc.url,
                    domain=doc.domain,
                    title=doc.title,
                    status=doc.status,
                    progress=doc.progress,
                    product_count=doc.product_count,
                    collection_count=doc.collection_count,
                    created_at=doc.created_at.isoformat(),
                    updated_at=doc.updated_at.isoformat() if doc.updated_at else doc.created_at.isoformat(),
                    error=doc.error_message
                )
                for doc in documents
            ]
            
            return {
                "items": items,
                "total": total,
                "has_more": offset + len(items) < total
            }
            
        except Exception as e:
            logger.exception(f"Error getting documents: {e}")
            raise

    async def get_active_jobs(
        self,
        db: AsyncSession,
        workspace_id: int
    ) -> Dict[str, List[ScrapingJobInfo]]:
        """Get active scraping jobs."""
        try:
            jobs_result = await db.execute(
                select(ScrapingJob).join(ScrapedDocument).filter(
                    ScrapedDocument.workspace_id == workspace_id,
                    ScrapingJob.status.in_([JobStatus.QUEUED, JobStatus.RUNNING])
                ).order_by(ScrapingJob.created_at.desc())
            )
            jobs = jobs_result.scalars().all()
            
            items = [
                ScrapingJobInfo(
                    id=job.id,
                    url=job.url,
                    status=job.status,
                    progress=job.progress,
                    started_at=job.started_at.isoformat() if job.started_at else None,
                    completed_at=job.completed_at.isoformat() if job.completed_at else None,
                    error=job.error_message,
                    metadata=job.metadata
                )
                for job in jobs
            ]
            
            return {"items": items}
            
        except Exception as e:
            logger.exception(f"Error getting active jobs: {e}")
            raise

    async def get_supported_platforms(
        self,
        db: AsyncSession
    ) -> List[PlatformInfo]:
        """Get supported scraping platforms."""
        try:
            platforms_result = await db.execute(
                select(ScrapingPlatform).filter(ScrapingPlatform.is_active == True)
            )
            platforms = platforms_result.scalars().all()
            
            return [
                PlatformInfo(
                    name=platform.name,
                    domains=platform.domains,
                    features=platform.features,
                    limitations=platform.limitations
                )
                for platform in platforms
            ]
            
        except Exception as e:
            logger.exception(f"Error getting supported platforms: {e}")
            # Return default platforms if database query fails
            return [
                PlatformInfo(
                    name="Shopify",
                    domains=["myshopify.com", "shopify.com"],
                    features=["Products", "Collections", "Variants", "Images"],
                    limitations=["Rate limited", "Requires public access"]
                ),
                PlatformInfo(
                    name="WooCommerce",
                    domains=["*"],
                    features=["Products", "Categories"],
                    limitations=["Generic scraper", "May require custom selectors"]
                )
            ]

    async def _detect_platform(self, domain: str) -> Optional[str]:
        """Detect the e-commerce platform for a domain."""
        if "shopify" in domain:
            return "shopify"
        elif "woocommerce" in domain:
            return "woocommerce"
        # Add more platform detection logic here
        return None


# Create singleton instance
scraper_service = ScraperService()
