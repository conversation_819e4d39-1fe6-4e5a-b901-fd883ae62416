"""
E-commerce Media Generation and Processing Celery Tasks.

This module contains all tasks related to generating and processing
professional media content for e-commerce stores including images, videos, and text.
Integrates with context engine, prompt engine, and provider system for high-quality results.
"""

import logging
from typing import Dict, Any

from servers.worker.celery_app import celery_app
from servers.worker.base_task import LoggedTask
from modules.media.processors import MediaGenerateProcessor, MediaPushProcessor, MediaCleanupProcessor

logger = logging.getLogger(__name__)


@celery_app.task(name="media.generate_media", bind=True, max_retries=3)
def generate_media(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate professional e-commerce media using comprehensive AI pipeline.

    This task handles the complete media generation workflow including:
    - Product context analysis and enrichment
    - Professional prompt generation for different media types
    - Multi-provider generation with fallback support
    - Quality assessment and optimization
    - Storage management

    Args:
        task_data: Complete task data including full payload

    Returns:
        Generation results with comprehensive metadata
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    # In Celery forked workers, we need to ensure we create a fresh event loop
    try:
        processor = MediaGenerateProcessor()
        return asyncio.run(processor.process(task_data))
    except Exception as e:
        logger.exception(f"Error generating media for task_data: {task_data}. Error: {e}", exc_info=True)
        raise




@celery_app.task(name="media.push_to_platform", bind=True, max_retries=3)
def push_to_platform(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Push generated media to platform store.

    This task handles pushing completed media variants to connected platform stores.
    It uses the media push processor for robust error handling and retry logic.

    Args:
        task_data: Dictionary containing task parameters including:
            - job_id: Media job ID to push
            - store_id: Store ID to push to
            - user_id: User ID (optional)
            - product_id: Product ID (optional)

    Returns:
        Push result with status and metadata
    """
    processor = MediaPushProcessor()
    return processor.process(task_data)




@celery_app.task(name="media.cleanup_old_media")
def cleanup_old_media(days_old: int = 90):
    """
    Clean up old generated media files to save storage space.

    Args:
        days_old: Number of days old media to keep
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    # In Celery forked workers, we need to ensure we create a fresh event loop
    processor = MediaCleanupProcessor()
    return asyncio.run(processor.process(days_old))


