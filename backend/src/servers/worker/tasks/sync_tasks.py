"""
Sync-related Celery tasks.

This module contains all tasks related to data synchronization between
Shopify (via Airbyte) and the production database.
"""

import logging
import json
import os
from typing import List, Optional, Dict, Any
from celery import Task

from servers.worker.celery_app import celery_app
from servers.worker.base_task import LoggedTask

logger = logging.getLogger(__name__)

# Load configuration from config.json
CONFIG_PATH = os.path.join(os.path.dirname(__file__), '..', 'config.json')
with open(CONFIG_PATH, 'r') as f:
    CONFIG = json.load(f)

def get_platform_config(platform: str) -> Dict[str, Any]:
    """
    Get platform-specific configuration with defaults fallback.

    Args:
        platform: Platform name (e.g., 'shopify')

    Returns:
        Dict with platform config merged with defaults
    """
    defaults = CONFIG.get('defaults', {})
    platform_config = CONFIG.get('platforms', {}).get(platform, {})

    # Merge platform config with defaults
    config = defaults.copy()
    config.update(platform_config)

    return config


def get_production_table_name(entity_type: str) -> str:
    """Map Airbyte entity types to production table names."""
    table_mapping = {
        'products': 'products',
        'product_variants': 'product_variants',
        'product_images': 'product_images',
        'inventory_levels': 'inventory_levels',
        # Metafields are stored as JSON on their parent tables, not as separate records
        # So we don't need production table mapping for missing records checks
    }
    return table_mapping.get(entity_type, entity_type)


@celery_app.task(name='sync.bulk_sync_store', base=LoggedTask)
def bulk_sync_store(
    store_id: str,  # Now expects external_id (string)
    entity_types: Optional[List[str]] = None,
    triggered_by: str = 'manual',
    webhook_event_id: Optional[str] = None  # Also external_id
) -> Dict[str, Any]:
    """
    New simplified bulk sync task that replaces the complex batch system.

    This task uses database-level locking and bulk SQL operations for 100% reliability.

    Args:
        store_id: Store external_id to sync (string UUID)
        entity_types: List of entity types to sync (optional)
        triggered_by: What triggered the sync
        webhook_event_id: Optional webhook event external_id

    Returns:
        Sync results dictionary
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    return asyncio.run(_bulk_sync_store_async(store_id, entity_types, triggered_by, webhook_event_id))


async def _bulk_sync_store_async(
    store_id: str,
    entity_types: Optional[List[str]] = None,
    triggered_by: str = 'manual',
    webhook_event_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Async implementation of bulk sync store to avoid event loop conflicts.
    """
    from core.db.database import get_session_factory
    from modules.sync.processors.bulk_sync_processor import BulkSyncProcessor
    from sqlalchemy import text

    # Validate store_id is a string (external_id)
    if not isinstance(store_id, str):
        logger.error(f"Invalid store_id type: {type(store_id)} (expected string external_id)")
        return {
            'status': 'failed',
            'error': f'Invalid store_id type: {type(store_id)} (expected string external_id)',
            'store_id': store_id
        }

    logger.info(f"Starting bulk sync for store {store_id}, triggered by {triggered_by}")

    session_factory = get_session_factory()
    async with session_factory() as db:
        lock_id = None
        try:
            # Use PostgreSQL advisory lock to prevent concurrent syncs
            # Convert store_id to hash if it's a string
            lock_id = hash(f"store_{store_id}") % 1000000
            lock_result = await db.execute(text("SELECT pg_try_advisory_lock(:lock_id)"), {"lock_id": lock_id})
            lock_acquired = lock_result.scalar()

            if not lock_acquired:
                logger.warning(f"Could not acquire sync lock for store {store_id} - sync already in progress")
                return {
                    'status': 'skipped',
                    'message': 'Sync already in progress for this store',
                    'store_id': store_id
                }

            # Execute bulk sync using the new processor
            processor = BulkSyncProcessor()
            result = await processor.sync_all_entities(db, store_id, entity_types)

            logger.info(f"Bulk sync completed for store {store_id}: {result['status']}")
            return result

        except Exception as e:
            logger.exception(f"Bulk sync failed for store {store_id}: {e}", exc_info=True)
            # Rollback the transaction to clean up aborted state
            await db.rollback()
            raise
        finally:
            # Always release the lock if it was acquired
            if lock_id is not None:
                try:
                    await db.execute(text("SELECT pg_advisory_unlock(:lock_id)"), {"lock_id": lock_id})
                except Exception as unlock_error:
                    logger.warning(f"Failed to release sync lock for store {store_id}: {unlock_error}")


@celery_app.task(name='sync.check_sync_status', base=LoggedTask)
def check_sync_status(store_id: str) -> Dict[str, Any]:
    """
    Check if sync is currently running for a store.

    Args:
        store_id: Store external_id to check (string UUID)

    Returns:
        Sync status information
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    return asyncio.run(_check_sync_status_async(store_id))


async def _check_sync_status_async(store_id: str) -> Dict[str, Any]:
    """
    Async implementation of check sync status to avoid event loop conflicts.
    """
    from core.db.database import get_session_factory
    from modules.stores.models import Store
    from sqlalchemy import text, select

    session_factory = get_session_factory()
    async with session_factory() as db:
        try:
            # Convert external_id to database ID for lock operations
            store_result = await db.execute(select(Store).filter(Store.external_id == store_id))
            store = store_result.scalar_one_or_none()
            if not store:
                logger.error(f"Store {store_id} not found")
                return {
                    'store_id': store_id,
                    'error': f'Store {store_id} not found',
                    'sync_in_progress': False,
                    'lock_available': False
                }

            store_db_id = store.id
            lock_id = None

            # Check if sync lock is available using database ID for the lock
            lock_id = hash(f"store_{store_db_id}") % 1000000
            lock_result = await db.execute(text("SELECT pg_try_advisory_lock(:lock_id)"), {"lock_id": lock_id})
            lock_available = lock_result.scalar()

            if lock_available:
                # Release the lock immediately since we were just checking
                try:
                    await db.execute(text("SELECT pg_advisory_unlock(:lock_id)"), {"lock_id": lock_id})
                except Exception as unlock_error:
                    logger.warning(f"Failed to release sync lock for store {store_id}: {unlock_error}")

            status = {
                'store_id': store_id,  # Return the external_id in the response
                'sync_in_progress': not lock_available,
                'lock_available': lock_available,
                'active_job': None  # Could be enhanced to check actual job status
            }

            logger.info(f"Sync status for store {store_id}: {status}")
            return status

        except Exception as e:
            logger.exception(f"Error checking sync status for store {store_id}: {e}", exc_info=True)
            # Rollback the transaction to clean up aborted state
            await db.rollback()
            raise
        finally:
            # Always release the lock if it was acquired and not already released
            if lock_id is not None:
                try:
                    await db.execute(text("SELECT pg_advisory_unlock(:lock_id)"), {"lock_id": lock_id})
                except Exception as unlock_error:
                    logger.warning(f"Failed to release sync lock for store {store_id}: {unlock_error}")


@celery_app.task(name='sync.monitor_scheduled_syncs', base=LoggedTask)
def monitor_scheduled_syncs():
    """
    Monitor for new data in Airbyte tables and trigger syncs as needed.

    This task runs periodically to check for new data that needs to be synced
    from Airbyte staging tables to production tables.
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    return asyncio.run(_monitor_scheduled_syncs_async())


async def _monitor_scheduled_syncs_async():
    """
    Async implementation of monitor scheduled syncs to avoid event loop conflicts.
    """
    from core.db.database import get_session_factory
    # Import all models to ensure relationships are properly configured
    from core.db import models  # noqa: F401
    from modules.stores.models import Store
    from sqlalchemy import create_engine, text, select
    from core.config import get_settings

    logger.info("Starting scheduled sync monitoring")

    session_factory = get_session_factory()
    settings = get_settings()

    async with session_factory() as db:
        try:
            # Get all active stores with Airbyte connections
            stores_result = await db.execute(
                select(Store).filter(
                    Store.is_active == True,
                    Store.platform == 'shopify'
                )
            )
            stores = stores_result.scalars().all()

            if not stores:
                logger.info("No active Shopify stores found")
                return

            # Create Airbyte database connection
            airbyte_url = (
                f"postgresql://{settings.AIRBYTE_DATABASE_USER}:"
                f"{settings.AIRBYTE_DATABASE_PASSWORD}@"
                f"{settings.AIRBYTE_DATABASE_HOST}:"
                f"{settings.AIRBYTE_DATABASE_PORT}/"
                f"{settings.AIRBYTE_DATABASE_NAME}"
            )

            airbyte_engine = create_engine(airbyte_url)

            # Check each store for new data
            for store in stores:
                try:
                    with airbyte_engine.connect() as airbyte_conn:
                        # Get platform-specific config
                        platform_config = get_platform_config('shopify')
                        entity_types = platform_config['entity_types']

                        for entity_type in entity_types:
                            try:
                                # Get checkpoint for last successful sync time
                                from modules.sync.models import SyncCheckpoint
                                checkpoint_result = await db.execute(
                                    select(SyncCheckpoint).filter(
                                        SyncCheckpoint.store_id == store.id,
                                        SyncCheckpoint.entity_type == entity_type
                                    )
                                )
                                checkpoint = checkpoint_result.scalar_one_or_none()
                                last_sync_time = checkpoint.last_successful_sync_at if checkpoint else None

                                shop_url = store.shop_domain.replace('.myshopify.com', '') if store.shop_domain else store.shop_name or f"store-{store.id}"

                                needs_sync = False
                                reasons = []

                                # Check if table exists (outside try block to avoid aborted transaction)
                                table_exists = False
                                try:
                                    table_exists_query = """
                                        SELECT EXISTS (
                                            SELECT 1 FROM information_schema.tables
                                            WHERE table_schema = 'public' AND table_name = :table_name
                                        )
                                    """
                                    table_exists = airbyte_conn.execute(text(table_exists_query), {'table_name': entity_type}).scalar()
                                except Exception as schema_error:
                                    if "aborted" in str(schema_error).lower():
                                        logger.warning(f"Transaction already aborted, cannot check table {entity_type} existence, skipping")
                                        continue
                                    else:
                                        logger.warning(f"Error checking table existence for {entity_type}: {schema_error}, skipping")
                                        continue

                                if not table_exists:
                                    logger.warning(f"Table {entity_type} does not exist in Airbyte database, skipping")
                                    continue

                                try:
                                    # Check for records newer than last successful sync
                                    if last_sync_time:
                                        if entity_type.startswith('metafield_'):
                                            # Metafield tables don't have shop_url column
                                            stale_query = f"""
                                                SELECT COUNT(*) FROM public.{entity_type}
                                                WHERE _airbyte_extracted_at > :last_sync_time
                                            """
                                            stale_count = airbyte_conn.execute(text(stale_query), {
                                                'last_sync_time': last_sync_time
                                            }).scalar()
                                        else:
                                            # All other tables have shop_url column
                                            stale_query = f"""
                                                SELECT COUNT(*) FROM public.{entity_type}
                                                WHERE _airbyte_extracted_at > :last_sync_time
                                                AND shop_url = :shop_url
                                            """
                                            stale_count = airbyte_conn.execute(text(stale_query), {
                                                'last_sync_time': last_sync_time,
                                                'shop_url': shop_url
                                            }).scalar()

                                        if stale_count > 0:
                                            needs_sync = True
                                            reasons.append(f"{stale_count} records newer than last sync")

                                    # Check for missing records in production (skip for metafields since they aggregate)
                                    missing_count = 0
                                    if not entity_type.startswith('metafield_'):
                                        # First, get count of records in Airbyte for this store
                                        airbyte_count_query = f"""
                                            SELECT COUNT(*) FROM public.{entity_type}
                                            WHERE shop_url = :shop_url
                                        """
                                        airbyte_count = airbyte_conn.execute(text(airbyte_count_query), {
                                            'shop_url': shop_url
                                        }).scalar()

                                        debug_shop_urls_query = f"""
                                            SELECT DISTINCT shop_url FROM public.{entity_type}
                                            WHERE shop_url IS NOT NULL
                                            LIMIT 10
                                        """
                                        try:
                                            shop_urls_result = airbyte_conn.execute(text(debug_shop_urls_query))
                                            actual_shop_urls = [row[0] for row in shop_urls_result.fetchall()]
                                            logger.debug(f"{entity_type} - Expected shop_url: '{shop_url}', Actual shop_urls in Airbyte: {actual_shop_urls}")
                                        except Exception as debug_error:
                                            logger.warning(f"Could not check shop_urls for {entity_type}: {debug_error}")

                                        # Then check how many exist in production
                                        production_table = get_production_table_name(entity_type)

                                        # Use the same count query logic as the syncer
                                        if production_table == 'products':
                                            production_count_query = f"SELECT COUNT(*) FROM {production_table} WHERE store_id = :store_id"
                                        elif production_table == 'product_images':
                                            production_count_query = f"""
                                                SELECT COUNT(*)
                                                FROM {production_table} pi
                                                JOIN products p ON pi.product_id = p.id
                                                WHERE p.store_id = :store_id
                                            """
                                        elif production_table == 'product_variants':
                                            production_count_query = f"""
                                                SELECT COUNT(*)
                                                FROM {production_table} pv
                                                JOIN products p ON pv.product_id = p.id
                                                WHERE p.store_id = :store_id
                                            """
                                        elif production_table == 'inventory_levels':
                                            production_count_query = f"""
                                                SELECT COUNT(*)
                                                FROM {production_table} il
                                                JOIN product_variants pv ON il.inventory_item_id = pv.inventory_item_id
                                                JOIN products p ON pv.product_id = p.id
                                                WHERE p.store_id = :store_id
                                            """
                                        else:
                                            production_count_query = f"SELECT COUNT(*) FROM {production_table} WHERE store_id = :store_id"

                                        production_result = await db.execute(text(production_count_query), {
                                            'store_id': store.id
                                        })
                                        production_count = production_result.scalar() or 0

                                        logger.debug(f"{entity_type} missing check - Airbyte: {airbyte_count}, Production: {production_count}, Shop: {shop_url}, Store: {store.id}")

                                        # If Airbyte has records but production has fewer, we have missing data
                                        if airbyte_count > 0 and production_count < airbyte_count:
                                            missing_count = airbyte_count - production_count
                                            needs_sync = True
                                            reasons.append(f"{missing_count} missing records")
                                            logger.debug(f"Triggering {entity_type} sync due to {missing_count} missing records")
                                        elif airbyte_count > 0 and production_count == 0:
                                            # Special case: if production has 0 records but Airbyte has some, always sync
                                            needs_sync = True
                                            reasons.append(f"no records in production but {airbyte_count} in Airbyte")
                                            logger.debug(f"Triggering {entity_type} sync - no production data but Airbyte has {airbyte_count} records")
                                        elif airbyte_count == 0 and production_count == 0:
                                            # Check if there are ANY records in Airbyte for this entity type (wrong shop_url filter?)
                                            total_airbyte_query = f"SELECT COUNT(*) FROM public.{entity_type}"
                                            total_airbyte_count = airbyte_conn.execute(text(total_airbyte_query)).scalar()
                                            if total_airbyte_count > 0:
                                                logger.debug(f"{entity_type} has {total_airbyte_count} total records in Airbyte but 0 with shop_url filter - possible shop_url mismatch")
                                                # For now, don't trigger sync as it might be wrong shop_url, but log the issue

                                    # For first-time syncs (no checkpoint)
                                    if not checkpoint:
                                        if entity_type.startswith('metafield_'):
                                            # Metafield tables don't have shop_url column
                                            total_airbyte_query = f"SELECT COUNT(*) FROM public.{entity_type}"
                                            total_count = airbyte_conn.execute(text(total_airbyte_query)).scalar()
                                        else:
                                            # All other tables have shop_url column
                                            total_airbyte_query = f"SELECT COUNT(*) FROM public.{entity_type} WHERE shop_url = :shop_url"
                                            total_count = airbyte_conn.execute(text(total_airbyte_query), {'shop_url': shop_url}).scalar()

                                        if total_count > 0:
                                            needs_sync = True
                                            reasons.append(f"first-time sync needed ({total_count} records)")

                                except Exception as query_error:
                                    # If transaction is aborted, log and skip this entity
                                    if "aborted" in str(query_error).lower():
                                        logger.warning(f"Transaction aborted for {entity_type}, skipping: {query_error}")
                                        continue
                                    else:
                                        raise

                                if needs_sync:
                                    logger.info(f"Triggering sync for {entity_type}: {', '.join(reasons)}")
                                    bulk_sync_store.delay(str(store.external_id), [entity_type], 'scheduled')

                            except Exception as e:
                                logger.warning(f"Error checking {entity_type} for store {store.id}: {e}")
                                # Rollback transaction to clean up aborted state
                                try:
                                    await db.rollback()
                                except Exception as rollback_error:
                                    logger.warning(f"Could not rollback transaction: {rollback_error}")
                                continue

                except Exception as e:
                    logger.warning(f"Could not connect to Airbyte database for store {store.id}: {e}")
                    continue

            logger.info("Completed scheduled sync monitoring")

        except Exception as e:
            logger.exception(f"Error in scheduled sync monitoring: {e}", exc_info=True)
            raise


@celery_app.task(name='sync.poll_airbyte_job_status', base=LoggedTask, bind=True)
def poll_airbyte_job_status(self, airbyte_job_id: int, store_id: str, entity_type: str = None, poll_count: int = 0):
    """
    Poll Airbyte job status and update sync statistics when complete.

    Args:
        airbyte_job_id: Airbyte job ID to poll
        store_id: Store external_id for the sync (string UUID)
        entity_type: Entity type being synced (optional)
        poll_count: Number of times we've polled this job (for rate limiting)
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    return asyncio.run(_poll_airbyte_job_status_async(airbyte_job_id, store_id, entity_type, poll_count))


async def _poll_airbyte_job_status_async(airbyte_job_id: int, store_id: str, entity_type: str = None, poll_count: int = 0):
    """
    Async implementation of poll airbyte job status to avoid event loop conflicts.
    """
    from core.db.database import get_session_factory
    from modules.sync.airbyte_service import AirbyteService
    from modules.sync.models import SyncJob, SyncCheckpoint
    from modules.stores.models import Store
    from datetime import datetime, timezone
    from sqlalchemy import select, update

    logger.info(f"Polling Airbyte job {airbyte_job_id} status for store {store_id} (poll #{poll_count})")

    session_factory = get_session_factory()
    airbyte_service = AirbyteService()

    async with session_factory() as db:
        try:
            # Convert external_id to database ID
            store_result = await db.execute(select(Store).filter(Store.external_id == store_id))
            store = store_result.scalar_one_or_none()
            if not store:
                logger.error(f"Store {store_id} not found")
                return {'status': 'error', 'message': f'Store {store_id} not found'}

            store_db_id = store.id

            # Get job details from Airbyte
            job_details = airbyte_service.get_job_details(airbyte_job_id)

            if not job_details:
                logger.warning(f"Could not get job details for Airbyte job {airbyte_job_id}")
                return {'status': 'error', 'message': 'Could not get job details'}

            job_data = job_details.get('job', {})
            job_status = job_data.get('status')

            # Update SyncJob record if it exists
            sync_job_result = await db.execute(
                select(SyncJob).filter(SyncJob.airbyte_job_id == airbyte_job_id)
            )
            sync_job = sync_job_result.scalar_one_or_none()

            if sync_job:
                sync_job.status = job_status
                if job_status in ['succeeded', 'failed']:
                    sync_job.finished_at = datetime.now(timezone.utc)
                    sync_job.duration_seconds = (
                        sync_job.finished_at - sync_job.started_at
                    ).total_seconds() if sync_job.started_at else None

                    # Get sync stats from Airbyte
                    sync_stats = airbyte_service.get_job_sync_stats(airbyte_job_id)
                    if sync_stats:
                        sync_job.records_processed = sync_stats.get('total_records_committed', 0)
                        logger.info(f"Updated SyncJob {sync_job.id} with {sync_job.records_processed} records processed")

            # Update SyncCheckpoint if we have sync stats
            if job_status in ['succeeded', 'failed']:
                sync_stats = airbyte_service.get_job_sync_stats(airbyte_job_id)
                if sync_stats:
                    # Find the relevant checkpoint
                    checkpoint_query = select(SyncCheckpoint).filter(
                        SyncCheckpoint.store_id == store_db_id
                    )

                    if entity_type:
                        checkpoint_query = checkpoint_query.filter(
                            SyncCheckpoint.entity_type == entity_type
                        )

                    checkpoint_result = await db.execute(checkpoint_query)
                    checkpoint = checkpoint_result.scalar_one_or_none()

                    if checkpoint:
                        # Update Airbyte-specific fields
                        checkpoint.airbyte_job_id = airbyte_job_id
                        checkpoint.airbyte_sync_finished_at = datetime.now(timezone.utc)

                        # Set Airbyte sync start time if not already set
                        if not checkpoint.airbyte_sync_started_at:
                            checkpoint.airbyte_sync_started_at = checkpoint.sync_started_at

                        # Update record counts from Airbyte stats
                        checkpoint.records_processed_in_sync = sync_stats.get('total_records_committed', 0)

                        # Update Airbyte last sync time
                        checkpoint.airbyte_last_sync_at = datetime.now(timezone.utc)

                        # Log the stats for debugging
                        logger.info(f"Updated SyncCheckpoint for {entity_type} with Airbyte stats: {sync_stats}")
                        logger.info(f"Airbyte job {airbyte_job_id} processed {checkpoint.records_processed_in_sync} records")

                        # Mark sync as completed
                        checkpoint.current_sync_stage = 'completed' if job_status == 'succeeded' else 'failed'
                        checkpoint.last_sync_status = job_status

            await db.commit()

            # If job is still running, schedule another poll
            if job_status in ['running', 'pending'] and poll_count < 30:  # Max 30 polls (30 minutes)
                # Schedule next poll in 60 seconds
                poll_airbyte_job_status.apply_async(
                    args=[airbyte_job_id, store_id, entity_type, poll_count + 1],
                    countdown=60
                )
                logger.info(f"Airbyte job {airbyte_job_id} still running, scheduling next poll in 60 seconds")
            elif job_status in ['succeeded', 'failed']:
                logger.info(f"Airbyte job {airbyte_job_id} completed with status: {job_status}")
            else:
                logger.warning(f"Airbyte job {airbyte_job_id} in unexpected status: {job_status}")

            result = {
                'status': job_status,
                'airbyte_job_id': airbyte_job_id,
                'sync_stats': airbyte_service.get_job_sync_stats(airbyte_job_id),
                'poll_count': poll_count
            }

            logger.info(f"Airbyte job {airbyte_job_id} status: {job_status}")
            return result

        except Exception as e:
            logger.exception(f"Error polling Airbyte job {airbyte_job_id}: {e}", exc_info=True)
            await db.rollback()
            raise


@celery_app.task(name='sync.cleanup_old_sync_jobs', base=LoggedTask)
def cleanup_old_sync_jobs(days_old: Optional[int] = None):
    """
    Clean up old sync job records to prevent database bloat.

    Args:
        days_old: Number of days old records to keep
    """
    import asyncio

    # Handle event loop issues in forked worker processes
    return asyncio.run(_cleanup_old_sync_jobs_async(days_old))


async def _cleanup_old_sync_jobs_async(days_old: Optional[int] = None):
    """
    Async implementation of cleanup old sync jobs to avoid event loop conflicts.
    """
    from core.db.database import get_session_factory
    from modules.sync.models import SyncJob
    from datetime import datetime, timezone, timedelta
    from sqlalchemy import delete, select

    # Use config value if not provided
    if days_old is None:
        platform_config = get_platform_config('shopify')
        days_old = platform_config['sync_cleanup_days_old']

    session_factory = get_session_factory()
    async with session_factory() as db:
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)

            # Delete old completed sync jobs
            delete_stmt = delete(SyncJob).filter(
                SyncJob.finished_at < cutoff_date,
                SyncJob.status.in_(['completed', 'failed'])
            )

            result = await db.execute(delete_stmt)
            deleted_count = result.rowcount

            await db.commit()

            logger.info(f"Cleaned up {deleted_count} old sync job records")
            return {'deleted_count': deleted_count}

        except Exception as e:
            await db.rollback()
            logger.exception(f"Error cleaning up old sync jobs: {e}", exc_info=True)
            raise


# Task routing configuration is now in config.json under task_routes
