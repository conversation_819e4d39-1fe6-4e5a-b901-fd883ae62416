"""
Celery application configuration for the worker.

This module provides the main Celery app instance used by all tasks and main.py.
"""

import json
import logging
from pathlib import Path

from celery import Celery

from core.config import get_settings

logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Create Celery app (shared instance)
celery_app = Celery(
    'productvideo_worker',
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=['servers.worker.tasks']
)

# Set default task class to enable logging for all tasks
from servers.worker.base_task import LoggedTask
celery_app.Task = LoggedTask

# Load configuration from JSON
config_path = Path(__file__).parent / "config.json"
with open(config_path) as f:
    celery_config = json.load(f)

# Add logging configuration to prevent Celery from overriding our logging
celery_config.update({
    'worker_hijack_root_logger': False, # Prevent Celery from configuring the root logger
    'worker_log_color': True, # Keep this as True, as it might be a separate setting for Celery's internal loggers
    'worker_redirect_stdouts': False, # Crucial: Let our custom StreamHandler manage stdout
    'worker_redirect_stdouts_level': settings.LOG_LEVEL.upper(),
})

celery_app.conf.update(celery_config)

logger.info("Shared Celery app initialized successfully")