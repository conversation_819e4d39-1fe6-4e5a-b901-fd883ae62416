"""
Integration tests for Veo3 Video Provider.
Tests real API calls to Google's Veo3 video generation service.
"""

import os
import pytest
import asyncio
from pathlib import Path
from unittest.mock import patch
import httpx

from google.genai import types

from modules.media.providers.video.veo3 import Veo3Provider
from modules.media.schemas import ProviderMediaRequest, MediaItem, MediaType
from modules.storage.storage_service import MediaStorageService, StorageProvider


@pytest.fixture
def test_env():
    """Set up test environment variables."""
    os.environ["TESTING"] = "true"
    os.environ["STORAGE_PROVIDER"] = "local"
    os.environ["LOCAL_STORAGE_PATH"] = "./test_storage"
    os.environ["LOCAL_STORAGE_BASE_URL"] = "http://localhost:8000/test_storage"
    yield
    # Cleanup
    if os.path.exists("./test_storage"):
        import shutil
        shutil.rmtree("./test_storage")


@pytest.fixture
async def storage_service(test_env):
    """Create storage service for testing."""
    service = MediaStorageService(provider=StorageProvider.LOCAL)
    return service


@pytest.fixture
async def veo3_provider(storage_service):
    """Create and initialize veo3 provider."""
    provider = Veo3Provider()
    provider.set_storage_service(storage_service)

    # Load config - using default config for veo3
    config_path = Path(__file__).parent.parent.parent / "src" / "modules" / "media" / "providers" / "configs" / "veo3.json"
    import json
    with open(config_path) as f:
        config = json.load(f)

    success = await provider.initialize(config)
    if not success:
        pytest.skip("Veo3 provider initialization failed - check VEO3_API_KEY in .env")

    return provider


@pytest.mark.asyncio
@pytest.mark.integration
async def test_veo3_provider_video_generation(veo3_provider):
    """Test real video generation with Veo3 provider."""
    # Create test request
    media_item = MediaItem(
        product_id="test_product_123",
        prompt="Create a professional product showcase video for premium wireless headphones with active noise cancellation and 30-hour battery life.",
        quantity=1
    )

    request = ProviderMediaRequest(
        media_type=MediaType.VIDEO,
        item=media_item,
        shop_id="shop_123"
    )

    # Generate video
    result = await veo3_provider.generate_media(request)

    # Assertions
    assert result.success is True
    assert result.error_message is None
    assert result.variants is not None
    assert len(result.variants) > 0

    # Check first variant
    variant = result.variants[0]
    assert variant.type == "video"
    assert variant.video_url is not None

# @pytest.mark.asyncio
# @pytest.mark.integration
# async def test_veo3_provider_video_generation_with_config_last_frame(veo3_provider):
#     """Test video generation with last_frame and reference_images."""
#     # Download images for testing
#     async with httpx.AsyncClient() as client:
#         # Last frame image - headphones
#         last_frame_response = await client.get("https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400")
#         last_frame_response.raise_for_status()
#         last_frame_data = last_frame_response.content
#         last_frame_mime = "image/jpeg"

#     # Create the Google GenAI objects
#     last_frame = types.Image(image_bytes=last_frame_data, mime_type=last_frame_mime)
#     # Create test request
#     media_item = MediaItem(
#         product_id="test_product_789",
#         prompt="Create a professional product showcase video for premium wireless headphones with active noise cancellation and 30-hour battery life.",
#         quantity=1,
#         provider_config={
#             "last_frame": last_frame,
#         }
#     )

#     request = ProviderMediaRequest(
#         media_type=MediaType.VIDEO,
#         item=media_item,
#         shop_id="shop_123"
#     )

#     # Generate video
#     result = await veo3_provider.generate_media(request)

#     # Assertions
#     assert result.success is True
#     assert result.error_message is None
#     assert result.variants is not None
#     assert len(result.variants) > 0

#     # Check first variant
#     variant = result.variants[0]
#     assert variant.type == "video"
#     assert variant.video_url is not None
#     assert variant.duration_seconds is not None
#     assert variant.aspect_ratio is not None
#     assert variant.resolution is not None
#     assert variant.provider == "veo3"
#     assert variant.generation_metadata is not None
#     assert variant.generation_metadata["provider"] == "veo3"


# @pytest.mark.asyncio
# @pytest.mark.integration
# async def test_veo3_provider_video_generation_with_config_reference_images(veo3_provider):
#     """Test video generation with last_frame and reference_images."""
#     # Download images for testing
#     async with httpx.AsyncClient() as client:
#         # Reference image 1 - watch (asset)
#         ref1_response = await client.get("https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400")
#         ref1_response.raise_for_status()
#         ref1_data = ref1_response.content

#         # Reference image 2 - shoes (style)
#         ref2_response = await client.get("https://images.unsplash.com/photo-**********-7eec264c27ff?w=400")
#         ref2_response.raise_for_status()
#         ref2_data = ref2_response.content
#         ref_mime = "image/jpeg"


#     ref_image1 = types.VideoGenerationReferenceImage(
#         image=types.Image(image_bytes=ref1_data, mime_type=ref_mime),
#         reference_type=types.VideoGenerationReferenceType.ASSET
#     )

#     ref_image2 = types.VideoGenerationReferenceImage(
#         image=types.Image(image_bytes=ref2_data, mime_type=ref_mime),
#         reference_type=types.VideoGenerationReferenceType.STYLE
#     )

#     # Create test request
#     media_item = MediaItem(
#         product_id="test_product_789",
#         prompt="Create a professional product showcase video for premium wireless headphones with active noise cancellation and 30-hour battery life.",
#         quantity=1,
#         provider_config={
#             "reference_images": [ref_image1, ref_image2]
#         }
#     )

#     request = ProviderMediaRequest(
#         media_type=MediaType.VIDEO,
#         item=media_item,
#         shop_id="shop_123"
#     )

#     # Generate video
#     result = await veo3_provider.generate_media(request)

#     # Assertions
#     assert result.success is True
#     assert result.error_message is None
#     assert result.variants is not None
#     assert len(result.variants) > 0

#     # Check first variant
#     variant = result.variants[0]
#     assert variant.type == "video"
#     assert variant.video_url is not None
#     assert variant.duration_seconds is not None
#     assert variant.aspect_ratio is not None
#     assert variant.resolution is not None
#     assert variant.provider == "veo3"
#     assert variant.generation_metadata is not None
#     assert variant.generation_metadata["provider"] == "veo3"


@pytest.mark.asyncio
@pytest.mark.integration
async def test_veo3_provider_video_generation_with_reference_image(veo3_provider):
    """Test video generation with reference image."""
    # Create test request with reference image
    media_item = MediaItem(
        product_id="test_product_456",
        prompt="Create a professional product showcase video for premium wireless headphones with active noise cancellation and 30-hour battery life.",
        reference_images=[
            "https://images.unsplash.com/photo-1618366712010-f4ae9c647dcb"
        ],
        quantity=1
    )

    request = ProviderMediaRequest(
        media_type=MediaType.VIDEO,
        item=media_item,
        shop_id="shop_123"
    )

    # Generate video
    result = await veo3_provider.generate_media(request)

    # Assertions
    assert result.success is True
    assert result.error_message is None
    assert result.variants is not None
    assert len(result.variants) > 0

    # Check first variant
    variant = result.variants[0]
    assert variant.type == "video"
    assert variant.video_url is not None

