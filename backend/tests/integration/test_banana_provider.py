"""
Integration tests for Banana (Gemini Image) Provider.
Tests real API calls to Google's Gemini image generation service.
"""

import os
import pytest
import asyncio
from pathlib import Path
from unittest.mock import patch

from modules.media.providers.image.banana import BananaProvider
from modules.media.schemas import ProviderMediaRequest, MediaItem, MediaType
from modules.storage.storage_service import MediaStorageService, StorageProvider
from core.config import get_settings


@pytest.fixture
def test_env():
    """Set up test environment variables."""
    os.environ["TESTING"] = "true"
    os.environ["STORAGE_PROVIDER"] = "local"
    yield


@pytest.fixture
async def storage_service(test_env):
    """Create storage service for testing."""
    service = MediaStorageService(provider=StorageProvider.LOCAL)
    return service


@pytest.fixture
async def banana_provider(storage_service):
    """Create and initialize banana provider."""
    provider = BananaProvider()
    provider.set_storage_service(storage_service)

    # Load config from test data
    config_path = Path(__file__).parent.parent.parent / "src" / "modules" / "media" / "providers" / "configs" / "banana.json"
    import json
    with open(config_path) as f:
        config = json.load(f)

    success = await provider.initialize(config)
    if not success:
        pytest.skip("Banana provider initialization failed - check BANANA_API_KEY in .env")

    return provider


@pytest.mark.asyncio
@pytest.mark.integration
async def test_banana_provider_image_generation(banana_provider):
    """Test real image generation with Banana provider."""
    # Create test request
    media_item = MediaItem(
        product_id="test_product_123",
        prompt="A professional product photo of wireless headphones on a clean background",
        quantity=1
    )

    request = ProviderMediaRequest(
        media_type=MediaType.IMAGE,
        item=media_item,
        shop_id=123
    )

    # Generate image
    result = await banana_provider.generate_media(request)

    # Assertions
    assert result.success is True
    assert result.error_message is None
    assert result.variants is not None
    assert len(result.variants) > 0

    # Check first variant
    variant = result.variants[0]
    assert variant.type == "image"
    assert variant.image_url is not None
    assert variant.provider == "gemini"
    assert variant.generation_metadata is not None
    assert variant.generation_metadata["provider"] == "gemini"


@pytest.mark.asyncio
@pytest.mark.integration
async def test_banana_provider_with_reference_images(banana_provider):
    """Test image generation with reference images."""
    # Create test request with reference images
    media_item = MediaItem(
        product_id="test_product_456",
        prompt="Generate a lifestyle image featuring these wireless headphones in a modern office setting",
        reference_images=[
            "https://images.unsplash.com/photo-1618366712010-f4ae9c647dcb",
            "https://images.unsplash.com/photo-**********-a3e426bf472b"
        ],
        quantity=1
    )

    request = ProviderMediaRequest(
        media_type=MediaType.IMAGE,
        item=media_item,
        shop_id=123
    )

    # Generate image
    result = await banana_provider.generate_media(request)

    # Assertions
    assert result.success is True
    assert result.error_message is None
    assert result.variants is not None
    assert len(result.variants) > 0

    # Check reference images were used
    variant = result.variants[0]
    assert variant.reference_images_used == 2


@pytest.mark.asyncio
@pytest.mark.integration
async def test_banana_provider_custom_prompt(banana_provider):
    """Test image generation with custom prompt."""
    # Create test request with custom prompt
    media_item = MediaItem(
        product_id="test_product_789",
        prompt="Create a cinematic product shot of premium wireless headphones with dramatic lighting and professional styling",
        quantity=1
    )

    request = ProviderMediaRequest(
        media_type=MediaType.IMAGE,
        item=media_item,
        shop_id=123
    )

    # Generate image
    result = await banana_provider.generate_media(request)

    # Assertions
    assert result.success is True
    assert result.error_message is None
    assert result.variants is not None
    assert len(result.variants) > 0

    # Check custom prompt was used
    variant = result.variants[0]
    assert "cinematic" in variant.prompt_used.lower()
    assert "dramatic lighting" in variant.prompt_used.lower()



@pytest.mark.asyncio
async def test_banana_provider_generate_without_initialization():
    """Test generation attempt without initialization."""
    provider = BananaProvider()

    media_item = MediaItem(
        product_id="test_product_999",
        prompt="Test prompt",
        quantity=1
    )

    request = ProviderMediaRequest(
        media_type=MediaType.IMAGE,
        item=media_item,
        shop_id=123
    )

    result = await provider.generate_media(request)

    assert result.success is False
    assert "not initialized" in result.error_message.lower()