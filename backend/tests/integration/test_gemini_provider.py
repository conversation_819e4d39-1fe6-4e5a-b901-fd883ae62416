"""
Integration tests for Gemini Text Provider.
Tests real API calls to Google's Gemini text generation service.
"""

import os
import pytest
import asyncio
from pathlib import Path
from unittest.mock import patch

from modules.media.providers.text.gemini import GeminiProvider
from modules.media.schemas import ProviderMediaRequest, MediaItem, MediaType
from modules.storage.storage_service import MediaStorageService, StorageProvider


@pytest.fixture
def test_env():
    """Set up test environment variables."""
    os.environ["TESTING"] = "true"
    os.environ["STORAGE_PROVIDER"] = "local"
    yield



@pytest.fixture
async def storage_service(test_env):
    """Create storage service for testing."""
    service = MediaStorageService(provider=StorageProvider.LOCAL)
    return service


@pytest.fixture
async def gemini_provider(storage_service):
    """Create and initialize gemini provider."""
    provider = GeminiProvider()
    provider.set_storage_service(storage_service)

    # Load config - using default config since no specific config file for text provider
    config_path = Path(__file__).parent.parent.parent / "src" / "modules" / "media" / "providers" / "configs" / "gemini.json"
    import json
    with open(config_path) as f:
        config = json.load(f)

    success = await provider.initialize(config)
    if not success:
        pytest.skip("Gemini provider initialization failed - check GEMINI_API_KEY in .env")

    return provider


@pytest.mark.asyncio
@pytest.mark.integration
async def test_gemini_provider_text_generation_product_description(gemini_provider):
    """Test real text generation for product description."""
    # Create test request
    media_item = MediaItem(
        product_id="test_product_123",
        prompt="Write a compelling product description for premium wireless headphones with active noise cancellation and 30-hour battery life.",
        quantity=1,
        provider_config={
            "content_type": "product_description"
        }
    )

    request = ProviderMediaRequest(
        media_type=MediaType.TEXT,
        item=media_item,
        shop_id="shop_123"
    )

    # Generate text
    result = await gemini_provider.generate_media(request)

    # Assertions
    assert result.success is True
    assert result.error_message is None
    assert result.variants is not None
    assert len(result.variants) > 0

    # Check first variant
    variant = result.variants[0]
    assert variant.type == "text"
    assert variant.text is not None
    assert len(variant.text.strip()) > 0
    assert variant.word_count > 0
    assert variant.character_count > 0
    assert variant.provider == "gemini"
    assert variant.generation_metadata is not None
    assert variant.generation_metadata["provider"] == "gemini"


@pytest.mark.asyncio
@pytest.mark.integration
async def test_gemini_provider_text_generation_marketing_copy(gemini_provider):
    """Test real text generation for marketing copy."""
    # Create test request with custom config for marketing copy
    media_item = MediaItem(
        product_id="test_product_456",
        prompt="Create marketing copy for premium wireless headphones with active noise cancellation and 30-hour battery life.",
        quantity=1,
    )

    request = ProviderMediaRequest(
        media_type=MediaType.TEXT,
        item=media_item,
        shop_id="shop_123"
    )

    # Generate text
    result = await gemini_provider.generate_media(request)

    # Assertions
    assert result.success is True
    assert result.error_message is None
    assert result.variants is not None
    assert len(result.variants) > 0

    # Check first variant
    variant = result.variants[0]
    assert variant.type == "text"
    assert variant.text is not None
    assert len(variant.text.strip()) > 0
    assert variant.word_count > 0
    assert variant.character_count > 0


@pytest.mark.asyncio
@pytest.mark.integration
async def test_gemini_provider_text_generation_social_caption(gemini_provider):
    """Test real text generation for social media caption."""
    # Create test request with custom config for social caption
    media_item = MediaItem(
        product_id="test_product_789",
        prompt="Create a social media caption for premium wireless headphones with active noise cancellation and 30-hour battery life.",
        quantity=1,
    )

    request = ProviderMediaRequest(
        media_type=MediaType.TEXT,
        item=media_item,
        shop_id="shop_123"
    )

    # Generate text
    result = await gemini_provider.generate_media(request)

    # Assertions
    assert result.success is True
    assert result.error_message is None
    assert result.variants is not None
    assert len(result.variants) > 0

    # Check first variant
    variant = result.variants[0]
    assert variant.type == "text"
    assert variant.text is not None
    assert len(variant.text.strip()) > 0
    assert variant.word_count > 0
    assert variant.character_count > 0


@pytest.mark.asyncio
@pytest.mark.integration
async def test_gemini_provider_custom_prompt(gemini_provider):
    """Test text generation with custom prompt."""
    # Create test request with custom prompt
    media_item = MediaItem(
        product_id="test_product_custom",
        prompt="Write a compelling product description for premium wireless headphones with active noise cancellation and 30-hour battery life. Focus on the immersive audio experience and professional features.",
        quantity=1
    )

    request = ProviderMediaRequest(
        media_type=MediaType.TEXT,
        item=media_item,
        shop_id="shop_123"
    )

    # Generate text
    result = await gemini_provider.generate_media(request)

    # Assertions
    assert result.success is True
    assert result.error_message is None
    assert result.variants is not None
    assert len(result.variants) > 0

    # Check custom prompt was used
    variant = result.variants[0]
    assert "noise cancellation" in variant.prompt_used.lower()
    assert "30-hour" in variant.prompt_used.lower()


@pytest.mark.asyncio
async def test_gemini_provider_initialization_failure():
    """Test provider initialization failure without API key."""
    from core.config import get_settings

    # Create a mock settings object with empty API key
    mock_settings = type('MockSettings', (), {'GEMINI_API_KEY': ''})()

    with patch('core.config.get_settings', return_value=mock_settings):
        provider = GeminiProvider()
        config = {
            "model": "test-model",
            "config_schema": {
                "temperature": {"default": 0.7},
                "top_p": {"default": 0.95},
                "top_k": {"default": 40},
                "max_output_tokens": {"default": 8192},
                "candidate_count": {"default": 1},
                "response_modalities": {"default": ["TEXT"]},
                "presence_penalty": {"default": 0},
                "frequency_penalty": {"default": 0},
                "seed": {"default": None},
                "stop_sequences": {"default": []},
                "response_logprobs": {"default": False},
                "logprobs": {"default": None},
                "thinking_config": {"default": None},
                "http_options": {"default": None},
                "should_return_http_response": {"default": None},
                "system_instruction": {"default": None},
                "response_mime_type": {"default": None},
                "response_schema": {"default": None},
                "response_json_schema": {"default": None},
                "routing_config": {"default": None},
                "model_selection_config": {"default": None},
                "safety_settings": {"default": None},
                "tools": {"default": None},
                "tool_config": {"default": None},
                "labels": {"default": None},
                "cached_content": {"default": None},
                "automatic_function_calling": {"default": None}
            }
        }
        success = await provider.initialize(config)

        # Test that generation fails when no API key by attempting a request
        media_item = MediaItem(
            product_id="test_product_999",
            prompt="Test prompt",
            quantity=1
        )
        request = ProviderMediaRequest(
            media_type=MediaType.TEXT,
            item=media_item,
            shop_id="shop_123"
        )

        result = await provider.generate_media(request)
        assert result.success is False
        assert "API" in result.error_message or "key" in result.error_message.lower()


@pytest.mark.asyncio
async def test_gemini_provider_generate_without_initialization():
    """Test generation attempt without initialization."""
    provider = GeminiProvider()

    media_item = MediaItem(
        product_id="test_product_999",
        prompt="Test prompt",
        quantity=1
    )

    request = ProviderMediaRequest(
        media_type=MediaType.TEXT,
        item=media_item,
        shop_id="shop_123"
    )

    result = await provider.generate_media(request)

    assert result.success is False
    assert "not initialized" in result.error_message.lower()


@pytest.mark.asyncio
async def test_gemini_provider_unsupported_media_type(gemini_provider):
    """Test unsupported media type handling."""
    media_item = MediaItem(
        product_id="test_product_999",
        prompt="Test prompt",
        quantity=1
    )

    request = ProviderMediaRequest(
        media_type=MediaType.IMAGE,  # Text provider doesn't support images
        item=media_item,
        shop_id="shop_123"
    )

    result = await gemini_provider.generate_media(request)

    assert result.success is False
    assert "unsupported media type" in result.error_message.lower()