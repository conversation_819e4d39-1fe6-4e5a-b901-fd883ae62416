import asyncio
from types import SimpleNamespace

from sqlalchemy import select

from core.db.database import get_session_factory
from modules.auth.models import User
from modules.auth.service import auth_service
from modules.auth.schemas import RegisterRequest


TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "12345678"
TEST_FIRST_NAME = "Test"
TEST_LAST_NAME = "User"
TEST_USERNAME = "test"


async def main():
    session_factory = get_session_factory()
    async with session_factory() as db:
        # Check if user exists
        result = await db.execute(select(User).where(User.email == TEST_EMAIL))
        user = result.scalars().first()
        if user:
            print(f"User already exists: {user.id} {user.email}")
            return

        # Create the user via service to ensure hashing and defaults
        req = RegisterRequest(
            email=TEST_EMAIL,
            password=TEST_PASSWORD,
            first_name=TEST_FIRST_NAME,
            last_name=TEST_LAST_NAME,
            username=TEST_USERNAME,
        )
        user = await auth_service.create_user(db, req)
        print(f"Created user: {user.id} {user.email}")


if __name__ == "__main__":
    asyncio.run(main())

