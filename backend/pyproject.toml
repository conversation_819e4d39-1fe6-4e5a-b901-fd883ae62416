[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "ecommerce-backend"
version = "1.0.0"
description = "E-commerce Integration Hub Backend API"
requires-python = ">=3.12, <3.13"
license = {text = "MIT"}
authors = [
    {name = "E-commerce Hub Team", email = "<EMAIL>"},
]
keywords = ["fastapi", "ecommerce", "shopify", "woocommerce", "api"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
]

dependencies = [
    "fastapi[all]==0.116.1",
    "uvicorn[standard]==0.35.0",
    "python-dotenv==1.1.1",
    "requests==2.32.4",
    "psycopg2-binary==2.9.10",
    "asyncpg==0.30.0",
    "pydantic-settings==2.10.1",
    "passlib[bcrypt]==1.7.4",
    "python-jose[cryptography]==3.5.0",
    "python-multipart>=0.0.9",
    "SQLAlchemy==2.0.43",
    "alembic==1.16.4",
    "mypy>=1.17.1",
    "ruff>=0.12.9",
    "pre-commit>=4.3.0",
    "httpx>=0.28.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pydantic",
    "python-json-logger",
    "colorlog>=6.8.0",
    "aiosqlite",
    "google-cloud-storage",
    "google-genai>=0.3.0", # Google GenAI for Gemini image generation
    "pillow>=10.0.0", # PIL for image processing
    "boto3>=1.34.0", # AWS SDK for S3 operations
    "stripe>=8.0.0", # Stripe integration
    "psutil>=5.9.0", # System monitoring
    "celery>=5.3.0", # Celery for job queue
    "flower>=2.0.0", # Flower for Celery monitoring
    "Pillow>=10.0.0", # Python Imaging Library for image processing
    # E-commerce specific dependencies
    "beautifulsoup4>=4.13.4",
    "graphql-core>=3.2.3",
    "sgqlc>=16.5",
    "google-genai>=1.33.0",
    "redis>=4.0.0",
    "prometheus-client>=0.19.0",
    "jinja2>=3.1.6",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",  # Add this for parallel execution
    "httpx>=0.24.0",
    "async-asgi-testclient",
    "aiosqlite",
    "black>=23.0.0",
    "isort>=5.12.0",
    "pytest-httpx>=0.29.0",
    "pytest-mock>=3.12.0",
    "datamodel-code-generator[graphql]",
    "pytest-timeout>=2.3.1",
]

[project.urls]
Homepage = "https://github.com/your-org/ecommerce-hub"
Documentation = "https://github.com/your-org/ecommerce-hub/docs"
Repository = "https://github.com/your-org/ecommerce-hub.git"
Issues = "https://github.com/your-org/ecommerce-hub/issues"

[project.scripts]
ecommerce-hub = "src.main:app"

[tool.setuptools]
package-dir = {"" = "src"}
[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '/(\.eggs|\.git|\.hg|\.mypy_cache|\.tox|\.venv|build|dist)/'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[tool.ruff]
select = ["I"]

[tool.ruff.isort]
known-first-party = ["core", "modules"]
force-single-line = false
split-on-trailing-comma = true
combine-as-imports = true

[tool.pytest.ini_options]
minversion = "8.0"
addopts = "-ra -q --strict-markers --strict-config -n auto"  # Add -n auto for parallel
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
pythonpath = ["src"]
markers = [
    "integration: marks tests as integration tests that make real API calls",
]

[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/.venv/*",
    "*/migrations/*",
]




