"""add external_id to stores

Revision ID: 8292897a23a0
Revises: 4abef3e64db8
Create Date: 2025-09-20 17:44:52.818531

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '8292897a23a0'
down_revision: Union[str, Sequence[str], None] = '4abef3e64db8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Ensure pgcrypto is available for gen_random_uuid()
    op.execute('CREATE EXTENSION IF NOT EXISTS "pgcrypto";')

    op.create_table('credit_transactions',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('transaction_type', sa.Enum('SUBSCRIPTION_GRANT', 'PURCHASE', 'USAGE', 'EXPIRATION', 'ADJUSTMENT', name='credittransactiontype'), nullable=False),
    sa.Column('amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('balance_after', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('description', sa.String(length=500), nullable=True),
    sa.Column('stripe_payment_id', sa.String(length=255), nullable=True),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('resource_type', sa.String(length=50), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('transaction_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_credit_transactions_external_id'), 'credit_transactions', ['external_id'], unique=True)
    op.create_index(op.f('ix_credit_transactions_id'), 'credit_transactions', ['id'], unique=False)
    op.create_table('dead_letter_queue',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('source_type', sa.String(length=50), nullable=False),
    sa.Column('source_id', sa.String(length=255), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=True),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.Column('original_payload', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('failure_reason', sa.String(length=100), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=False),
    sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('last_retry_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('resolved', sa.Boolean(), nullable=True),
    sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_dlq_created_at', 'dead_letter_queue', ['created_at'], unique=False)
    op.create_index('idx_dlq_store_id', 'dead_letter_queue', ['store_id'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_external_id'), 'dead_letter_queue', ['external_id'], unique=True)
    op.create_index(op.f('ix_dead_letter_queue_id'), 'dead_letter_queue', ['id'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_resolved'), 'dead_letter_queue', ['resolved'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_source_type'), 'dead_letter_queue', ['source_type'], unique=False)
    op.create_table('media_reviews',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('variant_id', sa.BigInteger(), nullable=False),
    sa.Column('reviewer_id', sa.BigInteger(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('quality_score', sa.Float(), nullable=True),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.Column('rejection_reasons', sa.JSON(), nullable=True),
    sa.Column('suggested_improvements', sa.Text(), nullable=True),
    sa.Column('review_criteria', sa.JSON(), nullable=True),
    sa.Column('review_time_seconds', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['reviewer_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['variant_id'], ['media_variants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_media_reviews_id'), 'media_reviews', ['id'], unique=False)
    op.create_table('rejected_assets',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('original_variant_id', sa.BigInteger(), nullable=False),
    sa.Column('job_id', sa.BigInteger(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('rejection_reason', sa.String(), nullable=False),
    sa.Column('rejection_category', sa.String(), nullable=True),
    sa.Column('rejection_details', sa.JSON(), nullable=True),
    sa.Column('original_media_url', sa.String(), nullable=True),
    sa.Column('original_prompt', sa.Text(), nullable=True),
    sa.Column('original_settings', sa.JSON(), nullable=True),
    sa.Column('regeneration_attempts', sa.Integer(), nullable=True),
    sa.Column('last_regeneration_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('regenerated_variant_id', sa.BigInteger(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['media_jobs.id'], ),
    sa.ForeignKeyConstraint(['original_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['regenerated_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rejected_assets_id'), 'rejected_assets', ['id'], unique=False)
    op.create_table('sync_checkpoints',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('last_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_id', sa.String(length=255), nullable=True),
    sa.Column('airbyte_state', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('total_records', sa.Integer(), nullable=True),
    sa.Column('last_sync_status', sa.String(length=20), nullable=True),
    sa.Column('last_error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_successful_sync_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('airbyte_last_sync_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('airbyte_sync_started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('airbyte_sync_finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('local_sync_started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('local_sync_finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('sync_duration_seconds', sa.Float(), nullable=True),
    sa.Column('airbyte_job_id', sa.String(length=255), nullable=True),
    sa.Column('sync_trigger_type', sa.String(length=20), nullable=True),
    sa.Column('current_sync_stage', sa.String(length=30), nullable=True),
    sa.Column('records_processed_in_sync', sa.Integer(), nullable=True),
    sa.Column('source_table_name', sa.String(length=100), nullable=True),
    sa.Column('source_database_name', sa.String(length=100), nullable=True),
    sa.Column('source_record_count_before', sa.Integer(), nullable=True),
    sa.Column('source_record_count_after', sa.Integer(), nullable=True),
    sa.Column('source_new_records_count', sa.Integer(), nullable=True),
    sa.Column('source_updated_records_count', sa.Integer(), nullable=True),
    sa.Column('source_deleted_records_count', sa.Integer(), nullable=True),
    sa.Column('source_last_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('source_filter_criteria', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('destination_table_name', sa.String(length=100), nullable=True),
    sa.Column('destination_database_name', sa.String(length=100), nullable=True),
    sa.Column('destination_record_count_before', sa.Integer(), nullable=True),
    sa.Column('destination_record_count_after', sa.Integer(), nullable=True),
    sa.Column('destination_inserted_count', sa.Integer(), nullable=True),
    sa.Column('destination_updated_count', sa.Integer(), nullable=True),
    sa.Column('destination_deleted_count', sa.Integer(), nullable=True),
    sa.Column('destination_failed_count', sa.Integer(), nullable=True),
    sa.Column('destination_last_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('sync_started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('sync_finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('batch_count', sa.Integer(), nullable=True),
    sa.Column('average_batch_size', sa.Float(), nullable=True),
    sa.Column('max_batch_size', sa.Integer(), nullable=True),
    sa.Column('min_batch_size', sa.Integer(), nullable=True),
    sa.Column('total_batches_processed', sa.Integer(), nullable=True),
    sa.Column('batches_with_errors', sa.Integer(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('records_per_second', sa.Float(), nullable=True),
    sa.Column('average_batch_processing_time', sa.Float(), nullable=True),
    sa.Column('max_batch_processing_time', sa.Float(), nullable=True),
    sa.Column('min_batch_processing_time', sa.Float(), nullable=True),
    sa.Column('memory_usage_peak', sa.Float(), nullable=True),
    sa.Column('database_connection_count', sa.Integer(), nullable=True),
    sa.Column('error_count_total', sa.Integer(), nullable=True),
    sa.Column('error_count_by_type', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('last_error_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('consecutive_error_count', sa.Integer(), nullable=True),
    sa.Column('error_rate_percentage', sa.Float(), nullable=True),
    sa.Column('duplicate_records_found', sa.Integer(), nullable=True),
    sa.Column('invalid_records_skipped', sa.Integer(), nullable=True),
    sa.Column('null_values_count', sa.Integer(), nullable=True),
    sa.Column('data_transformation_errors', sa.Integer(), nullable=True),
    sa.Column('batch_statistics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('sync_version', sa.String(length=50), nullable=True),
    sa.Column('platform_version', sa.String(length=50), nullable=True),
    sa.Column('airbyte_version', sa.String(length=50), nullable=True),
    sa.Column('configuration_hash', sa.String(length=255), nullable=True),
    sa.Column('environment_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('store_id', 'entity_type', name='uq_sync_checkpoints_store_entity')
    )
    op.create_index('idx_sync_checkpoints_store_entity', 'sync_checkpoints', ['store_id', 'entity_type'], unique=False)
    op.create_index('idx_sync_checkpoints_updated_at', 'sync_checkpoints', ['last_updated_at'], unique=False)
    op.create_index(op.f('ix_sync_checkpoints_external_id'), 'sync_checkpoints', ['external_id'], unique=True)
    op.create_index(op.f('ix_sync_checkpoints_id'), 'sync_checkpoints', ['id'], unique=False)
    op.create_table('sync_jobs',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('job_type', sa.String(length=20), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('triggered_by', sa.String(length=50), nullable=True),
    sa.Column('airbyte_job_id', sa.BigInteger(), nullable=True),
    sa.Column('airbyte_connection_id', sa.String(length=255), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('records_processed', sa.Integer(), nullable=True),
    sa.Column('records_failed', sa.Integer(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('celery_task_id', sa.String(length=255), nullable=True),
    sa.Column('job_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_sync_jobs_created_at', 'sync_jobs', ['created_at'], unique=False)
    op.create_index('idx_sync_jobs_store_entity', 'sync_jobs', ['store_id', 'entity_type'], unique=False)
    op.create_index(op.f('ix_sync_jobs_airbyte_job_id'), 'sync_jobs', ['airbyte_job_id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_celery_task_id'), 'sync_jobs', ['celery_task_id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_external_id'), 'sync_jobs', ['external_id'], unique=True)
    op.create_index(op.f('ix_sync_jobs_id'), 'sync_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_status'), 'sync_jobs', ['status'], unique=False)
    op.create_table('webhook_events',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('external_id', sa.UUID(), nullable=False),
    sa.Column('event_id', sa.String(length=255), nullable=False),
    sa.Column('topic', sa.String(length=100), nullable=False),
    sa.Column('shop_domain', sa.String(length=255), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.Column('tenant_id', sa.Integer(), nullable=True),
    sa.Column('event_type', sa.String(length=50), nullable=True),
    sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('headers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('hmac_verified', sa.Boolean(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('last_error', sa.Text(), nullable=True),
    sa.Column('processing_started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_webhook_events_created_at', 'webhook_events', ['created_at'], unique=False)
    op.create_index('idx_webhook_events_shop_topic', 'webhook_events', ['shop_domain', 'topic'], unique=False)
    op.create_index(op.f('ix_webhook_events_event_id'), 'webhook_events', ['event_id'], unique=True)
    op.create_index(op.f('ix_webhook_events_external_id'), 'webhook_events', ['external_id'], unique=True)
    op.create_index(op.f('ix_webhook_events_id'), 'webhook_events', ['id'], unique=False)
    op.create_index(op.f('ix_webhook_events_shop_domain'), 'webhook_events', ['shop_domain'], unique=False)
    op.create_index(op.f('ix_webhook_events_status'), 'webhook_events', ['status'], unique=False)
    op.create_index(op.f('ix_webhook_events_topic'), 'webhook_events', ['topic'], unique=False)
    op.drop_index(op.f('ix_voices_id'), table_name='voices')
    op.drop_table('voices')
    op.add_column('ab_test_experiments', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('ab_test_experiments', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_ab_test_experiments_external_id'), 'ab_test_experiments', ['external_id'], unique=True)
    op.add_column('analytics_events', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('analytics_events', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_analytics_events_external_id'), 'analytics_events', ['external_id'], unique=True)
    op.alter_column('assets', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_index(op.f('ix_assets_external_id'), table_name='assets')
    op.add_column('billing_usage', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('billing_usage', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_billing_usage_external_id'), 'billing_usage', ['external_id'], unique=True)
    op.add_column('conversion_funnels', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('conversion_funnels', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_conversion_funnels_external_id'), 'conversion_funnels', ['external_id'], unique=True)
    op.add_column('customer_addresses', sa.Column('shopify_address_id', sa.String(), nullable=True))
    op.alter_column('customer_addresses', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('customer_addresses', 'external_id',
               existing_type=sa.VARCHAR(),
               type_=sa.UUID(),
               nullable=False,
               postgresql_using="CASE WHEN external_id IS NULL THEN gen_random_uuid() WHEN external_id ~* '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' THEN external_id::uuid ELSE gen_random_uuid() END")
    op.alter_column('customer_addresses', 'customer_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=True)
    op.create_index(op.f('ix_customer_addresses_external_id'), 'customer_addresses', ['external_id'], unique=True)
    op.add_column('customers', sa.Column('shopify_customer_id', sa.String(), nullable=False))
    op.alter_column('customers', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('customers_id_seq'::regclass)"))
    op.alter_column('customers', 'external_id',
               existing_type=sa.VARCHAR(),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using="CASE WHEN external_id IS NULL THEN gen_random_uuid() WHEN external_id ~* '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' THEN external_id::uuid ELSE gen_random_uuid() END")
    op.alter_column('customers', 'store_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=True)
    op.drop_constraint(op.f('customers_external_id_key'), 'customers', type_='unique')
    op.create_index(op.f('ix_customers_external_id'), 'customers', ['external_id'], unique=True)
    op.create_unique_constraint(None, 'customers', ['shopify_customer_id'])
    op.add_column('email_verifications', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('email_verifications', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_email_verifications_external_id'), 'email_verifications', ['external_id'], unique=True)
    op.alter_column('forecasts', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('forecasts', 'store_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=True)
    op.add_column('generated_assets', sa.Column('external_id', sa.UUID(), nullable=True))
    op.execute("UPDATE generated_assets SET external_id = gen_random_uuid() WHERE external_id IS NULL;")
    op.alter_column('generated_assets', 'external_id', nullable=False)
    op.add_column('generated_assets', sa.Column('user_id', sa.BigInteger(), nullable=True))
    op.execute("UPDATE generated_assets SET user_id = (SELECT id FROM users ORDER BY id ASC LIMIT 1) WHERE user_id IS NULL;")
    op.alter_column('generated_assets', 'user_id', nullable=False)
    op.alter_column('generated_assets', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN id ~ '^[0-9]+$' THEN id::bigint ELSE abs((('x'||substr(md5(id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('generated_assets', 'product_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN product_id ~ '^[0-9]+$' THEN product_id::bigint ELSE abs((('x'||substr(md5(product_id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False)
    op.create_index(op.f('ix_generated_assets_external_id'), 'generated_assets', ['external_id'], unique=True)
    op.drop_constraint(op.f('generated_assets_workspace_id_fkey'), 'generated_assets', type_='foreignkey')
    op.create_foreign_key(None, 'generated_assets', 'users', ['user_id'], ['id'])
    op.drop_column('generated_assets', 'workspace_id')
    op.add_column('generation_batches', sa.Column('external_id', sa.UUID(), nullable=True))
    op.execute("UPDATE generation_batches SET external_id = gen_random_uuid() WHERE external_id IS NULL;")
    op.drop_constraint('generation_requests_batch_id_fkey', 'generation_requests', type_='foreignkey')

    op.alter_column('generation_batches', 'external_id', nullable=False)
    op.add_column('generation_batches', sa.Column('user_id', sa.BigInteger(), nullable=True))
    op.execute("UPDATE generation_batches SET user_id = (SELECT id FROM users ORDER BY id ASC LIMIT 1) WHERE user_id IS NULL;")
    op.alter_column('generation_batches', 'user_id', nullable=False)
    op.alter_column('generation_batches', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN id ~ '^[0-9]+$' THEN id::bigint ELSE abs((('x'||substr(md5(id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_generation_batches_external_id'), 'generation_batches', ['external_id'], unique=True)
    op.drop_constraint(op.f('generation_batches_workspace_id_fkey'), 'generation_batches', type_='foreignkey')
    op.create_foreign_key(None, 'generation_batches', 'users', ['user_id'], ['id'])
    op.drop_column('generation_batches', 'workspace_id')
    op.alter_column('generation_requests', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN id ~ '^[0-9]+$' THEN id::bigint ELSE abs((('x'||substr(md5(id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('generation_requests', 'batch_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN batch_id ~ '^[0-9]+$' THEN batch_id::bigint ELSE abs((('x'||substr(md5(batch_id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False)
    op.alter_column('generation_requests', 'product_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN product_id ~ '^[0-9]+$' THEN product_id::bigint ELSE abs((('x'||substr(md5(product_id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False)
    op.create_foreign_key(None, 'generation_requests', 'generation_batches', ['batch_id'], ['id'])
    op.alter_column('holidays', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.add_column('inventory_levels', sa.Column('platform_inventory_id', sa.String(), nullable=True))
    op.execute("UPDATE inventory_levels SET platform_inventory_id = 'legacy-' || id::text WHERE platform_inventory_id IS NULL;")
    op.alter_column('inventory_levels', 'platform_inventory_id', nullable=False)
    op.alter_column('inventory_levels', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('inventory_levels', 'external_id',
               existing_type=sa.VARCHAR(),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using="CASE WHEN external_id IS NULL THEN gen_random_uuid() WHEN external_id ~* '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' THEN external_id::uuid ELSE gen_random_uuid() END")
    op.drop_constraint(op.f('inventory_levels_external_id_key'), 'inventory_levels', type_='unique')
    op.create_index(op.f('ix_inventory_levels_external_id'), 'inventory_levels', ['external_id'], unique=True)
    op.create_unique_constraint(None, 'inventory_levels', ['platform_inventory_id'])
    op.add_column('invoices', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('invoices', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_invoices_external_id'), 'invoices', ['external_id'], unique=True)
    op.add_column('media_analytics', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('media_analytics', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_media_analytics_external_id'), 'media_analytics', ['external_id'], unique=True)
    op.add_column('media_jobs', sa.Column('external_id', sa.UUID(), nullable=False))
    op.add_column('media_jobs', sa.Column('user_id', sa.BigInteger(), nullable=False))
    op.add_column('media_jobs', sa.Column('resolved_provider', sa.String(), nullable=True))
    op.add_column('media_jobs', sa.Column('full_payload', sa.JSON(), nullable=True))
    op.add_column('media_jobs', sa.Column('mode', sa.String(), nullable=True))
    op.add_column('media_jobs', sa.Column('model', sa.String(), nullable=True))
    op.add_column('media_jobs', sa.Column('settings', sa.JSON(), nullable=True))
    op.add_column('media_jobs', sa.Column('items', sa.JSON(), nullable=True))
    op.add_column('media_jobs', sa.Column('shop_id', sa.BigInteger(), nullable=True))
    op.add_column('media_jobs', sa.Column('product_ids', sa.JSON(), nullable=True))
    op.add_column('media_jobs', sa.Column('celery_task_id', sa.String(), nullable=True))
    op.add_column('media_jobs', sa.Column('idempotency_key', sa.String(length=32), nullable=True))
    op.add_column('media_jobs', sa.Column('product_version', sa.String(length=16), nullable=True))
    op.add_column('media_jobs', sa.Column('needs_manual_review', sa.Boolean(), nullable=True))
    op.add_column('media_jobs', sa.Column('qa_metadata', sa.JSON(), nullable=True))
    op.add_column('media_jobs', sa.Column('language', sa.String(length=10), nullable=True))
    op.add_column('media_jobs', sa.Column('fallback_language', sa.String(length=10), nullable=True))
    op.alter_column('media_jobs', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('media_jobs_id_seq'::regclass)"))
    op.alter_column('media_jobs', 'product_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN product_id ~ '^[0-9]+$' THEN product_id::bigint ELSE abs((('x'||substr(md5(product_id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False)
    op.create_index(op.f('ix_media_jobs_external_id'), 'media_jobs', ['external_id'], unique=True)
    op.create_index(op.f('ix_media_jobs_idempotency_key'), 'media_jobs', ['idempotency_key'], unique=False)
    op.drop_constraint(op.f('media_jobs_tenant_id_fkey'), 'media_jobs', type_='foreignkey')
    op.create_foreign_key(None, 'media_jobs', 'users', ['user_id'], ['id'])
    op.drop_column('media_jobs', 'script')
    op.drop_column('media_jobs', 'voice_id')
    op.drop_column('media_jobs', 'tenant_id')
    op.add_column('media_variants', sa.Column('external_id', sa.UUID(), nullable=False))
    op.add_column('media_variants', sa.Column('user_id', sa.BigInteger(), nullable=False))
    op.add_column('media_variants', sa.Column('provider', sa.String(), nullable=True))
    op.add_column('media_variants', sa.Column('captions', sa.Text(), nullable=True))
    op.add_column('media_variants', sa.Column('text_content', sa.Text(), nullable=True))
    op.add_column('media_variants', sa.Column('quality_score', sa.Float(), nullable=True))
    op.add_column('media_variants', sa.Column('needs_manual_review', sa.Boolean(), nullable=True))
    op.add_column('media_variants', sa.Column('qa_metadata', sa.JSON(), nullable=True))
    op.add_column('media_variants', sa.Column('brand_safety_checked', sa.Boolean(), nullable=True))
    op.add_column('media_variants', sa.Column('copyright_validated', sa.Boolean(), nullable=True))
    op.add_column('media_variants', sa.Column('content_flags', sa.JSON(), nullable=True))
    op.alter_column('media_variants', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('media_variants', 'job_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.alter_column('media_variants', 'alt_text',
               existing_type=sa.VARCHAR(),
               type_=sa.Text(),
               existing_nullable=True)
    op.create_index(op.f('ix_media_variants_external_id'), 'media_variants', ['external_id'], unique=True)
    op.drop_constraint(op.f('media_variants_tenant_id_fkey'), 'media_variants', type_='foreignkey')
    op.create_foreign_key(None, 'media_variants', 'users', ['user_id'], ['id'])
    op.drop_column('media_variants', 'tenant_id')
    op.drop_column('media_variants', 'external_variant_id')
    op.drop_column('media_variants', 'voice_url')
    op.add_column('oauth_accounts', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('oauth_accounts', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_oauth_accounts_external_id'), 'oauth_accounts', ['external_id'], unique=True)
    op.add_column('order_line_items', sa.Column('platform_line_item_id', sa.String(), nullable=True))
    op.alter_column('order_line_items', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('order_line_items', 'external_id',
               existing_type=sa.VARCHAR(),
               type_=sa.UUID(),
               nullable=False,
               postgresql_using="CASE WHEN external_id IS NULL THEN gen_random_uuid() WHEN external_id ~* '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' THEN external_id::uuid ELSE gen_random_uuid() END")
    op.alter_column('order_line_items', 'order_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.create_index(op.f('ix_order_line_items_external_id'), 'order_line_items', ['external_id'], unique=True)
    op.add_column('orders', sa.Column('platform_order_id', sa.String(), nullable=False))
    op.alter_column('orders', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('orders', 'external_id',
               existing_type=sa.VARCHAR(),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using="CASE WHEN external_id IS NULL THEN gen_random_uuid() WHEN external_id ~* '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' THEN external_id::uuid ELSE gen_random_uuid() END")
    op.alter_column('orders', 'store_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.alter_column('orders', 'customer_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=True)
    op.drop_constraint(op.f('orders_external_id_key'), 'orders', type_='unique')
    op.create_index(op.f('ix_orders_external_id'), 'orders', ['external_id'], unique=True)
    op.create_unique_constraint(None, 'orders', ['platform_order_id'])
    op.add_column('password_resets', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('password_resets', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_password_resets_external_id'), 'password_resets', ['external_id'], unique=True)
    op.add_column('payment_methods', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('payment_methods', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_payment_methods_external_id'), 'payment_methods', ['external_id'], unique=True)
    op.add_column('product_images', sa.Column('platform_image_id', sa.String(), nullable=True))
    op.execute("UPDATE product_images SET platform_image_id = 'legacy-' || id::text WHERE platform_image_id IS NULL;")
    op.alter_column('product_images', 'platform_image_id', nullable=False)
    op.alter_column('product_images', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('product_images', 'external_id',
               existing_type=sa.VARCHAR(),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using="CASE WHEN external_id IS NULL THEN gen_random_uuid() WHEN external_id ~* '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' THEN external_id::uuid ELSE gen_random_uuid() END")
    op.alter_column('product_images', 'product_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.alter_column('product_images', 'variant_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=True)
    op.drop_constraint(op.f('product_images_external_id_key'), 'product_images', type_='unique')
    op.create_index(op.f('ix_product_images_external_id'), 'product_images', ['external_id'], unique=True)
    op.create_unique_constraint(None, 'product_images', ['platform_image_id'])
    op.alter_column('product_performance', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('product_performance', 'store_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.add_column('product_variants', sa.Column('platform_variant_id', sa.String(), nullable=True))
    op.execute("UPDATE product_variants SET platform_variant_id = 'legacy-' || id::text WHERE platform_variant_id IS NULL;")
    op.alter_column('product_variants', 'platform_variant_id', nullable=False)
    op.add_column('product_variants', sa.Column('product_external_id', sa.UUID(), nullable=True))
    op.alter_column('product_variants', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('product_variants', 'external_id',
               existing_type=sa.VARCHAR(),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using="CASE WHEN external_id IS NULL THEN gen_random_uuid() WHEN external_id ~* '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' THEN external_id::uuid ELSE gen_random_uuid() END")
    op.alter_column('product_variants', 'product_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.drop_constraint(op.f('product_variants_external_id_key'), 'product_variants', type_='unique')
    op.create_index(op.f('ix_product_variants_external_id'), 'product_variants', ['external_id'], unique=True)
    op.create_index(op.f('ix_product_variants_product_external_id'), 'product_variants', ['product_external_id'], unique=False)
    op.create_unique_constraint(None, 'product_variants', ['platform_variant_id'])
    op.add_column('products', sa.Column('platform_product_id', sa.String(), nullable=True))
    op.execute("UPDATE products SET platform_product_id = 'legacy-' || id::text WHERE platform_product_id IS NULL;")
    op.alter_column('products', 'platform_product_id', nullable=False)
    op.alter_column('products', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),

               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('products_id_seq'::regclass)"))
    op.alter_column('products', 'external_id',

               existing_type=sa.VARCHAR(),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using="CASE WHEN external_id IS NULL THEN gen_random_uuid() WHEN external_id ~* '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' THEN external_id::uuid ELSE gen_random_uuid() END")
    op.alter_column('products', 'store_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=True)
    op.drop_constraint(op.f('products_external_id_key'), 'products', type_='unique')
    op.create_index(op.f('ix_products_external_id'), 'products', ['external_id'], unique=True)
    op.create_unique_constraint(None, 'products', ['platform_product_id'])
    op.add_column('scraped_collections', sa.Column('external_id', sa.UUID(), nullable=True))

    op.execute("UPDATE scraped_collections SET external_id = gen_random_uuid() WHERE external_id IS NULL;")
    op.alter_column('scraped_collections', 'external_id', nullable=False)
    op.alter_column('scraped_collections', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN id ~ '^[0-9]+$' THEN id::bigint ELSE abs((('x'||substr(md5(id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False,
               autoincrement=True)
    op.drop_constraint('scraped_collections_document_id_fkey', 'scraped_collections', type_='foreignkey')

    op.alter_column('scraped_collections', 'document_id',

               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN document_id ~ '^[0-9]+$' THEN document_id::bigint ELSE abs((('x'||substr(md5(document_id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False)
    op.create_index(op.f('ix_scraped_collections_external_id'), 'scraped_collections', ['external_id'], unique=True)
    op.add_column('scraped_documents', sa.Column('external_id', sa.UUID(), nullable=True))
    op.drop_constraint('scraped_products_document_id_fkey', 'scraped_products', type_='foreignkey')
    op.drop_constraint('scraping_jobs_document_id_fkey', 'scraping_jobs', type_='foreignkey')


    op.execute("UPDATE scraped_documents SET external_id = gen_random_uuid() WHERE external_id IS NULL;")
    op.alter_column('scraped_documents', 'external_id', nullable=False)
    op.alter_column('scraped_documents', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN id ~ '^[0-9]+$' THEN id::bigint ELSE abs((('x'||substr(md5(id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('scraped_documents', 'workspace_id',

               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.create_index(op.f('ix_scraped_documents_external_id'), 'scraped_documents', ['external_id'], unique=True)
    op.create_foreign_key(None, 'scraped_collections', 'scraped_documents', ['document_id'], ['id'])

    op.add_column('scraped_products', sa.Column('external_id', sa.UUID(), nullable=True))
    op.execute("UPDATE scraped_products SET external_id = gen_random_uuid() WHERE external_id IS NULL;")
    op.alter_column('scraped_products', 'external_id', nullable=False)
    op.alter_column('scraped_products', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN id ~ '^[0-9]+$' THEN id::bigint ELSE abs((('x'||substr(md5(id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('scraped_products', 'document_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN document_id ~ '^[0-9]+$' THEN document_id::bigint ELSE abs((('x'||substr(md5(document_id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False)
    op.create_foreign_key(None, 'scraped_products', 'scraped_documents', ['document_id'], ['id'])

    op.create_index(op.f('ix_scraped_products_external_id'), 'scraped_products', ['external_id'], unique=True)
    op.add_column('scraping_jobs', sa.Column('external_id', sa.UUID(), nullable=True))
    op.execute("UPDATE scraping_jobs SET external_id = gen_random_uuid() WHERE external_id IS NULL;")
    op.alter_column('scraping_jobs', 'external_id', nullable=False)
    op.alter_column('scraping_jobs', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN id ~ '^[0-9]+$' THEN id::bigint ELSE abs((('x'||substr(md5(id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('scraping_jobs', 'document_id',
               existing_type=sa.VARCHAR(),
               type_=sa.BigInteger(),
               postgresql_using="CASE WHEN document_id ~ '^[0-9]+$' THEN document_id::bigint ELSE abs((('x'||substr(md5(document_id),1,16))::bit(64)::bigint)) END",
               existing_nullable=False)
    op.create_foreign_key(None, 'scraping_jobs', 'scraped_documents', ['document_id'], ['id'])

    op.create_index(op.f('ix_scraping_jobs_external_id'), 'scraping_jobs', ['external_id'], unique=True)
    op.add_column('scraping_platforms', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('scraping_platforms', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_scraping_platforms_external_id'), 'scraping_platforms', ['external_id'], unique=True)
    op.alter_column('store_analytics_snapshots', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('store_analytics_snapshots', 'store_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.alter_column('store_sales', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('store_sales', 'store_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.add_column('stores', sa.Column('external_id', sa.UUID(), nullable=True))
    op.execute("UPDATE stores SET external_id = gen_random_uuid() WHERE external_id IS NULL;")
    op.alter_column('stores', 'external_id', nullable=False)
    op.alter_column('stores', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('stores_id_seq'::regclass)"))
    op.alter_column('stores', 'owner_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=True)
    op.alter_column('stores', 'tenant_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=True)
    op.create_index(op.f('ix_stores_external_id'), 'stores', ['external_id'], unique=True)
    op.add_column('subscriptions', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('subscriptions', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_subscriptions_external_id'), 'subscriptions', ['external_id'], unique=True)
    op.add_column('sync_progress', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('sync_progress', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('sync_progress', 'store_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.create_index(op.f('ix_sync_progress_external_id'), 'sync_progress', ['external_id'], unique=True)
    op.add_column('templates', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('templates', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_templates_external_id'), 'templates', ['external_id'], unique=True)
    op.add_column('tenants', sa.Column('plan_tier', sa.String(length=50), nullable=True))
    op.add_column('tenants', sa.Column('credits', sa.Float(), nullable=True))
    op.add_column('tenants', sa.Column('credit_expires_at', sa.DateTime(), nullable=True))
    op.alter_column('tenants', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('tenants_id_seq'::regclass)"))
    op.drop_column('tenants', 'plan')
    op.drop_column('tenants', 'shop_domain')
    op.drop_column('tenants', 'shop_id')
    op.add_column('user_sessions', sa.Column('external_id', sa.UUID(), nullable=False))
    op.alter_column('user_sessions', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True)
    op.create_index(op.f('ix_user_sessions_external_id'), 'user_sessions', ['external_id'], unique=True)
    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('users_id_seq'::regclass)"))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('users_id_seq'::regclass)"))
    op.drop_index(op.f('ix_user_sessions_external_id'), table_name='user_sessions')
    op.alter_column('user_sessions', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('user_sessions', 'external_id')
    op.add_column('tenants', sa.Column('shop_id', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.add_column('tenants', sa.Column('shop_domain', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('tenants', sa.Column('plan', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.alter_column('tenants', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('tenants_id_seq'::regclass)"))
    op.drop_column('tenants', 'credit_expires_at')
    op.drop_column('tenants', 'credits')
    op.drop_column('tenants', 'plan_tier')
    op.drop_index(op.f('ix_templates_external_id'), table_name='templates')
    op.alter_column('templates', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('templates', 'external_id')
    op.drop_index(op.f('ix_sync_progress_external_id'), table_name='sync_progress')
    op.alter_column('sync_progress', 'store_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('sync_progress', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('sync_progress', 'external_id')
    op.drop_index(op.f('ix_subscriptions_external_id'), table_name='subscriptions')
    op.alter_column('subscriptions', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('subscriptions', 'external_id')
    op.drop_index(op.f('ix_stores_external_id'), table_name='stores')
    op.alter_column('stores', 'tenant_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('stores', 'owner_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('stores', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('stores_id_seq'::regclass)"))
    op.drop_column('stores', 'external_id')
    op.alter_column('store_sales', 'store_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('store_sales', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('store_analytics_snapshots', 'store_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('store_analytics_snapshots', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_index(op.f('ix_scraping_platforms_external_id'), table_name='scraping_platforms')
    op.alter_column('scraping_platforms', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('scraping_platforms', 'external_id')
    op.drop_index(op.f('ix_scraping_jobs_external_id'), table_name='scraping_jobs')
    op.alter_column('scraping_jobs', 'document_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('scraping_jobs', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('scraping_jobs', 'external_id')
    op.drop_index(op.f('ix_scraped_products_external_id'), table_name='scraped_products')
    op.alter_column('scraped_products', 'document_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('scraped_products', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('scraped_products', 'external_id')
    op.drop_index(op.f('ix_scraped_documents_external_id'), table_name='scraped_documents')
    op.alter_column('scraped_documents', 'workspace_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('scraped_documents', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('scraped_documents', 'external_id')
    op.drop_index(op.f('ix_scraped_collections_external_id'), table_name='scraped_collections')
    op.alter_column('scraped_collections', 'document_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('scraped_collections', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('scraped_collections', 'external_id')
    op.drop_constraint(None, 'products', type_='unique')
    op.drop_index(op.f('ix_products_external_id'), table_name='products')
    op.create_unique_constraint(op.f('products_external_id_key'), 'products', ['external_id'], postgresql_nulls_not_distinct=False)
    op.alter_column('products', 'store_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('products', 'external_id',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('products', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('products_id_seq'::regclass)"))
    op.drop_column('products', 'platform_product_id')
    op.drop_constraint(None, 'product_variants', type_='unique')
    op.drop_index(op.f('ix_product_variants_product_external_id'), table_name='product_variants')
    op.drop_index(op.f('ix_product_variants_external_id'), table_name='product_variants')
    op.create_unique_constraint(op.f('product_variants_external_id_key'), 'product_variants', ['external_id'], postgresql_nulls_not_distinct=False)
    op.alter_column('product_variants', 'product_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('product_variants', 'external_id',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('product_variants', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('product_variants', 'product_external_id')
    op.drop_column('product_variants', 'platform_variant_id')
    op.alter_column('product_performance', 'store_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('product_performance', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_constraint(None, 'product_images', type_='unique')
    op.drop_index(op.f('ix_product_images_external_id'), table_name='product_images')
    op.create_unique_constraint(op.f('product_images_external_id_key'), 'product_images', ['external_id'], postgresql_nulls_not_distinct=False)
    op.alter_column('product_images', 'variant_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('product_images', 'product_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('product_images', 'external_id',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('product_images', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('product_images', 'platform_image_id')
    op.drop_index(op.f('ix_payment_methods_external_id'), table_name='payment_methods')
    op.alter_column('payment_methods', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('payment_methods', 'external_id')
    op.drop_index(op.f('ix_password_resets_external_id'), table_name='password_resets')
    op.alter_column('password_resets', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('password_resets', 'external_id')
    op.drop_constraint(None, 'orders', type_='unique')
    op.drop_index(op.f('ix_orders_external_id'), table_name='orders')
    op.create_unique_constraint(op.f('orders_external_id_key'), 'orders', ['external_id'], postgresql_nulls_not_distinct=False)
    op.alter_column('orders', 'customer_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('orders', 'store_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('orders', 'external_id',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('orders', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('orders', 'platform_order_id')
    op.drop_index(op.f('ix_order_line_items_external_id'), table_name='order_line_items')
    op.alter_column('order_line_items', 'order_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('order_line_items', 'external_id',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(),
               nullable=True)
    op.alter_column('order_line_items', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('order_line_items', 'platform_line_item_id')
    op.drop_index(op.f('ix_oauth_accounts_external_id'), table_name='oauth_accounts')
    op.alter_column('oauth_accounts', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('oauth_accounts', 'external_id')
    op.add_column('media_variants', sa.Column('voice_url', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('media_variants', sa.Column('external_variant_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('media_variants', sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'media_variants', type_='foreignkey')
    op.create_foreign_key(op.f('media_variants_tenant_id_fkey'), 'media_variants', 'tenants', ['tenant_id'], ['id'])
    op.drop_index(op.f('ix_media_variants_external_id'), table_name='media_variants')
    op.alter_column('media_variants', 'alt_text',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(),
               existing_nullable=True)
    op.alter_column('media_variants', 'job_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('media_variants', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('media_variants', 'content_flags')
    op.drop_column('media_variants', 'copyright_validated')
    op.drop_column('media_variants', 'brand_safety_checked')
    op.drop_column('media_variants', 'qa_metadata')
    op.drop_column('media_variants', 'needs_manual_review')
    op.drop_column('media_variants', 'quality_score')
    op.drop_column('media_variants', 'text_content')
    op.drop_column('media_variants', 'captions')
    op.drop_column('media_variants', 'provider')
    op.drop_column('media_variants', 'user_id')
    op.drop_column('media_variants', 'external_id')
    op.add_column('media_jobs', sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('media_jobs', sa.Column('voice_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('media_jobs', sa.Column('script', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'media_jobs', type_='foreignkey')
    op.create_foreign_key(op.f('media_jobs_tenant_id_fkey'), 'media_jobs', 'tenants', ['tenant_id'], ['id'])
    op.drop_index(op.f('ix_media_jobs_idempotency_key'), table_name='media_jobs')
    op.drop_index(op.f('ix_media_jobs_external_id'), table_name='media_jobs')
    op.alter_column('media_jobs', 'product_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('media_jobs', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('media_jobs_id_seq'::regclass)"))
    op.drop_column('media_jobs', 'fallback_language')
    op.drop_column('media_jobs', 'language')
    op.drop_column('media_jobs', 'qa_metadata')
    op.drop_column('media_jobs', 'needs_manual_review')
    op.drop_column('media_jobs', 'product_version')
    op.drop_column('media_jobs', 'idempotency_key')
    op.drop_column('media_jobs', 'celery_task_id')
    op.drop_column('media_jobs', 'product_ids')
    op.drop_column('media_jobs', 'shop_id')
    op.drop_column('media_jobs', 'items')
    op.drop_column('media_jobs', 'settings')
    op.drop_column('media_jobs', 'model')
    op.drop_column('media_jobs', 'mode')
    op.drop_column('media_jobs', 'full_payload')
    op.drop_column('media_jobs', 'resolved_provider')
    op.drop_column('media_jobs', 'user_id')
    op.drop_column('media_jobs', 'external_id')
    op.drop_index(op.f('ix_media_analytics_external_id'), table_name='media_analytics')
    op.alter_column('media_analytics', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('media_analytics', 'external_id')
    op.drop_index(op.f('ix_invoices_external_id'), table_name='invoices')
    op.alter_column('invoices', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('invoices', 'external_id')
    op.drop_constraint(None, 'inventory_levels', type_='unique')
    op.drop_index(op.f('ix_inventory_levels_external_id'), table_name='inventory_levels')
    op.create_unique_constraint(op.f('inventory_levels_external_id_key'), 'inventory_levels', ['external_id'], postgresql_nulls_not_distinct=False)
    op.alter_column('inventory_levels', 'external_id',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('inventory_levels', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('inventory_levels', 'platform_inventory_id')
    op.alter_column('holidays', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('generation_requests', 'product_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('generation_requests', 'batch_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('generation_requests', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.add_column('generation_batches', sa.Column('workspace_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'generation_batches', type_='foreignkey')
    op.create_foreign_key(op.f('generation_batches_workspace_id_fkey'), 'generation_batches', 'tenants', ['workspace_id'], ['id'])
    op.drop_index(op.f('ix_generation_batches_external_id'), table_name='generation_batches')
    op.alter_column('generation_batches', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('generation_batches', 'user_id')
    op.drop_column('generation_batches', 'external_id')
    op.add_column('generated_assets', sa.Column('workspace_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'generated_assets', type_='foreignkey')
    op.create_foreign_key(op.f('generated_assets_workspace_id_fkey'), 'generated_assets', 'tenants', ['workspace_id'], ['id'])
    op.drop_index(op.f('ix_generated_assets_external_id'), table_name='generated_assets')
    op.alter_column('generated_assets', 'product_id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('generated_assets', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('generated_assets', 'user_id')
    op.drop_column('generated_assets', 'external_id')
    op.alter_column('forecasts', 'store_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('forecasts', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_index(op.f('ix_email_verifications_external_id'), table_name='email_verifications')
    op.alter_column('email_verifications', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('email_verifications', 'external_id')
    op.drop_constraint(None, 'customers', type_='unique')
    op.drop_index(op.f('ix_customers_external_id'), table_name='customers')
    op.create_unique_constraint(op.f('customers_external_id_key'), 'customers', ['external_id'], postgresql_nulls_not_distinct=False)
    op.alter_column('customers', 'store_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('customers', 'external_id',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('customers', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('customers_id_seq'::regclass)"))
    op.drop_column('customers', 'shopify_customer_id')
    op.drop_index(op.f('ix_customer_addresses_external_id'), table_name='customer_addresses')
    op.alter_column('customer_addresses', 'customer_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('customer_addresses', 'external_id',
               existing_type=sa.UUID(),
               type_=sa.VARCHAR(),
               nullable=True)
    op.alter_column('customer_addresses', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('customer_addresses', 'shopify_address_id')
    op.drop_index(op.f('ix_conversion_funnels_external_id'), table_name='conversion_funnels')
    op.alter_column('conversion_funnels', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('conversion_funnels', 'external_id')
    op.drop_index(op.f('ix_billing_usage_external_id'), table_name='billing_usage')
    op.alter_column('billing_usage', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('billing_usage', 'external_id')
    op.create_index(op.f('ix_assets_external_id'), 'assets', ['external_id'], unique=True)
    op.alter_column('assets', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_index(op.f('ix_analytics_events_external_id'), table_name='analytics_events')
    op.alter_column('analytics_events', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('analytics_events', 'external_id')
    op.drop_index(op.f('ix_ab_test_experiments_external_id'), table_name='ab_test_experiments')
    op.alter_column('ab_test_experiments', 'id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('ab_test_experiments', 'external_id')
    op.create_table('voices',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('provider', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('provider_voice_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('gender', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('accent', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('language', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('sample_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('plan_tier_required', postgresql.ENUM('FREE', 'STARTER', 'GROWTH', 'PRO', 'ENTERPRISE', name='plantier'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('voices_pkey'))
    )
    op.create_index(op.f('ix_voices_id'), 'voices', ['id'], unique=False)
    op.drop_index(op.f('ix_webhook_events_topic'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_status'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_shop_domain'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_id'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_external_id'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_event_id'), table_name='webhook_events')
    op.drop_index('idx_webhook_events_shop_topic', table_name='webhook_events')
    op.drop_index('idx_webhook_events_created_at', table_name='webhook_events')
    op.drop_table('webhook_events')
    op.drop_index(op.f('ix_sync_jobs_status'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_external_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_celery_task_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_airbyte_job_id'), table_name='sync_jobs')
    op.drop_index('idx_sync_jobs_store_entity', table_name='sync_jobs')
    op.drop_index('idx_sync_jobs_created_at', table_name='sync_jobs')
    op.drop_table('sync_jobs')
    op.drop_index(op.f('ix_sync_checkpoints_id'), table_name='sync_checkpoints')
    op.drop_index(op.f('ix_sync_checkpoints_external_id'), table_name='sync_checkpoints')
    op.drop_index('idx_sync_checkpoints_updated_at', table_name='sync_checkpoints')
    op.drop_index('idx_sync_checkpoints_store_entity', table_name='sync_checkpoints')
    op.drop_table('sync_checkpoints')
    op.drop_index(op.f('ix_rejected_assets_id'), table_name='rejected_assets')
    op.drop_table('rejected_assets')
    op.drop_index(op.f('ix_media_reviews_id'), table_name='media_reviews')
    op.drop_table('media_reviews')
    op.drop_index(op.f('ix_dead_letter_queue_source_type'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_resolved'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_id'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_external_id'), table_name='dead_letter_queue')
    op.drop_index('idx_dlq_store_id', table_name='dead_letter_queue')
    op.drop_index('idx_dlq_created_at', table_name='dead_letter_queue')
    op.drop_table('dead_letter_queue')
    op.drop_index(op.f('ix_credit_transactions_id'), table_name='credit_transactions')
    op.drop_index(op.f('ix_credit_transactions_external_id'), table_name='credit_transactions')
    op.drop_table('credit_transactions')
    # ### end Alembic commands ###
