"""Add media library assets table

Revision ID: add_media_library_assets
Revises: 2025_09_20-8292897a23a0_add_external_id_to_stores
Create Date: 2025-09-21 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'add_media_library_assets'
down_revision: Union[str, Sequence[str], None] = '2025_09_20-8292897a23a0_add_external_id_to_stores'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create media_library_assets table."""
    op.create_table(
        'media_library_assets',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('external_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.BigInteger(), nullable=False),
        sa.Column('tenant_id', sa.BigInteger(), nullable=True),
        sa.Column('collection', sa.String(), nullable=False),
        sa.Column('type', sa.String(), nullable=False, server_default='image'),
        sa.Column('file_uri', sa.String(), nullable=False),
        sa.Column('preview_uri', sa.String(), nullable=True),
        sa.Column('original_filename', sa.String(), nullable=True),
        sa.Column('content_type', sa.String(), nullable=True),
        sa.Column('size_bytes', sa.BigInteger(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('external_id')
    )
    op.create_index(op.f('ix_media_library_assets_collection'), 'media_library_assets', ['collection'], unique=False)
    op.create_index(op.f('ix_media_library_assets_id'), 'media_library_assets', ['id'], unique=False)
    op.create_index(op.f('ix_media_library_assets_user_id'), 'media_library_assets', ['user_id'], unique=False)


def downgrade() -> None:
    """Drop media_library_assets table."""
    op.drop_index(op.f('ix_media_library_assets_user_id'), table_name='media_library_assets')
    op.drop_index(op.f('ix_media_library_assets_id'), table_name='media_library_assets')
    op.drop_index(op.f('ix_media_library_assets_collection'), table_name='media_library_assets')
    op.drop_table('media_library_assets')
