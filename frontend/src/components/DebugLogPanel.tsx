"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Copy, Trash2, ChevronDown, ChevronUp, X } from "lucide-react";

interface DebugLog {
  timestamp: string;
  action: string;
  [key: string]: any;
}

interface DebugLogPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export function DebugLogPanel({ isOpen, onClose }: DebugLogPanelProps) {
  const [logs, setLogs] = useState<DebugLog[]>([]);
  const [isExpanded, setIsExpanded] = useState(true);

  useEffect(() => {
    const loadLogs = () => {
      if (typeof window !== "undefined") {
        const storedLogs = localStorage.getItem("mediaDebugLogs");
        if (storedLogs) {
          setLogs(JSON.parse(storedLogs));
        }
      }
    };

    loadLogs();

    // Poll for new logs every second
    const interval = setInterval(loadLogs, 1000);

    return () => clearInterval(interval);
  }, []);

  const copyAllLogs = () => {
    const logText = JSON.stringify(logs, null, 2);
    navigator.clipboard.writeText(logText);
  };

  const copyLog = (log: DebugLog) => {
    const logText = JSON.stringify(log, null, 2);
    navigator.clipboard.writeText(logText);
  };

  const clearLogs = () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem("mediaDebugLogs");
      setLogs([]);
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case "MEDIA_GENERATION_REQUEST":
        return "bg-blue-100 text-blue-800";
      case "MEDIA_GENERATION_SUCCESS":
        return "bg-green-100 text-green-800";
      case "MEDIA_GENERATION_ERROR":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/20 z-40" onClick={onClose} />

      {/* Panel */}
      <div className="fixed top-4 left-4 w-96 max-h-[80vh] bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl z-50">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-sm font-semibold">Debug Logs ({logs.length})</h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            <Button variant="outline" size="sm" onClick={copyAllLogs}>
              <Copy className="h-4 w-4 mr-1" />
              Copy All
            </Button>
            <Button variant="outline" size="sm" onClick={clearLogs}>
              <Trash2 className="h-4 w-4 mr-1" />
              Clear
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {logs.length === 0 ? (
          <div className="p-4">
            <p className="text-sm text-gray-500">
              No debug logs yet. Try generating media to see logs here.
            </p>
          </div>
        ) : isExpanded ? (
          <ScrollArea className="h-[60vh] pr-2">
            <div className="p-4 space-y-3">
              {logs.map((log, index) => (
                <div key={index} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge className={getActionColor(log.action)}>
                        {log.action}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyLog(log)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <pre className="text-xs bg-gray-50 dark:bg-gray-800 p-2 rounded overflow-auto max-h-64">
                    {JSON.stringify(log, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className="p-4">
            <p className="text-sm text-gray-500">Click expand to view logs</p>
          </div>
        )}
      </div>
    </>
  );
}
