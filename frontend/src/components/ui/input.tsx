import * as React from "react";
import { motion } from "framer-motion";

import { cn } from "@/lib/utils";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  animate?: boolean;
  variant?: "default" | "glass" | "minimal";
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, animate = true, variant = "default", ...props }, ref) => {
    const baseClasses =
      "flex h-12 w-full px-4 py-3 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300";

    const variantClasses = {
      default:
        "rounded-2xl border border-input bg-background hover:border-primary/30 focus-visible:border-primary/50 shadow-sm hover:shadow-md focus-visible:shadow-lg",
      glass:
        "rounded-2xl border border-white/20 bg-background/80 backdrop-blur-md hover:border-primary/30 focus-visible:border-primary/50 shadow-lg hover:shadow-xl",
      minimal:
        "rounded-2xl border-0 bg-muted/50 hover:bg-muted/70 focus-visible:bg-background focus-visible:ring-1 shadow-sm hover:shadow-md",
    };

    const Comp = animate ? motion.input : "input";
    const motionProps = animate
      ? {
          whileFocus: { scale: 1.0 },
          transition: { duration: 0.2, ease: "easeOut" },
        }
      : {};

    return (
      <Comp
        type={type}
        className={cn(baseClasses, variantClasses[variant], className)}
        ref={ref}
        {...motionProps}
        {...props}
      />
    );
  }
);
Input.displayName = "Input";

export { Input };
