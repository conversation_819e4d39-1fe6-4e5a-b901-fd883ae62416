import React, { useMemo, useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronDown, Images, Minus, Plus } from "lucide-react";

interface MediaFilterProps {
  sources: ("original" | "generated")[];
  onSourcesChange: (values: ("original" | "generated")[]) => void;
  types: ("image" | "video")[];
  onTypesChange: (values: ("image" | "video")[]) => void;
  minCount: number | null; // combined across selected source/type
  onMinCountChange: (value: number | null) => void;
}

export const MediaFilter: React.FC<MediaFilterProps> = ({
  sources,
  onSourcesChange,
  types,
  onTypesChange,
  minCount,
  onMinCountChange,
}) => {
  const [open, setOpen] = useState(false);

  const toggleFromSet = (arr: string[], val: string) => {
    const set = new Set(arr);
    if (set.has(val)) set.delete(val);
    else set.add(val);
    return Array.from(set);
  };

  const label = useMemo(() => {
    const parts: string[] = [];
    if (sources.length) parts.push(sources.join("/"));
    if (types.length) parts.push(types.join("/"));
    if (minCount != null) parts.push(`${minCount}+`);
    return parts.length ? parts.join(" • ") : "Media";
  }, [sources, types, minCount]);

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setOpen((v) => !v)}
        className="px-2.5 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-muted/50 transition-colors"
        aria-label="Media filters"
        title={label}
      >
        <Images className="h-3 w-3" />
        <ChevronDown className="h-3 w-3 ml-1" />
      </Button>

      {open && (
        <div className="absolute left-0 mt-1 w-72 z-20 rounded-md border bg-popover text-popover-foreground shadow-md p-3">
          <div className="space-y-3 text-sm">
            <div>
              <div className="text-[11px] uppercase tracking-wide text-muted-foreground mb-2">
                Source
              </div>
              <div className="flex items-center gap-3">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={sources.includes("original")}
                    onChange={() =>
                      onSourcesChange(
                        toggleFromSet(sources, "original") as (
                          | "original"
                          | "generated"
                        )[]
                      )
                    }
                    className="h-3.5 w-3.5"
                  />
                  <span>Original</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={sources.includes("generated")}
                    onChange={() =>
                      onSourcesChange(
                        toggleFromSet(sources, "generated") as (
                          | "original"
                          | "generated"
                        )[]
                      )
                    }
                    className="h-3.5 w-3.5"
                  />
                  <span>Generated</span>
                </label>
              </div>
            </div>

            <div>
              <div className="text-[11px] uppercase tracking-wide text-muted-foreground mb-2">
                Type
              </div>
              <div className="flex items-center gap-3">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={types.includes("image")}
                    onChange={() =>
                      onTypesChange(
                        toggleFromSet(types, "image") as ("image" | "video")[]
                      )
                    }
                    className="h-3.5 w-3.5"
                  />
                  <span>Images</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={types.includes("video")}
                    onChange={() =>
                      onTypesChange(
                        toggleFromSet(types, "video") as ("image" | "video")[]
                      )
                    }
                    className="h-3.5 w-3.5"
                  />
                  <span>Videos</span>
                </label>
              </div>
            </div>

            <div>
              <div className="text-[11px] uppercase tracking-wide text-muted-foreground mb-2">
                Count
              </div>
              <div className="flex items-center gap-2 flex-wrap">
                {[0, 1, 2, 3, 4, 5].map((n) => (
                  <button
                    key={n}
                    type="button"
                    onClick={() => onMinCountChange(n === 0 ? null : n)}
                    className={`h-6 px-2 rounded border text-xs ${minCount === (n === 0 ? null : n) ? "bg-primary/10 border-primary text-primary" : "bg-background"}`}
                    title={n === 0 ? "Any" : `${n}+`}
                  >
                    {n === 0 ? (
                      <Minus className="h-3 w-3" />
                    ) : (
                      <span className="flex items-center gap-1">
                        <Plus className="h-3 w-3" />
                        {n}
                      </span>
                    )}
                  </button>
                ))}
                <button
                  type="button"
                  onClick={() => onMinCountChange(5)}
                  className={`h-6 px-2 rounded border text-xs ${minCount === 5 ? "bg-primary/10 border-primary text-primary" : "bg-background"}`}
                >
                  5+
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {open && (
        <div className="fixed inset-0 z-10" onClick={() => setOpen(false)} />
      )}
    </div>
  );
};
