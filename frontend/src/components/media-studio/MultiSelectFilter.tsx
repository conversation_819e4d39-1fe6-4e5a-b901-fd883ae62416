import React, { useEffect, useMemo, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { ChevronDown, Search } from "lucide-react";

export interface MultiSelectFilterProps {
  label: string;
  options: string[];
  selected: string[];
  onChange: (values: string[]) => void;
  searchable?: boolean;
  icon?: React.ReactNode; // icon to render in trigger
  ariaLabel?: string; // accessible label when label is hidden
  hideLabel?: boolean; // render icon-only trigger
}

export const MultiSelectFilter: React.FC<MultiSelectFilterProps> = ({
  label,
  options,
  selected,
  onChange,
  searchable = true,
  icon,
  ariaLabel,
  hideLabel = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState("");
  const searchRef = useRef<HTMLInputElement>(null);

  const filtered = useMemo(() => {
    if (!searchable || !query.trim()) return options || [];
    const q = query.toLowerCase().trim();
    return (options || []).filter((o) => o.toLowerCase().includes(q));
  }, [options, query, searchable]);

  useEffect(() => {
    if (isOpen && searchable && searchRef.current) {
      const t = setTimeout(() => searchRef.current?.focus(), 80);
      return () => clearTimeout(t);
    }
  }, [isOpen, searchable]);

  const toggle = (val: string) => {
    const set = new Set(selected || []);
    if (set.has(val)) set.delete(val);
    else set.add(val);
    onChange(Array.from(set));
  };

  const clear = () => onChange([]);

  const triggerLabel = hideLabel ? null : label;
  const triggerAria = ariaLabel || label;

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen((v) => !v)}
        className="px-2.5 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-muted/50 transition-colors"
        aria-label={triggerAria}
        title={label}
      >
        {icon && <span className="inline-flex items-center">{icon}</span>}
        {triggerLabel && (
          <span className={icon ? "ml-1" : ""}>{triggerLabel}</span>
        )}
        {(selected || []).length > 0 && (
          <Badge
            variant="secondary"
            className="ml-1 px-1 py-0 text-xs h-4 min-w-4"
          >
            {(selected || []).length}
          </Badge>
        )}
        <ChevronDown className="h-3 w-3 ml-1" />
      </Button>

      {isOpen && (
        <div className="absolute left-0 mt-1 w-64 z-20 rounded-md border bg-popover text-popover-foreground shadow-md p-3">
          <div className="space-y-3">
            {searchable && (
              <div className="relative">
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-3 w-3 text-muted-foreground" />
                <Input
                  ref={searchRef}
                  placeholder={`Search ${label.toLowerCase()}...`}
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  className="pl-7 h-7 text-xs"
                />
              </div>
            )}

            <div className="max-h-48 overflow-y-auto space-y-2">
              {filtered.map((opt) => (
                <label key={opt} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={(selected || []).includes(opt)}
                    onChange={() => toggle(opt)}
                    className="h-3.5 w-3.5"
                  />
                  <span className="text-sm">{opt}</span>
                </label>
              ))}
              {filtered.length === 0 && (
                <div className="text-sm text-muted-foreground text-center py-2">
                  No results
                </div>
              )}
            </div>

            {(selected || []).length > 0 && (
              <div className="border-t pt-2 mt-2 flex justify-end">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clear}
                  className="h-6 px-2 text-xs"
                >
                  Clear all
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {isOpen && (
        <div className="fixed inset-0 z-10" onClick={() => setIsOpen(false)} />
      )}
    </div>
  );
};
