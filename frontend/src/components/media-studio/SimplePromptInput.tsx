import React, {
  useState,
  useRef,
  useCallback,
  useEffect,
  useMemo,
} from "react";
import { cn } from "@/lib/utils";
import { ImagePlus, Pencil, X as CloseIcon } from "lucide-react";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import type { Asset } from "@/services/assetService";
import type { AssetAction } from "@/types/mediaStudio";
import type { AttachedImage, PromptInputValue } from "@/types/mediaStudio";

const MAX_ATTACHMENTS = 10;

interface SimplePromptInputProps {
  productId: string;
  productTitle: string;
  value: PromptInputValue | undefined;
  onChange: (productId: string, value: PromptInputValue) => void;
  className?: string;
  onDebug?: (action: string, data: any) => void;
  onProductSelectionChange?: (productId: string, isChecked: boolean) => void;
  onAssetAction?: (action: AssetAction, asset: Asset) => void;
  onThumbnailClick?: (asset: Asset, allAssets: Asset[]) => void;
}

interface DragImagePayload {
  type: string;
  assetId?: string;
  id?: string;
  filename?: string;
  displayName?: string;
  url?: string;
  productId?: string;
  role?: "reference" | "model";
  sourceType?: "product" | "ai_generated";
  sceneId?: string;
  prompt?: string;
  name?: string;
}

interface ScenePromptPayload {
  id: string;
  name: string;
  prompt: string;
}

const createDefaultPromptValue = (text = ""): PromptInputValue => ({
  text,
  attachments: [],
});

const createAttachmentFromPayload = (
  payload: DragImagePayload
): AttachedImage | null => {
  const assetId = payload.assetId || payload.id;
  if (!assetId || !payload.url) return null;

  return {
    assetId,
    filename: payload.filename || "image.jpg",
    displayName: payload.displayName || payload.filename || assetId,
    url: payload.url,
    productId: payload.productId || "",
    role: payload.role === "model" ? "model" : "reference",
    sourceType: payload.sourceType,
  };
};

const attachmentToAsset = (attachment: AttachedImage): Asset => ({
  id: attachment.assetId,
  productId: attachment.productId ?? "",
  url: attachment.url,
  type: "image",
  filename: attachment.filename,
  displayName: attachment.displayName || attachment.filename,
  sourceType: attachment.sourceType,
});

const SimplePromptInput: React.FC<SimplePromptInputProps> = ({
  productId,
  productTitle,
  value,
  onChange,
  className,
  onDebug,
  onAssetAction,
  onThumbnailClick,
}) => {
  const promptValue = value ?? createDefaultPromptValue();
  const containerRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [localText, setLocalText] = useState(promptValue.text);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [isDraggingWithinPrompt, setIsDraggingWithinPrompt] = useState(false);
  const [isDraggingOverAddTile, setIsDraggingOverAddTile] = useState(false);
  const [isModelHighlighted, setIsModelHighlighted] = useState(false);
  const dragSourceIndexRef = useRef<number | null>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const attachments = promptValue.attachments;
  const referenceAttachments = useMemo(
    () => attachments.filter((item) => item.role !== "model"),
    [attachments]
  );
  const modelAttachment = useMemo(
    () => attachments.find((item) => item.role === "model") || null,
    [attachments]
  );

  // Sync local text when external value changes
  useEffect(() => {
    setLocalText(promptValue.text);
  }, [promptValue.text]);

  // Adjust textarea height when text changes
  useEffect(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = "auto";
      const nextHeight = Math.max(textarea.scrollHeight, 40);
      textarea.style.height = `${Math.min(nextHeight, 240)}px`;
    }
  }, [localText]);

  // Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  const updatePrompt = useCallback(
    (next: PromptInputValue) => {
      onChange(productId, next);
    },
    [productId, onChange]
  );

  const handleTextChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newText = e.target.value;
      setLocalText(newText);

      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }

      saveTimeoutRef.current = setTimeout(() => {
        updatePrompt({ text: newText, attachments: promptValue.attachments });
      }, 250);
    },
    [promptValue.attachments, updatePrompt]
  );

  const logDebug = useCallback(
    (action: string, data: any) => {
      if (!onDebug) return;
      onDebug(action, {
        productId,
        ...data,
      });
    },
    [onDebug, productId]
  );

  const applyScenePrompt = useCallback(
    (scene: ScenePromptPayload) => {
      const currentText = textareaRef.current?.value ?? localText ?? "";
      const trimmed = currentText.trimEnd();
      const prefix = trimmed.length > 0 ? `${trimmed}\n\n` : "";
      const newText = `${prefix}${scene.prompt}`;

      setLocalText(newText);

      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
        saveTimeoutRef.current = null;
      }

      updatePrompt({ text: newText, attachments: promptValue.attachments });

      if (textareaRef.current) {
        requestAnimationFrame(() => {
          const textarea = textareaRef.current;
          if (textarea) {
            const cursor = textarea.value.length;
            textarea.setSelectionRange(cursor, cursor);
            textarea.focus();
          }
        });
      }

      logDebug("PROMPT_SCENE_APPLIED", {
        sceneId: scene.id,
        sceneName: scene.name,
        previousLength: currentText.length,
        newLength: newText.length,
      });
    },
    [localText, promptValue.attachments, updatePrompt, logDebug]
  );

  const triggerAssetAction = useCallback(
    (action: AssetAction, attachment: AttachedImage) => {
      if (!onAssetAction) return;
      const asset = attachmentToAsset(attachment);
      onAssetAction(action, asset);
    },
    [onAssetAction]
  );

  const handleAttachmentChange = useCallback(
    (nextAttachments: AttachedImage[]) => {
      logDebug("ATTACHMENT_CHANGE", {
        previous: promptValue.attachments.length,
        next: nextAttachments.length,
      });

      updatePrompt({
        text: textareaRef.current?.value ?? localText,
        attachments: nextAttachments,
      });
    },
    [localText, updatePrompt, promptValue.attachments.length, logDebug]
  );

  const commitAttachments = useCallback(
    (references: AttachedImage[], model: AttachedImage | null) => {
      const normalizedReferences = references.map((attachment) => ({
        ...attachment,
        role:
          attachment.role === "model"
            ? "reference"
            : (attachment.role ?? "reference"),
      }));
      const normalizedModel = model
        ? { ...model, role: "model" as const }
        : null;
      const combined = normalizedModel
        ? [...normalizedReferences, normalizedModel]
        : normalizedReferences;
      handleAttachmentChange(combined);
    },
    [handleAttachmentChange]
  );

  const setReferenceAttachments = useCallback(
    (nextReferences: AttachedImage[]) => {
      commitAttachments(nextReferences, modelAttachment);
    },
    [commitAttachments, modelAttachment]
  );

  const setModelAttachmentValue = useCallback(
    (nextModel: AttachedImage | null) => {
      commitAttachments(referenceAttachments, nextModel);
    },
    [commitAttachments, referenceAttachments]
  );

  const handleRemoveAttachment = useCallback(
    (assetId: string) => {
      const next = referenceAttachments.filter(
        (item) => item.assetId !== assetId
      );
      setReferenceAttachments(next);
    },
    [referenceAttachments, setReferenceAttachments]
  );

  const reorderAttachments = useCallback(
    (fromIndex: number, toIndex: number) => {
      if (fromIndex === toIndex) return;
      const next = [...referenceAttachments];
      const [moved] = next.splice(fromIndex, 1);
      if (!moved) return;
      next.splice(toIndex, 0, moved);
      setReferenceAttachments(next);
      logDebug("PROMPT_REORDER_APPLIED", {
        fromIndex,
        toIndex,
        attachmentId: moved.assetId,
      });
    },
    [referenceAttachments, setReferenceAttachments, logDebug]
  );

  const addAttachment = useCallback(
    (attachment: AttachedImage, targetIndex?: number) => {
      if (!attachment) return;

      const normalized: AttachedImage = {
        ...attachment,
        role: "reference",
      };

      const existingIndex = referenceAttachments.findIndex(
        (item) => item.assetId === normalized.assetId
      );

      let working = referenceAttachments;
      if (existingIndex !== -1) {
        working = referenceAttachments.filter(
          (_, idx) => idx !== existingIndex
        );
      } else if (referenceAttachments.length >= MAX_ATTACHMENTS) {
        return;
      }

      const insertIndex =
        typeof targetIndex === "number"
          ? Math.max(0, Math.min(targetIndex, working.length))
          : working.length;

      const next = [...working];
      next.splice(insertIndex, 0, normalized);
      setReferenceAttachments(next);
      logDebug("PROMPT_ATTACHMENT_INSERT", {
        assetId: attachment.assetId,
        insertIndex,
        replacingExisting: existingIndex !== -1,
        attachmentCount: next.length,
      });
    },
    [referenceAttachments, setReferenceAttachments, logDebug]
  );

  const parseDragEvent = (e: React.DragEvent) => {
    let attachment: AttachedImage | null = null;
    let scenePrompt: ScenePromptPayload | null = null;
    const jsonData = e.dataTransfer.getData("application/json");
    if (jsonData) {
      try {
        const payload = JSON.parse(jsonData) as DragImagePayload;
        if (payload.type === "scene_prompt" && payload.prompt) {
          scenePrompt = {
            id:
              payload.sceneId ||
              payload.id ||
              payload.assetId ||
              payload.name ||
              "scene",
            name: payload.name || payload.sceneId || "Scene Preset",
            prompt: payload.prompt,
          };
        } else if (payload.type === "image" || payload.type === "slot") {
          attachment = createAttachmentFromPayload(payload);
        }
      } catch (error) {
        console.error("Failed to parse dropped image payload", error);
      }
    }

    const moveIndexData = e.dataTransfer.getData(
      "application/x-prompt-attachment-index"
    );
    let fromIndex: number | null = null;
    if (moveIndexData) {
      const parsed = Number.parseInt(moveIndexData, 10);
      if (!Number.isNaN(parsed)) {
        fromIndex = parsed;
      }
    }

    return { attachment, fromIndex, scenePrompt };
  };

  const computeInsertIndex = useCallback(
    (clientX: number, clientY?: number) => {
      const container = containerRef.current;
      if (!container) return referenceAttachments.length;

      const tiles = Array.from(
        container.querySelectorAll<HTMLButtonElement>("[data-attachment-index]")
      );

      if (tiles.length === 0) {
        return 0;
      }

      let fallbackIndex = tiles.length;
      for (const tile of tiles) {
        const rect = tile.getBoundingClientRect();
        const midpointX = rect.left + rect.width / 2;
        const pointerY = clientY ?? (rect.top + rect.bottom) / 2;
        const tileIndex = Number.parseInt(
          tile.dataset.attachmentIndex ?? "0",
          10
        );
        if (pointerY < rect.top) {
          logDebug("PROMPT_COMPUTE_INSERT", {
            tileIndex,
            reason: "above-row",
            clientX,
            clientY: pointerY,
            rectTop: rect.top,
          });
          return tileIndex;
        }

        if (pointerY <= rect.bottom) {
          if (clientX < midpointX) {
            logDebug("PROMPT_COMPUTE_INSERT", {
              tileIndex,
              reason: "same-row-before",
              clientX,
              clientY: pointerY,
              midpointX,
            });
            return tileIndex;
          }
          fallbackIndex = tileIndex + 1;
          continue;
        }

        fallbackIndex = tileIndex + 1;
      }

      logDebug("PROMPT_COMPUTE_INSERT_END", {
        clientX,
        clientY,
        tiles: tiles.length,
        attachmentCount: referenceAttachments.length,
        fallbackIndex,
      });
      return fallbackIndex;
    },
    [logDebug, referenceAttachments.length]
  );

  const commitDrop = useCallback(
    (
      insertIndex: number,
      fromIndex: number | null,
      attachment: AttachedImage | null
    ) => {
      const effectiveFrom =
        fromIndex !== null ? fromIndex : dragSourceIndexRef.current;

      if (effectiveFrom !== null) {
        const boundedIndex = Math.max(
          0,
          Math.min(insertIndex, referenceAttachments.length)
        );
        reorderAttachments(effectiveFrom, boundedIndex);
        setHoveredIndex(null);
        logDebug("PROMPT_REORDER", {
          fromIndex: effectiveFrom,
          toIndex: boundedIndex,
          attachmentCount: referenceAttachments.length,
        });
        dragSourceIndexRef.current = null;
        return;
      }

      if (attachment) {
        const boundedIndex = Math.max(
          0,
          Math.min(insertIndex, referenceAttachments.length + 1)
        );
        addAttachment(attachment, boundedIndex);
        logDebug("PROMPT_ATTACHMENT_ADDED", {
          toIndex: boundedIndex,
          assetId: attachment.assetId,
          attachmentCount: referenceAttachments.length,
        });
      }
      setHoveredIndex(null);
      dragSourceIndexRef.current = null;
    },
    [addAttachment, logDebug, referenceAttachments.length, reorderAttachments]
  );

  const handleRootDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      const { attachment, fromIndex, scenePrompt } = parseDragEvent(e);

      if (scenePrompt) {
        logDebug("PROMPT_SCENE_DROP", {
          target: "root",
          sceneId: scenePrompt.id,
        });
        applyScenePrompt(scenePrompt);
        setHoveredIndex(null);
        setIsDraggingWithinPrompt(false);
        setIsDraggingOverAddTile(false);
        setIsModelHighlighted(false);
        return;
      }

      const preferredIndex = referenceAttachments.length;
      logDebug("PROMPT_ROOT_DROP", {
        fromIndex,
        preferredIndex,
        clientX: e.clientX,
        clientY: e.clientY,
        targetTag: (e.target as HTMLElement).tagName,
      });
      commitDrop(preferredIndex, fromIndex, attachment);
      setHoveredIndex(null);
      setIsDraggingWithinPrompt(false);
      setIsDraggingOverAddTile(false);
      setIsModelHighlighted(false);
    },
    [applyScenePrompt, commitDrop, logDebug, referenceAttachments.length]
  );

  const handleTextareaDrop = useCallback(
    (e: React.DragEvent<HTMLTextAreaElement>) => {
      e.preventDefault();
      e.stopPropagation();
      const { attachment, fromIndex, scenePrompt } = parseDragEvent(e);

      if (scenePrompt) {
        logDebug("PROMPT_SCENE_DROP", {
          target: "textarea",
          sceneId: scenePrompt.id,
        });
        applyScenePrompt(scenePrompt);
        setHoveredIndex(null);
        setIsDraggingWithinPrompt(false);
        setIsDraggingOverAddTile(false);
        setIsModelHighlighted(false);
        return;
      }
      const index = referenceAttachments.length;
      logDebug("PROMPT_TEXTAREA_DROP", {
        fromIndex,
        index,
        clientX: e.clientX,
        clientY: e.clientY,
      });
      commitDrop(index, fromIndex, attachment);
      setHoveredIndex(null);
      setIsDraggingWithinPrompt(false);
      setIsDraggingOverAddTile(false);
      setIsModelHighlighted(false);
    },
    [applyScenePrompt, commitDrop, logDebug, referenceAttachments.length]
  );

  const handleAttachmentDrop = useCallback(
    (e: React.DragEvent<HTMLElement>) => {
      e.preventDefault();
      e.stopPropagation();

      const { attachment, fromIndex, scenePrompt } = parseDragEvent(e);

      if (scenePrompt) {
        logDebug("PROMPT_SCENE_DROP", {
          target: "attachments",
          sceneId: scenePrompt.id,
        });
        applyScenePrompt(scenePrompt);
        setHoveredIndex(null);
        setIsDraggingWithinPrompt(false);
        setIsDraggingOverAddTile(false);
        setIsModelHighlighted(false);
        return;
      }

      let insertIndex =
        hoveredIndex ?? computeInsertIndex(e.clientX, e.clientY);
      if (
        fromIndex !== null &&
        insertIndex !== null &&
        fromIndex < insertIndex
      ) {
        insertIndex -= 1;
      }

      logDebug("PROMPT_DROP", {
        fromIndex,
        rawInsertIndex: hoveredIndex,
        computedInsertIndex: computeInsertIndex(e.clientX, e.clientY),
        resolvedInsertIndex: insertIndex ?? referenceAttachments.length,
        attachmentId: attachment?.assetId,
      });

      logDebug("PROMPT_TILE_DROP", {
        fromIndex,
        insertIndex,
        hoveredIndex,
        clientX: e.clientX,
        clientY: e.clientY,
        targetTag: (e.target as HTMLElement).tagName,
      });

      commitDrop(
        insertIndex ?? referenceAttachments.length,
        fromIndex,
        attachment
      );
      setHoveredIndex(null);
      setIsDraggingWithinPrompt(false);
      setIsDraggingOverAddTile(false);
      setIsModelHighlighted(false);
    },
    [
      applyScenePrompt,
      commitDrop,
      computeInsertIndex,
      hoveredIndex,
      logDebug,
      referenceAttachments.length,
    ]
  );

  const handleModelDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();

      const { attachment, fromIndex, scenePrompt } = parseDragEvent(e);

      if (scenePrompt) {
        logDebug("PROMPT_SCENE_DROP", {
          target: "model-slot",
          sceneId: scenePrompt.id,
        });
        applyScenePrompt(scenePrompt);
        setIsModelHighlighted(false);
        setIsDraggingWithinPrompt(false);
        setIsDraggingOverAddTile(false);
        setHoveredIndex(null);
        return;
      }
      if (!attachment) {
        setIsModelHighlighted(false);
        setIsDraggingWithinPrompt(false);
        setIsDraggingOverAddTile(false);
        return;
      }

      const normalized: AttachedImage = { ...attachment, role: "model" };

      let nextReferences = referenceAttachments;
      if (fromIndex !== null) {
        nextReferences = referenceAttachments.filter(
          (_, idx) => idx !== fromIndex
        );
      } else {
        nextReferences = referenceAttachments.filter(
          (item) => item.assetId !== normalized.assetId
        );
      }

      commitAttachments(nextReferences, normalized);

      dragSourceIndexRef.current = null;

      setIsModelHighlighted(false);
      setIsDraggingWithinPrompt(false);
      setIsDraggingOverAddTile(false);
      setHoveredIndex(null);

      logDebug("PROMPT_MODEL_ATTACHMENT_SET", {
        assetId: normalized.assetId,
        fromIndex,
      });
    },
    [applyScenePrompt, referenceAttachments, commitAttachments, logDebug]
  );

  const handleModelRemove = useCallback(() => {
    if (modelAttachment) {
      logDebug("PROMPT_MODEL_ATTACHMENT_REMOVED", {
        assetId: modelAttachment.assetId,
      });
    }
    setModelAttachmentValue(null);
    setIsModelHighlighted(false);
  }, [logDebug, modelAttachment, setModelAttachmentValue]);

  const handleDragOverRoot = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      e.dataTransfer.dropEffect = "copy";
      setIsDraggingWithinPrompt(true);
      if (e.target !== e.currentTarget) return;
      const nextIndex = computeInsertIndex(e.clientX, e.clientY);
      logDebug("PROMPT_ROOT_DRAG_OVER", {
        clientX: e.clientX,
        clientY: e.clientY,
        nextIndex,
      });
      setHoveredIndex(nextIndex);
    },
    [computeInsertIndex, logDebug]
  );

  const handleAttachmentDragStart = useCallback(
    (
      index: number,
      attachment: AttachedImage,
      e: React.DragEvent<HTMLButtonElement>
    ) => {
      dragSourceIndexRef.current = index;
      e.dataTransfer.effectAllowed = "move";
      e.dataTransfer.setData(
        "application/x-prompt-attachment-index",
        index.toString()
      );
      // Provide fallback data for browsers that require text
      e.dataTransfer.setData("text/plain", index.toString());
      e.dataTransfer.setData(
        "application/json",
        JSON.stringify({
          type: "image",
          assetId: attachment.assetId,
          filename: attachment.filename,
          displayName: attachment.displayName,
          url: attachment.url,
          productId: attachment.productId,
          role: attachment.role ?? "reference",
        })
      );

      logDebug("PROMPT_DRAG_START", {
        fromIndex: index,
        assetId: attachment.assetId,
        attachmentCount: referenceAttachments.length,
      });
    },
    [logDebug, referenceAttachments.length]
  );

  const handleAttachmentDragEnd = useCallback(() => {
    dragSourceIndexRef.current = null;
    setHoveredIndex(null);
    setIsDraggingWithinPrompt(false);
    setIsDraggingOverAddTile(false);
    setIsModelHighlighted(false);
    logDebug("PROMPT_DRAG_END", {});
  }, [logDebug]);

  const handleContainerDragLeave = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      if ((e.target as HTMLElement).contains(e.relatedTarget as Node)) {
        return;
      }
      setHoveredIndex(null);
      setIsDraggingWithinPrompt(false);
      setIsDraggingOverAddTile(false);
      setIsModelHighlighted(false);
      logDebug("PROMPT_ROOT_DRAG_LEAVE", {});
    },
    [logDebug]
  );

  const placeholderText = `Describe the scene for ${productTitle.toLowerCase()}...`;

  const canAddMore = referenceAttachments.length < MAX_ATTACHMENTS;
  const highlightAddBackground =
    canAddMore &&
    (isDraggingWithinPrompt ||
      (hoveredIndex !== null && hoveredIndex >= referenceAttachments.length));
  const highlightAddOutline = canAddMore && isDraggingOverAddTile;
  const canDeleteModelAttachment =
    modelAttachment?.sourceType === "ai_generated";

  return (
    <div className={cn("space-y-3", className)}>
      <div
        className="relative rounded-lg bg-background transition-all"
        ref={containerRef}
        onDragOver={handleDragOverRoot}
        onDragEnter={handleDragOverRoot}
        onDragLeave={handleContainerDragLeave}
        onDrop={handleRootDrop}
      >
        {/* Prompt Input */}
        <div className="flex w-full items-start gap-3 px-3 pb-3">
          <div className="flex flex-1 min-w-0 flex-col space-y-3">
            <div className="flex flex-wrap items-center gap-2 pt-3 pb-0 scrollbar-hide">
              {referenceAttachments.map((attachment, index) => {
                const canDelete = attachment.sourceType === "ai_generated";
                return (
                  <Tooltip key={attachment.assetId}>
                    <TooltipTrigger asChild>
                      <button
                        type="button"
                        className={cn(
                          "group relative h-16 w-16 flex-shrink-0 rounded-lg bg-card transition-all",
                          "focus:outline-none focus-visible:ring-2 focus-visible:ring-primary/40",
                          hoveredIndex === index && "ring-2 ring-emerald-400"
                        )}
                        style={{ overflow: "visible" }}
                        data-attachment-index={index}
                        draggable
                        onDragStart={(e) =>
                          handleAttachmentDragStart(index, attachment, e)
                        }
                        onDragEnd={handleAttachmentDragEnd}
                        onDragOver={(e) => {
                          e.preventDefault();
                          e.dataTransfer.dropEffect =
                            dragSourceIndexRef.current === null ? "copy" : "move";
                          setIsDraggingWithinPrompt(true);
                          setHoveredIndex(computeInsertIndex(e.clientX, e.clientY));
                        }}
                        onDragEnter={(event) => {
                          event.preventDefault();
                          setIsDraggingWithinPrompt(true);
                          setHoveredIndex(
                            computeInsertIndex(event.clientX, event.clientY)
                          );
                        }}
                        onDragLeave={(event) => {
                          if (
                            !(event.currentTarget as HTMLElement).contains(
                              event.relatedTarget as Node
                            )
                          ) {
                            setHoveredIndex(null);
                          }
                        }}
                        onDrop={handleAttachmentDrop}
                      >
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div
                              role="button"
                              tabIndex={0}
                              onClick={(event) => event.stopPropagation()}
                              className="absolute top-1 left-1 z-30 flex h-7 w-7 items-center justify-center rounded-full bg-black/45 text-white opacity-0 transition-opacity hover:bg-black/60 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary/40 group-hover:opacity-100"
                              aria-label="Open attachment actions"
                            >
                              <Pencil className="h-3.5 w-3.5" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            align="start"
                            side="right"
                            sideOffset={4}
                            onClick={(event) => event.stopPropagation()}
                            className="w-44"
                          >
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem
                              disabled
                              onSelect={(event) => event.preventDefault()}
                            >
                              Edit (coming soon)
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              disabled
                              onSelect={(event) => event.preventDefault()}
                            >
                              Video (coming soon)
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onSelect={(event) => {
                                event.preventDefault();
                                event.stopPropagation();
                                triggerAssetAction("add_to_models", attachment);
                              }}
                            >
                              Add to models
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onSelect={(event) => {
                                event.preventDefault();
                                event.stopPropagation();
                                triggerAssetAction("add_to_outfits", attachment);
                              }}
                            >
                              Add to outfits
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onSelect={(event) => {
                                event.preventDefault();
                                event.stopPropagation();
                                triggerAssetAction("add_to_scenes", attachment);
                              }}
                            >
                              Add to scenes
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              disabled={!canDelete}
                              className={cn(
                                !canDelete
                                  ? ""
                                  : "text-destructive focus:text-destructive"
                              )}
                              onSelect={(event) => {
                                event.preventDefault();
                                event.stopPropagation();
                                if (canDelete) {
                                  triggerAssetAction("delete", attachment);
                                  handleRemoveAttachment(attachment.assetId);
                                }
                              }}
                            >
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>

                        <div
                          className="h-full w-full overflow-hidden rounded-lg cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (onThumbnailClick) {
                              // Convert attachment to Asset format and create array of all attachments
                              const assetFromAttachment: Asset = {
                                id: attachment.assetId,
                                productId: productId,
                                url: attachment.url,
                                type: "image" as const,
                                filename: attachment.filename,
                                displayName:
                                  attachment.displayName ||
                                  attachment.filename,
                                sourceType: attachment.sourceType || "product",
                              };
                              const allAttachmentAssets: Asset[] =
                                referenceAttachments.map((att) => ({
                                  id: att.assetId,
                                  productId: productId,
                                  url: att.url,
                                  type: "image" as const,
                                  filename: att.filename,
                                  displayName: att.displayName || att.filename,
                                  sourceType: att.sourceType || "product",
                                }));
                              onThumbnailClick(
                                assetFromAttachment,
                                allAttachmentAssets
                              );
                            }
                          }}
                        >
                          <img
                            src={attachment.url}
                            alt={attachment.displayName || attachment.filename}
                            className="h-full w-full object-cover"
                          />
                        </div>

                        <span className="pointer-events-none absolute bottom-1 left-1 h-5 w-5 flex items-center justify-center rounded-full bg-black/40 text-[10px] font-semibold text-white drop-shadow-sm">
                          {index + 1}
                        </span>

                        <button
                          type="button"
                          onClick={(event) => {
                            event.stopPropagation();
                            handleRemoveAttachment(attachment.assetId);
                          }}
                          className="absolute top-0 right-0 flex h-5 w-5 items-center justify-center rounded-full bg-black/40 text-white opacity-0 transition hover:bg-black/60 drop-shadow translate-x-[28%] -translate-y-[28%] group-hover:opacity-100"
                          aria-label="Remove reference image"
                        >
                          <CloseIcon className="h-3 w-3" />
                        </button>
                      </button>
                    </TooltipTrigger>
                    <TooltipContent sideOffset={6}>
                      <div className="max-w-xs text-left">
                        <p className="font-medium">
                          {attachment.displayName || attachment.filename}
                        </p>
                        <p className="text-xs opacity-80">Reference #{index + 1}</p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                );
              })}

              {canAddMore && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      type="button"
                      className={cn(
                        "group relative flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-lg bg-muted/30 transition-all",
                        highlightAddBackground && "bg-emerald-100/60",
                        highlightAddOutline && "ring-2 ring-emerald-400"
                      )}
                      onDragEnter={(event) => {
                        event.preventDefault();
                        setIsDraggingWithinPrompt(true);
                        setIsDraggingOverAddTile(true);
                        setHoveredIndex(
                          computeInsertIndex(event.clientX, event.clientY)
                        );
                      }}
                      onDragLeave={(event) => {
                        if (
                          !(event.currentTarget as HTMLElement).contains(
                            event.relatedTarget as Node
                          )
                        ) {
                          setHoveredIndex(null);
                          setIsDraggingOverAddTile(false);
                        }
                      }}
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.dataTransfer.dropEffect = "copy";
                        setIsDraggingWithinPrompt(true);
                        setIsDraggingOverAddTile(true);
                        setHoveredIndex(computeInsertIndex(e.clientX, e.clientY));
                      }}
                      onDrop={handleAttachmentDrop}
                      aria-label="Attach reference image"
                    >
                      <ImagePlus className="h-6 w-6 text-muted-foreground transition group-hover:text-primary" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent sideOffset={6}>
                    Drop or drag here to attach a reference image
                  </TooltipContent>
                </Tooltip>
              )}
            </div>

            <div className="relative">
              <textarea
                ref={textareaRef}
                value={localText}
                onChange={handleTextChange}
                onDrop={handleTextareaDrop}
                onDragOver={(event) => {
                  event.preventDefault();
                  event.dataTransfer.dropEffect = "copy";
                  setIsDraggingWithinPrompt(true);
                  setIsDraggingOverAddTile(false);
                  setHoveredIndex(computeInsertIndex(event.clientX, event.clientY));
                }}
                onDragEnter={(event) => {
                  event.preventDefault();
                  setIsDraggingWithinPrompt(true);
                  setIsDraggingOverAddTile(false);
                  setHoveredIndex(computeInsertIndex(event.clientX, event.clientY));
                }}
                placeholder={placeholderText}
                className={cn(
                  "w-full rounded-lg border border-transparent bg-transparent px-3 pt-1 pb-1 text-xs leading-normal text-foreground placeholder:text-muted-foreground focus:outline-none focus-visible:ring-0 resize-none overflow-y-auto scrollbar-hide min-h-[40px] max-h-[240px]"
                )}
                style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement;
                  target.style.height = "auto";
                  const nextHeight = Math.max(target.scrollHeight, 40);
                  target.style.height = `${Math.min(nextHeight, 240)}px`;
                }}
              />
            </div>
          </div>

          <Tooltip>
            <TooltipTrigger asChild>
              <div
                className={cn(
                  "ml-auto mt-3 flex h-16 w-16 flex-shrink-0 cursor-pointer flex-col items-center justify-center rounded-lg bg-muted/40 text-xs font-semibold uppercase tracking-wide text-muted-foreground transition-all",
                  isModelHighlighted &&
                    "ring-2 ring-emerald-400 bg-emerald-100/60 text-foreground",
                  modelAttachment && "bg-primary/10 text-foreground"
                )}
                onDragOver={(event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  event.dataTransfer.dropEffect = "copy";
                  setIsModelHighlighted(true);
                }}
                onDragEnter={(event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  setIsModelHighlighted(true);
                }}
                onDragLeave={(event) => {
                  if (
                    !(event.currentTarget as HTMLElement).contains(
                      event.relatedTarget as Node
                    )
                  ) {
                    setIsModelHighlighted(false);
                  }
                }}
                onDrop={handleModelDrop}
              >
                {modelAttachment ? (
                  <div className="relative group h-full w-full overflow-hidden rounded-lg">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <div
                          role="button"
                          tabIndex={0}
                          onClick={(event) => event.stopPropagation()}
                          className="absolute top-1 left-1 z-30 flex h-7 w-7 items-center justify-center rounded-full bg-black/45 text-white opacity-0 transition-opacity hover:bg-black/60 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary/40 group-hover:opacity-100"
                          aria-label="Open model attachment actions"
                        >
                          <Pencil className="h-3.5 w-3.5" />
                        </div>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="start"
                        side="right"
                        sideOffset={4}
                        onClick={(event) => event.stopPropagation()}
                        className="w-44"
                      >
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem
                          disabled
                          onSelect={(event) => event.preventDefault()}
                        >
                          Edit (coming soon)
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          disabled
                          onSelect={(event) => event.preventDefault()}
                        >
                          Video (coming soon)
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onSelect={(event) => {
                            event.preventDefault();
                            event.stopPropagation();
                            triggerAssetAction("add_to_models", modelAttachment);
                          }}
                        >
                          Add to models
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onSelect={(event) => {
                            event.preventDefault();
                            event.stopPropagation();
                            triggerAssetAction("add_to_outfits", modelAttachment);
                          }}
                        >
                          Add to outfits
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onSelect={(event) => {
                            event.preventDefault();
                            event.stopPropagation();
                            triggerAssetAction("add_to_scenes", modelAttachment);
                          }}
                        >
                          Add to scenes
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          disabled={!canDeleteModelAttachment}
                          className={cn(
                            !canDeleteModelAttachment
                              ? ""
                              : "text-destructive focus:text-destructive"
                          )}
                          onSelect={(event) => {
                            event.preventDefault();
                            event.stopPropagation();
                            if (canDeleteModelAttachment) {
                              triggerAssetAction("delete", modelAttachment);
                              handleModelRemove();
                            }
                          }}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <img
                      src={modelAttachment.url}
                      alt={
                        modelAttachment.displayName || modelAttachment.filename
                      }
                      className="h-full w-full object-cover cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onThumbnailClick) {
                          // Convert model attachment to Asset format
                          const assetFromModel: Asset = {
                            id: modelAttachment.assetId,
                            productId: productId,
                            url: modelAttachment.url,
                            type: "image" as const,
                            filename: modelAttachment.filename,
                            displayName:
                              modelAttachment.displayName ||
                              modelAttachment.filename,
                            sourceType: modelAttachment.sourceType || "product",
                          };
                          // Include model attachment with reference attachments for lightbox
                          const allAttachmentAssets: Asset[] = [
                            assetFromModel,
                            ...referenceAttachments.map((att) => ({
                              id: att.assetId,
                              productId: productId,
                              url: att.url,
                              type: "image" as const,
                              filename: att.filename,
                              displayName: att.displayName || att.filename,
                              sourceType: att.sourceType || "product",
                            })),
                          ];
                          onThumbnailClick(assetFromModel, allAttachmentAssets);
                        }
                      }}
                    />
                    <button
                      type="button"
                      onClick={(event) => {
                        event.stopPropagation();
                        handleModelRemove();
                      }}
                      className="absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-full bg-black/50 text-white transition hover:bg-black/70"
                      aria-label="Remove model image"
                    >
                      <CloseIcon className="h-3 w-3" />
                    </button>
                  </div>
                ) : (
                  <>
                    <span className="mb-1 rounded-full bg-muted px-1.5 text-base leading-none">
                      +
                    </span>
                    Model
                  </>
                )}
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" sideOffset={8}>
              {modelAttachment
                ? "Use [model] in the prompt to reference this image"
                : "Drop an image here and reference it with [model] in the prompt"}
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default SimplePromptInput;
