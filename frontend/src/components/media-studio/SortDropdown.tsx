import React, { useMemo, useState } from "react";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, ArrowU<PERSON>, ArrowDown } from "lucide-react";

export type SortField =
  | "product_title"
  | "created"
  | "updated"
  | "inventory"
  | "product_type"
  | "vendor";

export type SortDir = "asc" | "desc";

interface SortDropdownProps {
  field: SortField | null;
  dir: SortDir;
  onChange: (field: SortField | null, dir: SortDir) => void;
}

const FIELDS: { key: SortField; label: string }[] = [
  { key: "product_title", label: "Product title" },
  { key: "created", label: "Created" },
  { key: "updated", label: "Updated" },
  { key: "inventory", label: "Inventory" },
  { key: "product_type", label: "Product type" },
  { key: "vendor", label: "Vendor" },
];

export const SortDropdown: React.FC<SortDropdownProps> = ({
  field,
  dir,
  onChange,
}) => {
  const [open, setOpen] = useState(false);

  const label = useMemo(() => {
    const f = FIELDS.find((f) => f.key === field)?.label;
    if (!f) return "Sort";
    return `${f} · ${dir === "asc" ? "Oldest first" : "Newest first"}`;
  }, [field, dir]);

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setOpen((v) => !v)}
        className="px-2.5 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-muted/50 transition-colors"
        aria-label="Sort"
        title={label}
      >
        <ArrowUpDown className="h-3 w-3" />
      </Button>

      {open && (
        <div className="absolute right-0 mt-1 w-64 z-20 rounded-md border bg-popover text-popover-foreground shadow-md p-2">
          <div className="text-[11px] uppercase tracking-wide text-muted-foreground px-2 py-1">
            Sort by
          </div>
          <div className="max-h-48 overflow-y-auto">
            {FIELDS.map((f) => (
              <button
                key={f.key}
                className={`w-full text-left px-3 py-1.5 text-sm hover:bg-muted ${field === f.key ? "bg-muted/70" : ""}`}
                onClick={() => onChange(f.key, dir)}
                type="button"
              >
                {f.label}
              </button>
            ))}
          </div>
          <div className="border-t my-1" />
          <div className="text-[11px] uppercase tracking-wide text-muted-foreground px-2 py-1">
            Order direction
          </div>
          <div className="flex items-center gap-2 p-2">
            <Button
              variant={dir === "asc" ? "secondary" : "outline"}
              size="sm"
              className="h-7 text-xs"
              onClick={() => onChange(field, "asc")}
            >
              <ArrowUp className="h-3 w-3 mr-1" /> Oldest first
            </Button>
            <Button
              variant={dir === "desc" ? "secondary" : "outline"}
              size="sm"
              className="h-7 text-xs"
              onClick={() => onChange(field, "desc")}
            >
              <ArrowDown className="h-3 w-3 mr-1" /> Newest first
            </Button>
          </div>
        </div>
      )}

      {open && (
        <div className="fixed inset-0 z-10" onClick={() => setOpen(false)} />
      )}
    </div>
  );
};
