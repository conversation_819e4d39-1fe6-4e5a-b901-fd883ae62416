import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronDown, Plus } from "lucide-react";

interface AddFilterMenuProps {
  showCollection: boolean;
  onToggleCollection: (show: boolean) => void;
}

export const AddFilterMenu: React.FC<AddFilterMenuProps> = ({
  showCollection,
  onToggleCollection,
}) => {
  const [open, setOpen] = useState(false);

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setOpen((v) => !v)}
        className="px-2.5 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-muted/50 transition-colors"
        aria-label="Add filter"
        title="Add filter"
      >
        <Plus className="h-3 w-3" />
        <ChevronDown className="h-3 w-3 ml-1" />
      </Button>

      {open && (
        <div className="absolute left-0 mt-1 w-56 z-20 rounded-md border bg-popover text-popover-foreground shadow-md p-2">
          <button
            type="button"
            className={`w-full text-left px-3 py-1.5 text-sm hover:bg-muted ${showCollection ? "bg-muted/70" : ""}`}
            onClick={() => {
              onToggleCollection(!showCollection);
              setOpen(false);
            }}
          >
            Collection
          </button>
          {/* Categories, Sales channel skipped (not in DB yet) */}
        </div>
      )}

      {open && (
        <div className="fixed inset-0 z-10" onClick={() => setOpen(false)} />
      )}
    </div>
  );
};
