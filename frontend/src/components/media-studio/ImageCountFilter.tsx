import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Image } from "lucide-react";

export type ImageCountFilterValue = {
  count?: "1" | "2" | "3" | "4" | "5" | "5+" | null;
};

interface ImageCountFilterProps {
  value: ImageCountFilterValue;
  onChange?: (value: ImageCountFilterValue) => void;
  compact?: boolean;
}

export const ImageCountFilter: React.FC<ImageCountFilterProps> = ({
  value,
  onChange,
  compact = false,
}) => {
  const handleCountChange = (count: string) => {
    if (count === "all") {
      onChange?.({ count: null });
    } else {
      onChange?.({
        count: count as "1" | "2" | "3" | "4" | "5" | "5+",
      });
    }
  };

  if (compact) {
    return (
      <Select value={value.count || "all"} onValueChange={handleCountChange}>
        <SelectTrigger className="w-20 h-8 text-xs rounded-full [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground">
          <div className="flex items-center gap-1">
            <Image className="h-3 w-3" />
            <SelectValue />
          </div>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All</SelectItem>
          <SelectItem value="1">1</SelectItem>
          <SelectItem value="2">2</SelectItem>
          <SelectItem value="3">3</SelectItem>
          <SelectItem value="4">4</SelectItem>
          <SelectItem value="5">5</SelectItem>
          <SelectItem value="5+">5+</SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // Non-compact version (fallback)
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium">Image Count</label>
      <div className="flex gap-2">
        <Select value={value.count || "all"} onValueChange={handleCountChange}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Select count" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="1">1 image</SelectItem>
            <SelectItem value="2">2 images</SelectItem>
            <SelectItem value="3">3 images</SelectItem>
            <SelectItem value="4">4 images</SelectItem>
            <SelectItem value="5">5 images</SelectItem>
            <SelectItem value="5+">5+ images</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
