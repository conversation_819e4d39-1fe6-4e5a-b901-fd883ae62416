"use client";

import React, { useState, useMemo, useEffect, useCallback } from "react";
import { useVirtualizer } from "@tanstack/react-virtual";
import { cn } from "@/lib/utils";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Asset } from "@/services/assetService";
import { Product } from "@/services/productService";
import { AssetAction, MainTab, PromptInputValue } from "@/types/mediaStudio";
import { Check } from "lucide-react";

import SimplePromptInput from "./SimplePromptInput";
import { Thumbnail } from "./Thumbnail";

interface ProductGridProps {
  products: Product[];
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  onAssetAction: (action: AssetAction, asset: Asset) => void;
  prompts: Record<string, PromptInputValue>;
  onPromptChange: (productId: string, value: PromptInputValue) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onSelectAllProducts: (isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generatedImages?: Record<string, Asset[]>;
  availableCollections?: Array<{ id: string; name: string; color: string }>;
  collectionFilters?: string[];
  onCollectionFiltersChange?: (filters: string[]) => void;
  searchQuery?: string;
  onSearchQueryChange?: (query: string) => void;
  filterSelectedOnly?: boolean;
  onFilterSelectedOnlyChange?: (value: boolean) => void;
  filterHasGenerated?: boolean;
  onFilterHasGeneratedChange?: (value: boolean) => void;
  sortMode?: "default" | "selected_first" | "generated_first";
  onSortModeChange?: (
    mode: "default" | "selected_first" | "generated_first"
  ) => void;
  productTotalCount?: number;
  onDebug?: (action: string, data: any) => void;
  onThumbnailClick?: (asset: Asset, allAssets: Asset[]) => void;
  scrollParentRef?: React.RefObject<HTMLElement | null>;
  columns?: number;
}

const getAssetTimestamp = (asset: Asset) => {
  const candidates = [
    asset.generatedAt,
    (asset as any).createdAt,
    (asset as any).created_at,
    (asset as any).updatedAt,
    (asset as any).updated_at,
  ].filter(Boolean) as string[];

  for (const value of candidates) {
    const time = Date.parse(value);
    if (!Number.isNaN(time)) {
      return time;
    }
  }

  const numericId = parseInt(asset.id.replace(/[^0-9]/g, ""), 10);
  if (!Number.isNaN(numericId)) {
    return numericId;
  }

  return 0;
};

const sortByRecency = (a: Asset, b: Asset) => {
  const diff = getAssetTimestamp(b) - getAssetTimestamp(a);
  if (diff !== 0) {
    return diff;
  }
  return b.id.localeCompare(a.id);
};

const INTERACTIVE_SELECTOR = [
  "button",
  "input",
  "textarea",
  "select",
  "a",
  "label",
  '[role="textbox"]',
  '[role="button"]',
  '[role="menuitem"]',
  '[role="checkbox"]',
  '[role="switch"]',
  '[contenteditable="true"]',
  "[data-radix-popover-trigger]",
].join(", ");

const isWithinInteractiveElement = (target: EventTarget | null) => {
  if (!(target instanceof Element)) {
    return false;
  }

  if (target.closest('[data-card-interactive="true"]')) {
    return true;
  }

  if (target.closest(INTERACTIVE_SELECTOR)) {
    return true;
  }

  return false;
};

const ProductRow: React.FC<{
  product: Product;
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  onAssetAction: (action: AssetAction, asset: Asset) => void;
  prompts: Record<string, PromptInputValue>;
  onPromptChange: (productId: string, value: PromptInputValue) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generatedImages?: Record<string, Asset[]>;
  allAvailableAssets?: Asset[];
  allProducts?: Product[]; // All products for cross-product asset references
  onDebug?: (action: string, data: any) => void;
  onThumbnailClick?: (asset: Asset, allAssets: Asset[]) => void;
}> = ({
  product,
  selectedAssetIds,
  onAssetSelect,
  onAssetAction,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onCopyPromptToAll,
  generatedImages = {},
  allAvailableAssets = [],
  allProducts = [],
  onDebug,
  onThumbnailClick,
}) => {
  const isRowSelected = selectedProductIds.has(String(product.id));
  const scrollContainerRef = React.useRef<HTMLDivElement | null>(null);
  // Persistent state for selected image index per product
  const [selectedImageIndex, setSelectedImageIndex] = useState(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem(`media-studio-preview-${product.id}`);
      return saved ? parseInt(saved, 10) : 0;
    }
    return 0;
  });

  // Persist selected image index when it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        `media-studio-preview-${product.id}`,
        selectedImageIndex.toString()
      );
    }
  }, [selectedImageIndex, product.id]);

  const existingAssetIds = React.useMemo(
    () => new Set(product.assets?.map((a) => a.id) || []),
    [product.assets]
  );

  const productGeneratedImages = useMemo(
    () => generatedImages[product.id] || [],
    [generatedImages, product.id]
  );

  const filteredGeneratedImages = useMemo(
    () => productGeneratedImages.filter((a) => !existingAssetIds.has(a.id)),
    [productGeneratedImages, existingAssetIds]
  );

  // Get all available assets for this product (original + generated)
  const sortedGeneratedAssets = useMemo(() => {
    return filteredGeneratedImages.slice().sort(sortByRecency);
  }, [filteredGeneratedImages]);

  const productAssets = useMemo(() => {
    // Convert MediaItem objects to Asset objects for Thumbnail component
    return (product.assets || []).map((mediaItem: any) => {
      // If it's already an Asset object, return as-is
      if (mediaItem.url && mediaItem.displayName !== undefined) {
        return mediaItem;
      }
      // Convert MediaItem to Asset format - cache the conversion
      const filename = mediaItem.src?.split("/").pop() || "image.jpg";
      return {
        id: mediaItem.id,
        productId: mediaItem.data?.product_id || String(product.id),
        url: mediaItem.src || mediaItem.url,
        type: mediaItem.type as "image" | "video",
        filename,
        displayName: mediaItem.alt || filename,
        generatedAt: mediaItem.data?.generated_at,
        sourceType: mediaItem.data?.source_type || "product",
      };
    });
  }, [product.assets, product.id]);

  const allProductAssets = useMemo(() => {
    return [...productAssets, ...sortedGeneratedAssets];
  }, [productAssets, sortedGeneratedAssets]);

  useEffect(() => {
    if (allProductAssets.length === 0) {
      setSelectedImageIndex(0);
      return;
    }

    setSelectedImageIndex((prev) =>
      Math.min(prev, Math.max(allProductAssets.length - 1, 0))
    );
  }, [allProductAssets.length]);

  const handleCardClick = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isWithinInteractiveElement(event.target)) {
        return;
      }
      onProductSelectionChange(product.id.toString(), !isRowSelected);
    },
    [isRowSelected, onProductSelectionChange, product.id]
  );

  return (
    <Card
      className={cn(
        "relative transition-all duration-200 overflow-hidden flex flex-col shadow-none rounded-[14px] bg-card border border-border/40 ring-0 ring-transparent",
        isRowSelected && "ring-[3px] ring-primary"
      )}
      onClick={handleCardClick}
      role="button"
      aria-pressed={isRowSelected}
      tabIndex={0}
      onKeyDown={(event) => {
        if (isWithinInteractiveElement(event.target)) {
          return;
        }
        if (event.key === "Enter" || event.key === " ") {
          event.preventDefault();
          onProductSelectionChange(product.id.toString(), !isRowSelected);
        }
      }}
    >
      <button
        type="button"
        onClick={(event) => {
          event.preventDefault();
          event.stopPropagation();
          onProductSelectionChange(product.id.toString(), !isRowSelected);
        }}
        className={cn(
          "group/selection absolute right-2 top-2 z-20 flex h-6 w-6 items-center justify-center rounded-full border bg-background/90 backdrop-blur-sm transition-colors duration-200",
          isRowSelected
            ? "border-primary bg-primary text-primary-foreground"
            : "border-border/40 text-muted-foreground/50 hover:border-border/60 hover:text-muted-foreground"
        )}
        aria-pressed={isRowSelected}
        aria-label={isRowSelected ? "Deselect product" : "Select product"}
        data-card-interactive="true"
      >
        <Check
          className={cn(
            "h-3.5 w-3.5 transition-opacity duration-200",
            isRowSelected
              ? "opacity-100"
              : "opacity-0 group-hover/selection:opacity-50"
          )}
        />
      </button>
      {/* Product Title Section */}
      <CardContent className="p-3 pb-1">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-foreground text-base leading-tight mb-1 truncate">
              {product.title}
            </h3>
            {/* Categories */}
            {(product.collections ?? []).length > 0 && (
              <div className="flex flex-wrap gap-1">
                {(Array.isArray(product.collections)
                  ? product.collections
                  : []
                ).map((col) => (
                  <Badge
                    key={col.id}
                    variant="secondary"
                    className="text-xs px-2 py-0.5 font-normal pointer-events-none select-none hover:bg-transparent transition-none"
                  >
                    {col.name}
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <div className="flex items-center gap-3 ml-4">
            <div className="text-xs text-muted-foreground">
              {productAssets.length > 0 && `${productAssets.length} product`}
              {productAssets.length > 0 &&
                sortedGeneratedAssets.length > 0 &&
                ", "}
              {sortedGeneratedAssets.length > 0 &&
                `${sortedGeneratedAssets.length} generated`}
              {productAssets.length === 0 &&
                sortedGeneratedAssets.length === 0 &&
                "0 assets"}
            </div>
          </div>
        </div>
      </CardContent>

      {/* Main Content Layout */}
      <div className="flex flex-1 min-h-0">
        {/* Product media thumbnails */}
        {productAssets.length > 0 && (
          <div className="relative bg-muted/10 group">
            <div className="px-3 py-1 text-xs text-muted-foreground font-medium border-b border-border/50">
              Product Images
            </div>
            <div
              ref={scrollContainerRef}
              className="flex items-center gap-2 overflow-x-auto px-3 py-2 scrollbar-hide"
              style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
            >
              {productAssets.map((asset, index) => {
                const isActive = index === selectedImageIndex;
                return (
                  <button
                    key={asset.id}
                    onClick={() => setSelectedImageIndex(index)}
                    className={cn(
                      "relative w-20 h-20 flex-shrink-0 transition-all duration-200",
                      isActive
                        ? "bg-primary/10 border-b-2 border-primary"
                        : "hover:bg-primary/5 border-b-2 border-transparent hover:border-primary/30"
                    )}
                  >
                    <Thumbnail
                      asset={asset}
                      isSelected={false}
                      onSelect={() => {
                        // Only change the displayed image, no selection
                        setSelectedImageIndex(index);
                      }}
                      showBorder={false}
                      draggable={true}
                    />
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Generated media thumbnails */}
        {sortedGeneratedAssets.length > 0 && (
          <div className="relative bg-blue-50/50 dark:bg-blue-950/20 group border-t border-border/50">
            <div className="px-3 py-1 text-xs text-blue-700 dark:text-blue-300 font-medium">
              Generated Assets ({sortedGeneratedAssets.length})
            </div>
            <div className="flex items-center gap-2 overflow-x-auto px-3 py-2 scrollbar-hide">
              {sortedGeneratedAssets.map((asset, index) => {
                const globalIndex = productAssets.length + index;
                const isActive = globalIndex === selectedImageIndex;
                return (
                  <button
                    key={asset.id}
                    onClick={() => setSelectedImageIndex(globalIndex)}
                    className={cn(
                      "relative w-20 h-20 flex-shrink-0 transition-all duration-200",
                      isActive
                        ? "bg-blue-100 dark:bg-blue-900/50 border-b-2 border-blue-500"
                        : "hover:bg-blue-50 dark:hover:bg-blue-900/30 border-b-2 border-transparent hover:border-blue-400"
                    )}
                  >
                    <Thumbnail
                      asset={asset}
                      isSelected={false}
                      onSelect={() => {
                        // Only change the displayed image, no selection
                        setSelectedImageIndex(globalIndex);
                      }}
                      showBorder={false}
                      draggable={true}
                    />
                    {/* Generated badge */}
                    <div className="absolute top-1 right-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded text-[10px] font-medium">
                      AI
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}
        <div className="w-full min-w-0">
          {allProductAssets.length > 0 && (
            <div className="relative mt-2 bg-muted/10">
              <div
                className="flex flex-wrap gap-2 px-3 py-2 md:flex-nowrap md:overflow-x-auto scrollbar-hide"
                style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
              >
                {allProductAssets.map((asset, index) => {
                  const isActive = index === selectedImageIndex;
                  return (
                    <button
                      key={
                        asset.id ||
                        `${product.id}:${index}:${(asset as any).url || (asset as any).src || "no-url"}`
                      }
                      onClick={(event) => {
                        event.stopPropagation();
                        setSelectedImageIndex(index);
                      }}
                      className={cn(
                        "relative w-32 h-32 flex-shrink-0 transition-all duration-200 sm:w-36 sm:h-36",
                        isActive
                          ? "bg-primary/10 border-b-2 border-primary"
                          : "hover:bg-primary/5 border-b-2 border-transparent hover:border-primary/30"
                      )}
                      data-card-interactive="true"
                    >
                      <Thumbnail
                        asset={asset}
                        isSelected={false}
                        onSelect={() => {
                          setSelectedImageIndex(index);
                        }}
                        showBorder={false}
                        draggable={true}
                        onAction={onAssetAction}
                        onThumbnailClick={onThumbnailClick}
                        allAssets={allProductAssets}
                      />
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Small labels row: Type and Status */}
      {(product.product_type || product.status) && (
        <CardContent className="px-3 pt-2 pb-0">
          <div className="flex items-center justify-between gap-2 text-xs">
            <div className="flex items-center gap-2">
              {product.product_type && (
                <Badge
                  className="px-2 py-0.5 rounded-full text-xs normal-case font-normal shadow-none border-0 bg-muted/40 text-muted-foreground pointer-events-none select-none"
                  title="Product type"
                >
                  {product.product_type}
                </Badge>
              )}
              {product.status &&
                (() => {
                  const s = (product.status || "").toLowerCase();
                  const statusColorClass =
                    s === "active"
                      ? "bg-primary/10 text-primary"
                      : s === "draft"
                        ? "bg-amber-50 text-amber-700 dark:bg-amber-950/20 dark:text-amber-300"
                        : s === "archived"
                          ? "bg-muted/30 text-muted-foreground dark:bg-muted/20 dark:text-muted-foreground"
                          : "bg-muted/40 text-muted-foreground";
                  return (
                    <Badge
                      className={cn(
                        "px-2 py-0.5 rounded-full text-xs capitalize font-normal shadow-none border-0 pointer-events-none select-none",
                        statusColorClass
                      )}
                      title="Status"
                    >
                      {s}
                    </Badge>
                  );
                })()}
            </div>
            <span className="text-muted-foreground">
              {allProductAssets.length} asset
              {allProductAssets.length !== 1 ? "s" : ""}
            </span>
          </div>
        </CardContent>
      )}

      {/* Prompt Section */}
      <CardContent className="p-3" data-card-interactive="true">
        <SimplePromptInput
          productId={product.id.toString()}
          productTitle={product.title}
          value={prompts[product.id.toString()]}
          onChange={onPromptChange}
          className="space-y-0"
          onDebug={onDebug}
          onAssetAction={onAssetAction}
          onThumbnailClick={onThumbnailClick}
        />
      </CardContent>
    </Card>
  );
};

const MemoProductRow = React.memo(ProductRow, (prev, next) => {
  if (prev.product.id !== next.product.id) return false;

  const prevSel = prev.selectedProductIds.has(String(prev.product.id));
  const nextSel = next.selectedProductIds.has(String(next.product.id));

  const prevGen = (prev.generatedImages?.[String(prev.product.id)] || [])
    .length;
  const nextGen = (next.generatedImages?.[String(next.product.id)] || [])
    .length;

  const prevPrompt = prev.prompts[String(prev.product.id)];
  const nextPrompt = next.prompts[String(next.product.id)];

  const prevAssetsLen = prev.product.assets?.length || 0;
  const nextAssetsLen = next.product.assets?.length || 0;

  if (prevSel !== nextSel) return false;
  if (prevGen !== nextGen) return false;
  if (prevAssetsLen !== nextAssetsLen) return false;

  // Compare prompt shallowly by JSON size to avoid expensive deep checks for large lists
  if (JSON.stringify(prevPrompt) !== JSON.stringify(nextPrompt)) return false;

  return true;
});

export const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  selectedAssetIds,
  onAssetSelect,
  onAssetAction,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onSelectAllProducts,
  onCopyPromptToAll,
  generatedImages = {},
  availableCollections = [],
  collectionFilters = [],
  onCollectionFiltersChange,
  searchQuery = "",
  onSearchQueryChange,
  filterSelectedOnly = false,
  onFilterSelectedOnlyChange,
  filterHasGenerated = false,
  onFilterHasGeneratedChange,
  sortMode = "default",
  onSortModeChange,
  productTotalCount,
  onDebug,
  onThumbnailClick,
  scrollParentRef,
  columns = 2,
}) => {
  // Responsive grid virtualization (1–4 columns)
  const cols = Math.min(4, Math.max(1, columns ?? 2));
  const rowCount = Math.ceil(products.length / cols);
  const estimateRowHeight = 520; // tuned for taller thumbnail rows
  const rowVirtualizer = useVirtualizer({
    count: rowCount,
    getScrollElement: () => scrollParentRef?.current ?? null,
    estimateSize: () => estimateRowHeight,
    overscan: 10,
    measureElement: (el) =>
      el?.getBoundingClientRect().height ?? estimateRowHeight,
  });

  const virtualItems = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();

  React.useEffect(() => {
    rowVirtualizer.measure();
  }, [rowVirtualizer, cols, products.length]);

  return (
    <div className="relative w-full" style={{ height: totalSize }}>
      {virtualItems.map((vi) => {
        const baseIndex = vi.index * cols;

        return (
          <div
            key={vi.key}
            data-index={vi.index}
            className="absolute inset-x-0 grid w-full gap-6"
            ref={rowVirtualizer.measureElement}
            style={{
              transform: `translateY(${vi.start}px)`,
              top: 0,
              left: 0,
              width: "100%",
              paddingBottom: 24,
              gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))`,
            }}
          >
            {Array.from({ length: cols }).map((_, i) => {
              const p = products[baseIndex + i];
              return (
                p && (
                  <MemoProductRow
                    key={p.id}
                    product={p}
                    selectedAssetIds={selectedAssetIds}
                    onAssetSelect={onAssetSelect}
                    onAssetAction={onAssetAction}
                    prompts={prompts}
                    onPromptChange={onPromptChange}
                    onTabChange={onTabChange}
                    selectedProductIds={selectedProductIds}
                    onProductSelectionChange={onProductSelectionChange}
                    onCopyPromptToAll={onCopyPromptToAll}
                    generatedImages={generatedImages}
                    allAvailableAssets={[]}
                    allProducts={products}
                    onDebug={onDebug}
                    onThumbnailClick={onThumbnailClick}
                  />
                )
              );
            })}
          </div>
        );
      })}
    </div>
  );
};
