import React, { useMemo } from "react";
import { ScenePreset } from "./scenePresets";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";

interface SceneLibraryProps {
  scenes: ScenePreset[];
}

const BACKGROUND_CLASSES = [
  "from-sky-500/15 via-slate-500/10 to-indigo-500/15",
  "from-rose-500/15 via-amber-500/10 to-pink-500/15",
  "from-emerald-500/15 via-teal-500/10 to-cyan-500/15",
  "from-purple-500/15 via-blue-500/10 to-fuchsia-500/15",
  "from-amber-500/15 via-orange-500/10 to-rose-500/15",
  "from-slate-500/15 via-stone-500/10 to-zinc-500/15",
];

const getBackgroundClass = (id: string) => {
  const hash = Array.from(id).reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return BACKGROUND_CLASSES[hash % BACKGROUND_CLASSES.length];
};

const createDragPayload = (scene: ScenePreset) =>
  JSON.stringify({
    type: "scene_prompt",
    sceneId: scene.id,
    name: scene.name,
    prompt: scene.prompt,
  });

export const SceneLibrary: React.FC<SceneLibraryProps> = ({ scenes }) => {
  const sortedScenes = useMemo(
    () => scenes.slice().sort((a, b) => a.name.localeCompare(b.name)),
    [scenes]
  );

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-semibold text-foreground">Scene Presets</h3>
        <p className="text-xs text-muted-foreground mt-1">
          Drag a preset onto any prompt to append its styling description.
        </p>
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 xl:grid-cols-4 gap-3">
        {sortedScenes.map((scene) => (
          <Tooltip key={scene.id}>
            <TooltipTrigger asChild>
              <Card
                role="button"
                tabIndex={0}
                draggable
                onDragStart={(event) => {
                  event.dataTransfer.effectAllowed = "copy";
                  event.dataTransfer.setData(
                    "application/json",
                    createDragPayload(scene)
                  );
                  event.dataTransfer.setData("text/plain", scene.prompt);
                }}
                onKeyDown={(event) => {
                  if (event.key === "Enter" || event.key === " ") {
                    event.preventDefault();
                    if (navigator.clipboard) {
                      void navigator.clipboard.writeText(scene.prompt);
                    }
                  }
                }}
                className={cn(
                  "group relative aspect-square overflow-hidden border border-dashed border-muted-foreground/40 transition-all",
                  "hover:border-primary/60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40",
                  "bg-gradient-to-br",
                  getBackgroundClass(scene.id)
                )}
              >
                <div className="absolute inset-0 flex items-center justify-center px-4 text-center">
                  <span className="text-sm font-semibold uppercase tracking-tight text-foreground">
                    {scene.name}
                  </span>
                </div>
                <div className="absolute bottom-2 left-1/2 -translate-x-1/2">
                  <span className="rounded-full bg-background/60 px-2 py-0.5 text-[10px] font-medium text-muted-foreground opacity-0 transition-opacity group-hover:opacity-100">
                    Drag to use
                  </span>
                </div>
              </Card>
            </TooltipTrigger>
            <TooltipContent sideOffset={6} className="max-w-xs text-xs leading-relaxed">
              {scene.prompt}
            </TooltipContent>
          </Tooltip>
        ))}
      </div>
    </div>
  );
};
