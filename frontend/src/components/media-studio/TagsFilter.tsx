import React, { useState, useMemo, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { ChevronDown, X, Tag, Search } from "lucide-react";
import { cn } from "@/lib/utils";

interface TagsFilterProps {
  availableTags: string[];
  selectedTags: string[];
  onChange?: (selectedTags: string[]) => void;
  compact?: boolean;
}

export const TagsFilter: React.FC<TagsFilterProps> = ({
  availableTags,
  selectedTags,
  onChange,
  compact = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter tags based on search query
  const filteredTags = useMemo(() => {
    if (!searchQuery.trim()) {
      return availableTags || [];
    }
    const query = searchQuery.toLowerCase().trim();
    return (availableTags || []).filter((tag) =>
      tag.toLowerCase().includes(query)
    );
  }, [availableTags, searchQuery]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      // Small delay to ensure the dropdown is rendered
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleToggleTag = (tag: string) => {
    if (!onChange) return;

    const currentSelectedTags = selectedTags || [];
    const newSelectedTags = currentSelectedTags.includes(tag)
      ? currentSelectedTags.filter((t) => t !== tag)
      : [...currentSelectedTags, tag];

    onChange(newSelectedTags);
  };

  const handleRemoveTag = (tag: string) => {
    if (!onChange) return;
    onChange((selectedTags || []).filter((t) => t !== tag));
  };

  const handleDropdownToggle = () => {
    if (isOpen) {
      // Clear search when closing dropdown
      setSearchQuery("");
    }
    setIsOpen(!isOpen);
  };

  if (compact) {
    return (
      <div className="relative">
        <Button
          variant="outline"
          size="sm"
          onClick={handleDropdownToggle}
          className="px-2.5 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
        >
          <Tag className="h-3 w-3 mr-1" />
          Tags
          {(selectedTags || []).length > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 px-1 py-0 text-xs h-4 min-w-4"
            >
              {(selectedTags || []).length}
            </Badge>
          )}
          <ChevronDown className="h-3 w-3 ml-1" />
        </Button>

        {isOpen && (
          <div className="absolute right-0 mt-1 w-64 z-20 rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg p-3">
            <div className="space-y-3">
              {/* Search input */}
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-7 h-7 text-xs"
                />
              </div>

              <div className="max-h-48 overflow-y-auto space-y-2">
                {filteredTags.map((tag) => (
                  <label key={tag} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={(selectedTags || []).includes(tag)}
                      onChange={() => handleToggleTag(tag)}
                      className="h-3.5 w-3.5"
                    />
                    <span className="text-sm text-gray-800 dark:text-gray-100">
                      {tag}
                    </span>
                  </label>
                ))}
                {filteredTags.length === 0 &&
                  (availableTags || []).length > 0 && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 text-center py-2">
                      No tags match your search
                    </div>
                  )}
                {(availableTags || []).length === 0 && (
                  <div className="text-sm text-gray-500 dark:text-gray-400 text-center py-2">
                    No tags available
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-1">
                {(selectedTags || []).map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1 text-xs"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full p-0.5"
                    >
                      <X className="h-2.5 w-2.5" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Backdrop to close dropdown */}
        {isOpen && (
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Tags
        </label>
      </div>

      {/* Search input */}
      <div className="relative">
        <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
        <Input
          placeholder="Search tags..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-7 h-8 text-sm"
        />
      </div>

      <div className="flex flex-wrap gap-2">
        {(selectedTags || []).map((tag) => (
          <Badge
            key={tag}
            variant="secondary"
            className="flex items-center gap-1"
          >
            {tag}
            <button
              type="button"
              onClick={() => handleRemoveTag(tag)}
              className="ml-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full p-0.5"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>

      <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
        {filteredTags.map((tag) => (
          <label key={tag} className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={(selectedTags || []).includes(tag)}
              onChange={() => handleToggleTag(tag)}
              className="h-4 w-4"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {tag}
            </span>
          </label>
        ))}
        {filteredTags.length === 0 && (availableTags || []).length > 0 && (
          <div className="col-span-2 text-sm text-gray-500 dark:text-gray-400 text-center py-4">
            No tags match your search
          </div>
        )}
        {(availableTags || []).length === 0 && (
          <div className="col-span-2 text-sm text-gray-500 dark:text-gray-400 text-center py-4">
            No tags available
          </div>
        )}
      </div>
    </div>
  );
};
