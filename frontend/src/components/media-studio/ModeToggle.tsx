import React from "react";
import { GenerationMode } from "@/types/mediaStudio";
import { cn } from "@/lib/utils";
import { ImageIcon, VideoIcon, Type as TypeIcon } from "lucide-react";

interface ModeToggleProps {
  mode: GenerationMode;
  onChange: (mode: GenerationMode) => void;
  orientation?: "horizontal" | "vertical";
  fullWidth?: boolean;
  className?: string;
}

export const ModeToggle: React.FC<ModeToggleProps> = ({
  mode,
  onChange,
  orientation = "horizontal",
  fullWidth = false,
  className,
}) => {
  const items: Array<{
    value: GenerationMode;
    label: string;
    icon: React.ReactNode;
  }> = [
    { value: "text", label: "Text", icon: <TypeIcon className="w-3.5 h-3.5" /> },
    { value: "image", label: "Image", icon: <ImageIcon className="w-3.5 h-3.5" /> },
    { value: "video", label: "Video", icon: <VideoIcon className="w-3.5 h-3.5" /> },
  ];

  return (
    <div
      className={cn(
        "flex items-stretch rounded-lg bg-gray-100 p-1 dark:bg-gray-800",
        orientation === "vertical" ? "flex-col" : "flex-row",
        fullWidth && "w-full",
        className
      )}
    >
      {items.map((item) => {
        const isActive = mode === item.value;
        return (
          <button
            key={item.value}
            onClick={() => onChange(item.value)}
            className={cn(
              "flex-1 inline-flex items-center justify-center gap-1.5 rounded-md px-3 text-xs font-medium transition-all duration-200",
              orientation === "vertical" ? "py-2.5" : "py-2",
              isActive
                ? "bg-white text-gray-900 shadow-sm dark:bg-gray-700 dark:text-gray-100"
                : "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
            )}
            type="button"
          >
            {item.icon}
            {item.label}
          </button>
        );
      })}
    </div>
  );
};
