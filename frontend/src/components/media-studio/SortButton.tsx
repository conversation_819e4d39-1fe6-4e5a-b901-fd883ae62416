import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";

export type SortOption = 
  | "default"
  | "name_asc" 
  | "name_desc"
  | "images_asc"
  | "images_desc"
  | "selected_first"
  | "generated_first"
  | "created_asc"
  | "created_desc";

interface SortButtonProps {
  value: SortOption;
  onChange?: (value: SortOption) => void;
  compact?: boolean;
}

const SORT_OPTIONS = [
  { value: "default", label: "Default", icon: null },
  { value: "name_asc", label: "Name A-Z", icon: ArrowUp },
  { value: "name_desc", label: "Name Z-A", icon: ArrowDown },
  { value: "images_asc", label: "Images ↑", icon: ArrowUp },
  { value: "images_desc", label: "Images ↓", icon: ArrowDown },
  { value: "selected_first", label: "Selected First", icon: null },
  { value: "generated_first", label: "Generated First", icon: null },
  { value: "created_asc", label: "Oldest First", icon: ArrowUp },
  { value: "created_desc", label: "Newest First", icon: ArrowDown },
] as const;

export const SortButton: React.FC<SortButtonProps> = ({
  value,
  onChange,
  compact = true,
}) => {
  const selectedOption = SORT_OPTIONS.find(option => option.value === value);
  
  const getDisplayIcon = () => {
    if (value === "default") return <ArrowUpDown className="h-3 w-3" />;
    
    const option = SORT_OPTIONS.find(opt => opt.value === value);
    if (option?.icon) {
      const Icon = option.icon;
      return <Icon className="h-3 w-3" />;
    }
    
    return <ArrowUpDown className="h-3 w-3" />;
  };

  const getDisplayText = () => {
    if (value === "default") return "Sort";
    return selectedOption?.label || "Sort";
  };

  if (compact) {
    return (
      <Select value={value} onValueChange={(val) => onChange?.(val as SortOption)}>
        <SelectTrigger className="w-auto h-8 px-2.5 text-xs rounded-full border-input hover:bg-muted/50 transition-colors [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground">
          <div className="flex items-center gap-1.5">
            {getDisplayIcon()}
            <span className="hidden sm:inline">{getDisplayText()}</span>
          </div>
        </SelectTrigger>
        <SelectContent align="end" className="min-w-[160px]">
          <SelectItem value="default">
            <div className="flex items-center gap-2">
              <ArrowUpDown className="h-3 w-3 text-muted-foreground" />
              Default
            </div>
          </SelectItem>
          
          <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground border-b">
            By Name
          </div>
          <SelectItem value="name_asc">
            <div className="flex items-center gap-2">
              <ArrowUp className="h-3 w-3 text-muted-foreground" />
              Name A-Z
            </div>
          </SelectItem>
          <SelectItem value="name_desc">
            <div className="flex items-center gap-2">
              <ArrowDown className="h-3 w-3 text-muted-foreground" />
              Name Z-A
            </div>
          </SelectItem>
          
          <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground border-b">
            By Image Count
          </div>
          <SelectItem value="images_asc">
            <div className="flex items-center gap-2">
              <ArrowUp className="h-3 w-3 text-muted-foreground" />
              Fewest Images
            </div>
          </SelectItem>
          <SelectItem value="images_desc">
            <div className="flex items-center gap-2">
              <ArrowDown className="h-3 w-3 text-muted-foreground" />
              Most Images
            </div>
          </SelectItem>
          
          <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground border-b">
            By Status
          </div>
          <SelectItem value="selected_first">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-sm bg-primary/20 border border-primary/40" />
              Selected First
            </div>
          </SelectItem>
          <SelectItem value="generated_first">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-sm bg-green-500/20 border border-green-500/40" />
              Generated First
            </div>
          </SelectItem>
          
          <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground border-b">
            By Date
          </div>
          <SelectItem value="created_desc">
            <div className="flex items-center gap-2">
              <ArrowDown className="h-3 w-3 text-muted-foreground" />
              Newest First
            </div>
          </SelectItem>
          <SelectItem value="created_asc">
            <div className="flex items-center gap-2">
              <ArrowUp className="h-3 w-3 text-muted-foreground" />
              Oldest First
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // Non-compact version
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium">Sort Products</label>
      <Select value={value} onValueChange={(val) => onChange?.(val as SortOption)}>
        <SelectTrigger className="w-full">
          <div className="flex items-center gap-2">
            {getDisplayIcon()}
            <SelectValue placeholder="Choose sort order" />
          </div>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="default">Default Order</SelectItem>
          <SelectItem value="name_asc">Name A-Z</SelectItem>
          <SelectItem value="name_desc">Name Z-A</SelectItem>
          <SelectItem value="images_asc">Fewest Images First</SelectItem>
          <SelectItem value="images_desc">Most Images First</SelectItem>
          <SelectItem value="selected_first">Selected Products First</SelectItem>
          <SelectItem value="generated_first">Generated Content First</SelectItem>
          <SelectItem value="created_desc">Newest Products First</SelectItem>
          <SelectItem value="created_asc">Oldest Products First</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};
