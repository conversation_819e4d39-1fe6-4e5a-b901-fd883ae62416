import React from "react";
import { MODELS } from "./constants";
import type {
  GenerationMode,
  ImageSettings,
  VideoSettings,
} from "@/types/mediaStudio";
import { ModeToggle } from "./ModeToggle";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  ModelSelect,
  BrandGIcon,
  FluxIcon,
  WanIcon,
  MultiRefIcon,
  OpenAIIcon,
} from "./ModelSelect";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";

import { cn } from "@/lib/utils";
import {
  <PERSON>es,
  Sparkles,
  Mountain,
  ChevronRight,
  Plus,
  Settings2,
  DownloadCloud,
  UploadCloud,
} from "lucide-react";

type GenerationBatch = {
  batchId: string;
  total: number;
  completed: number;
  failed: number;
  status: string;
};

type ToastType = "error" | "success" | "info" | "warn";

type Toast = {
  id: number;
  type: ToastType;
  text: string;
  createdAt: number;
};

export interface BaseGenerateControlsProps {
  selectedCount: number;
  generationMode: GenerationMode;
  onModeChange: (mode: GenerationMode) => void;
  settings: ImageSettings | VideoSettings;
  setSettings: React.Dispatch<
    React.SetStateAction<ImageSettings | VideoSettings>
  >;
  selectedModelId: string;
  onModelChange: (modelId: string) => void;
  onGenerate?: () => void;
  generationBatch?: GenerationBatch | null;
  isInitiating?: boolean;
  initiationMessage?: string | null;
  overLimit?: boolean;
  overLimitMessage?: string;
  errorMessage?: string | null;
  onDismissMessage?: () => void;
  onDismissInitiationMessage?: () => void;
  lastRequestId?: string | null;
  // Selection controls (moved from header row)
  selectionAllLabel?: string; // e.g., "3/50" or "50/500"
  allSelected?: boolean;
  onSelectAllChange?: (checked: boolean) => void;
  filterSelectedOnly?: boolean;
  onFilterSelectedOnlyChange?: (checked: boolean) => void;
  filteredCount?: number;
  totalCount?: number;
  onSaveGroup?: (groupName: string) => Promise<void> | void;
  isSavingGroup?: boolean;
}

export interface GenerateControlsProps extends BaseGenerateControlsProps {
  variant?: "floating" | "sidebar";
  className?: string;
}

const toastBackgroundClasses: Record<ToastType, string> = {
  error: "bg-destructive/10 text-destructive border border-destructive/20",
  success: "bg-green-500/10 text-green-700 border border-green-500/20",
  warn: "bg-amber-500/10 text-amber-700 border border-amber-500/20",
  info: "bg-primary/10 text-primary border border-primary/20",
};

const formatSeconds = (seconds: number): string => {
  if (!Number.isFinite(seconds) || seconds < 0) return "–";
  if (seconds < 60) return `${seconds}s`;
  const minutes = Math.floor(seconds / 60);
  const remaining = seconds % 60;
  if (remaining === 0) return `${minutes}m`;
  return `${minutes}m ${remaining}s`;
};

export const GenerateControls: React.FC<GenerateControlsProps> = ({
  variant = "floating",
  className,
  ...props
}) => {
  const {
    selectedCount,
    generationMode,
    onModeChange,
    settings,
    setSettings,
    selectedModelId,
    onModelChange,
    onGenerate,
    generationBatch,
    isInitiating = false,
    initiationMessage,
    overLimit = false,
    overLimitMessage = "Select up to 5 products.",
    errorMessage,
    onDismissMessage,
    onDismissInitiationMessage,
    lastRequestId,
    selectionAllLabel,
    allSelected,
    onSelectAllChange,
    filterSelectedOnly,
    onFilterSelectedOnlyChange,
    filteredCount,
    totalCount,
    onSaveGroup,
    isSavingGroup = false,
  } = props;

  void onDismissInitiationMessage;

  const [isSaveGroupOpen, setIsSaveGroupOpen] = React.useState(false);
  const [groupName, setGroupName] = React.useState("");
  const [groupNameError, setGroupNameError] = React.useState<string | null>(
    null
  );

  const noSelection = selectedCount === 0;
  const isTextMode = generationMode === "text";
  const isGenerateDisabled =
    noSelection || overLimit || isInitiating || isTextMode;

  const VIDEO_MODELS: {
    value: string;
    label: string;
    eta?: string;
    cost?: number;
  }[] = [
    { value: "veo-2.0-generate-001", label: "Veo 2", eta: "~20s", cost: 8 },
    {
      value: "veo-3.0-fast-generate-preview",
      label: "Veo 3 Fast (Preview)",
      eta: "~10s",
      cost: 10,
    },
    {
      value: "veo-3.0-generate-preview",
      label: "Veo 3 (Preview)",
      eta: "~20s",
      cost: 12,
    },
  ];

  const selectedModel =
    generationMode === "video"
      ? VIDEO_MODELS.find((m) => m.value === selectedModelId) || null
      : MODELS.find((m) => m.id === selectedModelId) || null;

  const eta =
    selectedModel && (selectedModel as any).eta
      ? (selectedModel as any).eta
      : "~?s";

  const parseSeconds = (s: string): number | null => {
    if (!s) return null;
    const m = String(s).match(/(\d+)/);
    if (!m) return null;
    const n = parseInt(m[1], 10);
    return Number.isFinite(n) ? n : null;
  };

  const etaSeconds = parseSeconds(eta);
  const totalSeconds = etaSeconds != null ? etaSeconds * selectedCount : null;
  const costPerAsset =
    selectedModel && (selectedModel as any).cost !== undefined
      ? ((selectedModel as any).cost as number)
      : 0;
  const totalCost = selectedCount * costPerAsset;

  const handleSettingsChange = (
    field: keyof (ImageSettings | VideoSettings),
    value: string | number
  ) => {
    setSettings((prev: ImageSettings | VideoSettings) => ({
      ...prev,
      [field]: value,
    }));
  };

  const quality = "quality" in settings ? settings.quality : "Standard";

  const IMAGE_MODELS = [
    {
      value: "gemini-2.5-flash-image-preview",
      title: "Nano Banana",
      subtitle: "Google's advanced image editing model",
      badge: "Premium",
      disabled: false,
      icon: <BrandGIcon className="h-5 w-5" />,
    },
    {
      value: "flux-kontext-max",
      title: "Flux Kontext Max",
      subtitle: "Edit with accuracy",
      badge: "Premium",
      disabled: true,
      comingSoon: true,
      icon: <FluxIcon className="h-5 w-5" />,
    },
    {
      value: "dall-e-3",
      title: "GPT Image",
      subtitle: "OpenAI's powerful image tool",
      badge: "Premium",
      disabled: true,
      comingSoon: true,
      icon: <OpenAIIcon className="h-5 w-5" />,
    },
    {
      value: "midjourney-v6",
      title: "Multi-Reference",
      subtitle: "Multiple edits in one shot",
      badge: "Premium",
      disabled: true,
      comingSoon: true,
      icon: <MultiRefIcon className="h-5 w-5" />,
    },
    {
      value: "wan-2.2",
      title: "WAN 2.2",
      subtitle: "High-fidelity cinematic visuals",
      badge: undefined,
      disabled: true,
      comingSoon: true,
      icon: <WanIcon className="h-5 w-5" />,
    },
  ];

  const modelOptions = generationMode === "video" ? VIDEO_MODELS : IMAGE_MODELS;

  const aspectRatioOptions = (
    generationMode === "image"
      ? ["1:1", "16:9", "9:16", "4:3", "3:4"]
      : ["16:9", "9:16"]
  ).map((ratio) => ({ value: ratio, label: ratio }));

  React.useEffect(() => {
    if (generationMode !== "video") return;
    const valid = VIDEO_MODELS.some((m) => m.value === selectedModelId);
    if (!valid && VIDEO_MODELS.length > 0) {
      onModelChange(VIDEO_MODELS[0].value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [generationMode]);

  React.useEffect(() => {
    if (generationMode !== "image") return;
    const valid = IMAGE_MODELS.some(
      (m) => m.value === selectedModelId && !m.disabled
    );
    if (!valid && IMAGE_MODELS.length > 0) {
      const firstEnabled = IMAGE_MODELS.find((m) => !m.disabled);
      if (firstEnabled) onModelChange(firstEnabled.value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [generationMode]);

  const isVeo2 =
    generationMode === "video" && selectedModelId.startsWith("veo-2.0");
  const isVeo3 =
    generationMode === "video" && selectedModelId.startsWith("veo-3.0");

  const ar = (settings as any).aspectRatio as string;
  const currentResolution: string = (settings as any).resolution || "720p";

  const resolutionOptions = React.useMemo(() => {
    if (generationMode !== "video")
      return [] as { value: string; label: string }[];
    if (isVeo2) return ["720p"].map((r) => ({ value: r, label: r }));
    if (ar === "9:16") return ["720p"].map((r) => ({ value: r, label: r }));
    return ["720p", "1080p"].map((r) => ({ value: r, label: r }));
  }, [generationMode, isVeo2, ar]);

  React.useEffect(() => {
    if (generationMode !== "video") return;
    const allowed = resolutionOptions.map((o) => o.value);
    if (!allowed.includes(currentResolution) && allowed.length > 0) {
      handleSettingsChange("resolution" as any, allowed[0]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    generationMode,
    resolutionOptions.map((o) => o.value).join("|"),
    currentResolution,
    isVeo2,
    ar,
  ]);

  const durationOptions = React.useMemo(
    () =>
      generationMode === "video"
        ? [
            { value: "5", label: "5s" },
            { value: "8", label: "8s" },
          ]
        : [],
    [generationMode]
  );

  const currentDuration = String((settings as any).duration ?? "8");

  React.useEffect(() => {
    if (generationMode !== "video") return;
    const allowed = durationOptions.map((o) => o.value);
    if (!allowed.includes(currentDuration) && allowed.length > 0) {
      handleSettingsChange("duration" as any, Number(allowed[0]));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    generationMode,
    currentDuration,
    durationOptions.map((o) => o.value).join("|"),
  ]);

  const qualityOptions = [
    { value: "Standard", label: "Standard" },
    { value: "High", label: "High" },
  ];

  const handleGenerate = () => {
    if (onGenerate && !isGenerateDisabled) {
      onGenerate();
    }
  };

  const generateButtonLabel = isTextMode
    ? "Text generation coming soon"
    : isInitiating
      ? "Starting…"
      : `Generate ${selectedCount > 0 ? `(${selectedCount})` : ""}`;

  const filteredDisplay = filteredCount ?? 0;
  const totalDisplay = totalCount ?? filteredDisplay;

  const handleOpenSaveGroup = () => {
    setGroupName("");
    setGroupNameError(null);
    setIsSaveGroupOpen(true);
  };

  const handleSaveGroup = async () => {
    const trimmed = groupName.trim();
    if (!trimmed) {
      setGroupNameError("Enter a group name");
      return;
    }
    if (!onSaveGroup) {
      setIsSaveGroupOpen(false);
      return;
    }
    try {
      await onSaveGroup(trimmed);
      setIsSaveGroupOpen(false);
      setGroupName("");
      setGroupNameError(null);
    } catch (error) {
      setGroupNameError(
        error instanceof Error
          ? error.message
          : "Unable to save group. Please try again."
      );
    }
  };

  const progressText = generationBatch
    ? `${generationBatch.completed}/${generationBatch.total} completed${
        generationBatch.failed ? ` • ${generationBatch.failed} failed` : ""
      }`
    : "";

  const isProcessing =
    !!generationBatch &&
    generationBatch.completed < (generationBatch.total || 0);
  const isCompleted =
    !!generationBatch &&
    generationBatch.total > 0 &&
    generationBatch.completed >= generationBatch.total;

  const statusIndicator = generationBatch ? (
    <div className="flex items-center gap-2">
      {errorMessage ? (
        <div
          className="flex h-5 w-5 items-center justify-center rounded-full bg-red-500"
          title="Error"
          aria-label="Error"
        >
          <svg
            className="h-3 w-3 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm-1.414-5.414a1 1 0 011.414 0L10 12.586l.707-.707a1 1 0 111.414 1.414L11.414 14l.707.707a1 1 0 01-1.414 1.414L10 15.414l-.707.707a1 1 0 01-1.414-1.414L8.586 14l-.707-.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      ) : isProcessing ? (
        <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent" />
      ) : isCompleted ? (
        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-green-500">
          <svg
            className="h-3 w-3 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      ) : null}
      {progressText && <span>{progressText}</span>}
    </div>
  ) : null;

  const statusRow =
    statusIndicator || lastRequestId ? (
      <div
        className={cn(
          "flex flex-wrap items-center gap-3 text-xs text-muted-foreground",
          statusIndicator ? "justify-between" : "justify-end"
        )}
      >
        {statusIndicator}
        {lastRequestId && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigator.clipboard.writeText(lastRequestId)}
            title={lastRequestId}
            className="px-2 py-[2px] h-7 text-xs"
          >
            id: {lastRequestId.slice(0, 8)}
          </Button>
        )}
      </div>
    ) : null;

  const [toasts, setToasts] = React.useState<Toast[]>([]);
  const currentErrorToastId = React.useRef<number | null>(null);

  const addToast = React.useCallback(
    (type: ToastType, text: string, ms: number): number => {
      const id = Date.now() + Math.floor(Math.random() * 1000);
      const createdAt = Date.now();
      setToasts((prev) => [{ id, type, text, createdAt }, ...prev]);
      window.setTimeout(() => {
        setToasts((prev) => prev.filter((t) => t.id !== id));
        if (type === "error" && currentErrorToastId.current === id) {
          onDismissMessage && onDismissMessage();
        }
      }, ms);
      return id;
    },
    [onDismissMessage]
  );

  const prevError = React.useRef<string | null>(null);
  React.useEffect(() => {
    if (errorMessage && prevError.current !== errorMessage) {
      const id = addToast("error", errorMessage, 8000);
      currentErrorToastId.current = id;
    }
    prevError.current = errorMessage || null;
  }, [errorMessage, addToast]);

  const prevInitMsg = React.useRef<string | null>(null);
  React.useEffect(() => {
    if (initiationMessage && prevInitMsg.current !== initiationMessage) {
      addToast("info", initiationMessage, 5000);
    }
    prevInitMsg.current = initiationMessage || null;
  }, [initiationMessage, addToast]);

  const prevOverLimit = React.useRef<boolean>(false);
  React.useEffect(() => {
    if (overLimit && !prevOverLimit.current && overLimitMessage) {
      addToast("warn", overLimitMessage, 4000);
    }
    prevOverLimit.current = !!overLimit;
  }, [overLimit, overLimitMessage, addToast]);

  const completionText = generationBatch
    ? `${generationBatch.completed}/${generationBatch.total} completed`
    : "";
  const completedToastGuard = React.useRef<number>(0);
  React.useEffect(() => {
    if (isCompleted) {
      const now = Date.now();
      if (now - completedToastGuard.current > 500) {
        addToast("success", `Generation finished. ${completionText}.`, 6000);
        completedToastGuard.current = now;
      }
    }
  }, [isCompleted, completionText, addToast]);

  const toastItems = toasts
    .slice()
    .sort((a, b) => b.createdAt - a.createdAt)
    .map((t) => (
      <div
        key={t.id}
        className={cn(
          "w-full flex items-start justify-between gap-3 px-3 py-2 rounded-lg",
          toastBackgroundClasses[t.type]
        )}
      >
        <span className="text-sm whitespace-normal break-words">{t.text}</span>
        <button
          className="ml-2 opacity-70 hover:opacity-100"
          onClick={() => {
            setToasts((prev) => prev.filter((x) => x.id !== t.id));
            if (t.type === "error" && currentErrorToastId.current === t.id) {
              onDismissMessage && onDismissMessage();
            }
          }}
          aria-label="Dismiss notification"
        >
          ✕
        </button>
      </div>
    ));

  const selectors = (
    <>
      <Select
        value={settings.aspectRatio}
        onValueChange={(value: string) => {
          handleSettingsChange("aspectRatio", value);
          if (generationMode === "video") {
            if ((isVeo3 || isVeo2) && value === "9:16") {
              handleSettingsChange("resolution" as any, "720p");
            }
          }
        }}
      >
        <SelectTrigger
          size="sm"
          className="w-32 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
        >
          <SelectValue placeholder="Select aspect ratio" />
        </SelectTrigger>
        <SelectContent>
          {aspectRatioOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {generationMode === "video" && (
        <Select
          value={currentResolution}
          onValueChange={(value: string) =>
            handleSettingsChange("resolution" as any, value)
          }
        >
          <SelectTrigger
            size="sm"
            className="w-28 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
          >
            <SelectValue placeholder="Select resolution" />
          </SelectTrigger>
          <SelectContent>
            {resolutionOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
      {generationMode === "image" && (
        <Select
          value={quality}
          onValueChange={(value: string) =>
            handleSettingsChange("quality", value)
          }
        >
          <SelectTrigger
            size="sm"
            className="w-32 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
          >
            <SelectValue placeholder="Select quality" />
          </SelectTrigger>
          <SelectContent>
            {qualityOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
      {generationMode === "image" ? (
        <ModelSelect
          label="AI Model"
          value={selectedModelId}
          onChange={(val: string) => onModelChange(val)}
          options={IMAGE_MODELS as any}
          dense
          className="w-36"
        />
      ) : generationMode === "video" ? (
        <Select
          value={selectedModelId}
          onValueChange={(val: string) => {
            onModelChange(val);
            const nextIsVeo2 = val.startsWith("veo-2.0");
            const arNow = (settings as any).aspectRatio as string;
            const resAllowed = nextIsVeo2
              ? ["720p"]
              : arNow === "9:16"
                ? ["720p"]
                : ["720p", "1080p"];
            if (!resAllowed.includes(currentResolution)) {
              handleSettingsChange("resolution" as any, resAllowed[0]);
            }
          }}
        >
          <SelectTrigger
            size="sm"
            className="w-36 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
          >
            <SelectValue placeholder="Select AI model" />
          </SelectTrigger>
          <SelectContent>
            {modelOptions.map((option: any) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : null}
      {generationMode === "video" && (
        <Select
          value={currentDuration}
          onValueChange={(value: string) =>
            handleSettingsChange("duration" as any, Number(value))
          }
        >
          <SelectTrigger
            size="sm"
            className="w-20 [&>svg]:text-muted-foreground [&>svg]:hover:text-foreground hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
          >
            <SelectValue placeholder="Select duration" />
          </SelectTrigger>
          <SelectContent>
            {durationOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
    </>
  );

  const creativeSections = [
    {
      key: "items",
      title: "Items",
      subtitle:
        selectedCount > 0
          ? `${selectedCount.toLocaleString()} selected`
          : "Select products to begin",
      icon: <Boxes className="h-4 w-4" />,
      badge:
        selectedCount > 0 ? (
          <Badge
            variant="secondary"
            className="px-2 py-0 text-[10px] uppercase"
          >
            Active
          </Badge>
        ) : null,
      trailing: (
        <div className="flex items-center gap-1.5 text-muted-foreground">
          <span className="flex h-7 w-7 items-center justify-center rounded-md border border-dashed border-border/60 bg-card/80">
            <Plus className="h-3.5 w-3.5" />
          </span>
          <ChevronRight className="h-4 w-4 opacity-60" />
        </div>
      ),
    },
    {
      key: "models",
      title: "Models",
      subtitle: selectedModel?.label
        ? `${selectedModel.label} • linked`
        : selectedCount > 0
          ? "Assign models to selected items"
          : "Choose models for products",
      icon:
        generationMode === "video" ? (
          <Sparkles className="h-4 w-4" />
        ) : (
          <Sparkles className="h-4 w-4" />
        ),
      badge: (
        <Badge variant="outline" className="px-2 py-0 text-[10px] uppercase">
          {generationMode}
        </Badge>
      ),
      trailing: (
        <div className="flex items-center gap-1.5 text-muted-foreground">
          <span className="flex h-7 w-7 items-center justify-center rounded-md border border-border/60 bg-card/80">
            <Settings2 className="h-3.5 w-3.5" />
          </span>
          <ChevronRight className="h-4 w-4 opacity-60" />
        </div>
      ),
    },
    {
      key: "scene",
      title: "Scene",
      subtitle:
        generationMode === "video"
          ? `${currentResolution.toUpperCase()} • ${settings.aspectRatio}`
          : `${settings.aspectRatio}${quality ? ` • ${quality}` : ""}`,
      icon: <Mountain className="h-4 w-4" />,
      badge: (
        <Badge variant="outline" className="px-2 py-0 text-[10px] uppercase">
          Setup
        </Badge>
      ),
      trailing: (
        <div className="flex items-center gap-1.5 text-muted-foreground">
          <span className="flex h-7 w-7 items-center justify-center rounded-md border border-border/60 bg-card/80">
            <Sparkles className="h-3.5 w-3.5" />
          </span>
          <ChevronRight className="h-4 w-4 opacity-60" />
        </div>
      ),
    },
  ];

  const creativeSectionNodes = creativeSections.map((section) => (
    <button
      key={section.key}
      type="button"
      className="flex w-full items-center justify-between rounded-lg border border-border/70 bg-card/80 px-3 py-3 text-left shadow-sm transition hover:bg-muted/60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40"
    >
      <span className="flex items-center gap-3">
        <span className="flex h-9 w-9 items-center justify-center rounded-md bg-muted/40 text-muted-foreground">
          {section.icon}
        </span>
        <span className="flex flex-col">
          <span className="text-sm font-semibold text-foreground">
            {section.title}
          </span>
          <span className="text-xs text-muted-foreground">
            {section.subtitle}
          </span>
        </span>
      </span>
      <span className="flex items-center gap-2">
        {section.badge}
        {section.trailing}
      </span>
    </button>
  ));

  if (variant === "floating") {
    return (
      <div
        className={cn(
          "fixed bottom-2 left-1/2 -translate-x-1/2 z-50",
          className
        )}
      >
        <Card className="backdrop-blur-sm shadow-2xl min-w-[720px]">
          <CardContent className="flex flex-col gap-4 p-3">
            {toastItems.length > 0 && (
              <div className="w-full flex flex-col gap-2">{toastItems}</div>
            )}
            <div className="flex justify-center">
              <ModeToggle
                mode={generationMode}
                onChange={onModeChange}
                orientation="horizontal"
                className="h-10"
              />
            </div>
            {statusRow}
            <div className="flex flex-wrap items-end justify-between gap-3">
              <div className="flex flex-wrap items-center gap-3">
                {selectors}
              </div>
              <Button
                onClick={handleGenerate}
                disabled={isGenerateDisabled}
                className="h-14 min-w-[220px] px-6 text-base font-semibold"
                size="lg"
              >
                {generateButtonLabel}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const perAssetEta = etaSeconds != null ? `${etaSeconds}s each` : "—";
  const totalEta =
    etaSeconds != null && totalSeconds != null
      ? formatSeconds(totalSeconds)
      : "—";
  const costBreakdown = costPerAsset > 0 ? `$${costPerAsset} each` : "—";
  const totalCostDisplay = costPerAsset > 0 ? `$${totalCost}` : "—";

  return (
    <div
      className={cn(
        "flex h-full w-[320px] flex-col border-r border-border/80 bg-card/70 backdrop-blur-sm",
        className
      )}
    >
      <div className="flex h-12 items-center border-b border-border/70 bg-card px-4">
        <span className="text-xs font-semibold uppercase tracking-[0.2em] text-muted-foreground">
          AI Art Director
        </span>
      </div>
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-6">
        {toastItems.length > 0 && (
          <div className="flex flex-col gap-2">{toastItems}</div>
        )}
        {/* Shopify Sync actions */}
        <div className="flex flex-col gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="h-7 px-2"
                onClick={() => {
                  // TODO: implement sync-all-from-shopify
                  console.log("Sync All FROM Shopify clicked");
                }}
              >
                <DownloadCloud className="h-3.5 w-3.5 mr-1" />
                <span className="text-xs">Sync All FROM Shopify</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              Get all latest products from Shopify
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="h-7 px-2"
                disabled={selectedCount === 0}
                onClick={() => {
                  // TODO: implement sync-selected-to-shopify
                  console.log("Sync Selected TO Shopify clicked");
                }}
              >
                <UploadCloud className="h-3.5 w-3.5 mr-1" />
                <span className="text-xs">Sync Selected TO Shopify</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              push the selected items with the selected images from here TO the
              shopify database
            </TooltipContent>
          </Tooltip>
        </div>

        <div className="space-y-3">
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>
                Matching
                <span className="ml-1 font-semibold text-foreground">
                  {totalDisplay.toLocaleString()}
                </span>
                {totalDisplay !== filteredDisplay && (
                  <span className="ml-1 text-muted-foreground/70">
                    • showing {filteredDisplay.toLocaleString()}
                  </span>
                )}
              </span>
              <span>
                Selected
                <span className="ml-1 font-semibold text-foreground">
                  {selectedCount.toLocaleString()}
                </span>
              </span>
            </div>
          </div>
          {/* Selection controls moved from header row */}
          <div className="flex items-center gap-3">
            {typeof allSelected !== "undefined" &&
              onSelectAllChange &&
              selectionAllLabel && (
                <div className="flex items-center gap-1.5 px-1.5 py-0.5 rounded bg-muted/20">
                  <Checkbox
                    id="gc-select-all"
                    checked={!!allSelected}
                    onCheckedChange={(c) => onSelectAllChange?.(c === true)}
                    className="h-3.5 w-3.5"
                  />
                  <label
                    htmlFor="gc-select-all"
                    className="text-[11px] text-muted-foreground cursor-pointer select-none font-mono"
                  >
                    {selectionAllLabel}
                  </label>
                </div>
              )}
            {typeof filterSelectedOnly !== "undefined" &&
              onFilterSelectedOnlyChange && (
                <label className="flex items-center gap-2 text-xs text-muted-foreground cursor-pointer select-none">
                  <Checkbox
                    id="gc-selected-only"
                    checked={!!filterSelectedOnly}
                    onCheckedChange={(c) =>
                      onFilterSelectedOnlyChange?.(c === true)
                    }
                    className="h-3.5 w-3.5"
                  />
                  <span>Selected only</span>
                </label>
              )}
          </div>

          {onSaveGroup && (
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="secondary"
                size="sm"
                className="mt-1"
                onClick={handleOpenSaveGroup}
                disabled={selectedCount === 0 || isTextMode}
              >
                Save selection as group
              </Button>
              {selectedCount === 0 && (
                <span className="text-[11px] text-muted-foreground/70">
                  Select items first
                </span>
              )}
            </div>
          )}

          <div className="pt-1">
            <ModeToggle
              mode={generationMode}
              onChange={onModeChange}
              orientation="horizontal"
              fullWidth
              className="h-10 w-full"
            />
          </div>

          {statusRow}
        </div>
        <div className="space-y-4">
          <div className="space-y-2">{creativeSectionNodes}</div>
          <div className="space-y-3">
            <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Settings
            </label>
            <div className="flex flex-wrap gap-3">
              {React.Children.toArray(selectors)}
            </div>
          </div>
          <Button
            onClick={handleGenerate}
            disabled={isGenerateDisabled}
            className="h-14 w-full text-base font-semibold"
            size="lg"
          >
            {generateButtonLabel}
          </Button>
          <div className="grid grid-cols-2 gap-3 text-xs text-muted-foreground">
            <div className="flex flex-col gap-1">
              <span className="font-medium text-foreground/80">ETA</span>
              <span>
                Per asset: {perAssetEta}
                <br />
                Total: {totalEta}
              </span>
            </div>
            <div className="flex flex-col gap-1">
              <span className="font-medium text-foreground/80">Cost</span>
              <span>
                Per asset: {costBreakdown}
                <br />
                Total: {totalCostDisplay}
              </span>
            </div>
          </div>
          {isProcessing && (
            <p className="text-center text-xs text-muted-foreground">
              Generating assets… you can continue browsing products.
            </p>
          )}
        </div>
      </div>

      <Dialog open={isSaveGroupOpen} onOpenChange={setIsSaveGroupOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Save selection as group</DialogTitle>
          </DialogHeader>
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Enter a name for this group to recall the current selection of
              products later.
            </p>
            <Input
              autoFocus
              value={groupName}
              onChange={(event) => {
                setGroupName(event.target.value);
                if (groupNameError) setGroupNameError(null);
              }}
              placeholder="e.g. Summer launch set"
              disabled={isSavingGroup}
            />
            {groupNameError && (
              <p className="text-xs text-destructive">{groupNameError}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {selectedCount.toLocaleString()} product
              {selectedCount === 1 ? "" : "s"} will be stored in this group.
            </p>
          </div>
          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="ghost"
              onClick={() => setIsSaveGroupOpen(false)}
              disabled={isSavingGroup}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleSaveGroup}
              disabled={isSavingGroup}
            >
              {isSavingGroup ? "Saving…" : "Save group"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
