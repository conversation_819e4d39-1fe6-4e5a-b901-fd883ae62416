import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, X, Folder } from "lucide-react";
import { cn } from "@/lib/utils";

interface CollectionsFilterProps {
  options: Array<{ id: string; name: string; color: string }>;
  selectedIds: string[];
  onChange?: (selectedIds: string[]) => void;
  compact?: boolean;
  iconOnly?: boolean; // when compact, render icon-only trigger
}

export const CollectionsFilter: React.FC<CollectionsFilterProps> = ({
  options,
  selectedIds,
  onChange,
  compact = false,
  iconOnly = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const selectedOptions = (options || []).filter((option) =>
    selectedIds.includes(option.id)
  );

  const handleToggleOption = (optionId: string) => {
    if (!onChange) return;

    const currentSelectedIds = selectedIds || [];
    const newSelectedIds = currentSelectedIds.includes(optionId)
      ? currentSelectedIds.filter((id) => id !== optionId)
      : [...currentSelectedIds, optionId];

    onChange(newSelectedIds);
  };

  const handleRemoveOption = (optionId: string) => {
    if (!onChange) return;
    onChange((selectedIds || []).filter((id) => id !== optionId));
  };

  const handleClearAll = () => {
    if (!onChange) return;
    onChange([]);
  };

  if (compact) {
    return (
      <div className="relative">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="px-2.5 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-primary/10 hover:text-primary-hover transition-colors duration-200"
          aria-label="Collections"
          title="Collections"
        >
          <Folder className="h-3 w-3" />
          {!iconOnly && <span className="ml-1">Collections</span>}
          {(selectedIds || []).length > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 px-1 py-0 text-xs h-4 min-w-4"
            >
              {(selectedIds || []).length}
            </Badge>
          )}
          <ChevronDown className="h-3 w-3 ml-1" />
        </Button>

        {isOpen && (
          <div className="absolute right-0 mt-1 w-64 z-20 rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg p-3">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-[11px] uppercase tracking-wide text-gray-500 dark:text-gray-400">
                  Collections
                </div>
                {(selectedIds || []).length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearAll}
                    className="h-5 px-2 text-xs"
                  >
                    Clear all
                  </Button>
                )}
              </div>

              <div className="max-h-48 overflow-y-auto space-y-2">
                {(options || []).map((option) => (
                  <label key={option.id} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={(selectedIds || []).includes(option.id)}
                      onChange={() => handleToggleOption(option.id)}
                      className="h-3.5 w-3.5"
                    />
                    <span className="text-sm text-gray-800 dark:text-gray-100">
                      {option.name}
                    </span>
                  </label>
                ))}
                {(options || []).length === 0 && (
                  <div className="text-sm text-gray-500 dark:text-gray-400 text-center py-2">
                    No collections available
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-1">
                {(selectedIds || []).map((id) => {
                  const option = options.find((opt) => opt.id === id);
                  return option ? (
                    <Badge
                      key={id}
                      variant="secondary"
                      className="flex items-center gap-1 text-xs"
                    >
                      {option.name}
                      <button
                        type="button"
                        onClick={() => handleRemoveOption(id)}
                        className="ml-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full p-0.5"
                      >
                        <X className="h-2.5 w-2.5" />
                      </button>
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          </div>
        )}

        {/* Backdrop to close dropdown */}
        {isOpen && (
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Collections
        </label>
        {(selectedIds || []).length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearAll}
            className="h-6 px-2 text-xs"
          >
            Clear all
          </Button>
        )}
      </div>

      <div className="flex flex-wrap gap-2">
        {selectedOptions.map((option) => (
          <Badge
            key={option.id}
            variant="secondary"
            className="flex items-center gap-1"
          >
            {option.name}
            <button
              type="button"
              onClick={() => handleRemoveOption(option.id)}
              className="ml-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full p-0.5"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>

      <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
        {(options || []).map((option) => (
          <label key={option.id} className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={(selectedIds || []).includes(option.id)}
              onChange={() => handleToggleOption(option.id)}
              className="h-4 w-4"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {option.name}
            </span>
          </label>
        ))}
        {(options || []).length === 0 && (
          <div className="col-span-2 text-sm text-gray-500 dark:text-gray-400 text-center py-4">
            No collections available
          </div>
        )}
      </div>
    </div>
  );
};
