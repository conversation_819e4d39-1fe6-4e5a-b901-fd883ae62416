import React from "react";
import { MultiSelectFilter } from "./MultiSelectFilter";
import { Building2 } from "lucide-react";

interface VendorsFilterProps {
  options: string[];
  selected: string[];
  onChange: (values: string[]) => void;
}

export const VendorsFilter: React.FC<VendorsFilterProps> = ({
  options,
  selected,
  onChange,
}) => {
  return (
    <MultiSelectFilter
      label="Vendors"
      options={options}
      selected={selected}
      onChange={onChange}
      searchable
      icon={<Building2 className="h-3 w-3" />}
      hideLabel
      ariaLabel="Vendors"
    />
  );
};
