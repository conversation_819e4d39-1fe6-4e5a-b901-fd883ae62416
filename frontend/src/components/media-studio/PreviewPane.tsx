import React from "react";
import { Asset } from "@/services/assetService";
import { Thumbnail } from "./Thumbnail";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import type { AssetAction } from "@/types/mediaStudio";

// Removed Card wrapper to match flat reference design
import { RefreshCw, Search, X, ArrowUpDown } from "lucide-react";

interface PreviewPaneProps {
  assets: Asset[];
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoadingMore?: boolean;
  onAssetAction?: (action: AssetAction, asset: Asset) => void;
  onThumbnailClick?: (asset: Asset, allAssets: Asset[]) => void;
}

export const PreviewPane: React.FC<PreviewPaneProps> = ({
  assets = [],
  loading = false,
  error = null,
  onRefresh,
  onLoadMore,
  hasMore = false,
  isLoadingMore = false,
  onAssetAction,
  onThumbnailClick,
}) => {
  const [filterOptions, setFilterOptions] = React.useState<string[]>([]);

  const [sortBy, setSortBy] = React.useState<
    "newest" | "oldest" | "name" | "type"
  >("newest");
  const [promptQuery, setPromptQuery] = React.useState<string>("");
  const [debouncedQuery, setDebouncedQuery] = React.useState<string>("");

  // Debounce query to reduce recompute churn
  React.useEffect(() => {
    const t = window.setTimeout(
      () => setDebouncedQuery(promptQuery.trim().toLowerCase()),
      200
    );
    return () => window.clearTimeout(t);
  }, [promptQuery]);

  // Intersection observer for infinite scroll
  const sentinelRef = React.useRef<HTMLDivElement | null>(null);
  React.useEffect(() => {
    if (!sentinelRef.current || !hasMore || isLoadingMore || !onLoadMore)
      return;

    const el = sentinelRef.current;

    // Find the nearest scrollable parent to use as the IO root
    const getScrollParent = (node: HTMLElement | null): HTMLElement | null => {
      let p = node?.parentElement || null;
      while (p) {
        const style = window.getComputedStyle(p);
        const oy = style.overflowY;
        const o = style.overflow;
        if (
          oy === "auto" ||
          oy === "scroll" ||
          o === "auto" ||
          o === "scroll"
        ) {
          return p;
        }
        p = p.parentElement;
      }
      return null;
    };

    const rootEl = getScrollParent(el);

    const io = new IntersectionObserver(
      (entries) => {
        const e = entries[0];
        console.log("Gallery sentinel intersection:", {
          isIntersecting: e?.isIntersecting,
          hasMore,
          isLoadingMore,
          rootEl: !!rootEl,
        });
        if (e && e.isIntersecting && hasMore && !isLoadingMore) {
          // Trigger load-more when sentinel enters view
          onLoadMore();
        }
      },
      { root: rootEl, rootMargin: "100px 0px 0px 0px", threshold: 0.1 }
    );
    io.observe(el);
    return () => io.disconnect();
  }, [hasMore, isLoadingMore, onLoadMore]);

  const filteredAssets = React.useMemo(() => {
    const q = debouncedQuery;
    let filtered = assets.filter((a) => {
      const selectedTypes = filterOptions.filter(
        (o) => o === "image" || o === "video"
      );
      const selectedSources = filterOptions.filter(
        (o) => o === "generated" || o === "product"
      );
      const typeOk =
        selectedTypes.length === 0 || selectedTypes.includes(a.type);
      const sourceOk =
        selectedSources.length === 0 ||
        (selectedSources.includes("generated") &&
          a.sourceType === "ai_generated") ||
        (selectedSources.includes("product") && a.sourceType === "product");
      const text = `${a.prompt || ""} ${a.filename || ""}`.toLowerCase();
      const queryOk = q === "" || text.includes(q);
      return typeOk && sourceOk && queryOk;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "newest": {
          const ta = a.generatedAt
            ? Date.parse(a.generatedAt)
            : Date.parse(a.id);
          const tb = b.generatedAt
            ? Date.parse(b.generatedAt)
            : Date.parse(b.id);
          return (isNaN(tb) ? 0 : tb) - (isNaN(ta) ? 0 : ta);
        }
        case "oldest": {
          const ta = a.generatedAt
            ? Date.parse(a.generatedAt)
            : Date.parse(a.id);
          const tb = b.generatedAt
            ? Date.parse(b.generatedAt)
            : Date.parse(b.id);
          return (isNaN(ta) ? 0 : ta) - (isNaN(tb) ? 0 : tb);
        }
        case "name":
          return (a.displayName || a.filename).localeCompare(
            b.displayName || b.filename
          );
        case "type":
          return a.type.localeCompare(b.type);
        default:
          return 0;
      }
    });

    return filtered;
  }, [assets, filterOptions, sortBy, debouncedQuery]);

  const onSelect = React.useCallback(() => {}, []);

  const clearFilters = React.useCallback(() => {
    setFilterOptions([]);
    setSortBy("newest");
    setPromptQuery("");
  }, []);

  const hasActiveFilters =
    filterOptions.length > 0 ||
    sortBy !== "newest" ||
    promptQuery.trim() !== "";

  return (
    <div className="w-full h-full flex flex-col">
      {loading ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-muted-foreground">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-3" />
            <div className="text-sm font-medium mb-1">
              Loading Gallery Assets
            </div>
            <div className="text-xs">This may take a moment...</div>
          </div>
        </div>
      ) : error ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-amber-700">
          <div className="text-center max-w-md">
            <div className="text-sm font-medium mb-2">
              Failed to Load Gallery
            </div>
            <div className="text-xs mb-4">{error}</div>
            {onRefresh && (
              <button
                onClick={onRefresh}
                className="text-xs bg-amber-100 hover:bg-amber-200 px-3 py-1 rounded"
              >
                Try Again
              </button>
            )}
          </div>
        </div>
      ) : assets.length === 0 ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-muted-foreground">
          <div className="text-center max-w-md">
            <div className="text-6xl mb-4">🖼️</div>
            <div className="text-sm font-medium mb-2">No Gallery Assets</div>
            <div className="text-xs mb-4">
              Gallery assets will appear here once you generate or upload media.
            </div>
            {onRefresh && (
              <button
                onClick={onRefresh}
                className="text-xs bg-muted hover:bg-muted/80 px-3 py-1 rounded"
              >
                Refresh Gallery
              </button>
            )}
          </div>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto overflow-x-hidden min-h-0 relative">
          {/* Filters toolbar - Single row */}
          <div className="sticky top-0 z-10 bg-background px-3 pt-3 pb-2">
            <div className="flex items-center gap-2">
              {/* Search (left) */}
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search by prompt or filename..."
                  value={promptQuery}
                  onChange={(e) => setPromptQuery(e.target.value)}
                  className="pl-10 h-7 text-sm"
                />
                {promptQuery && (
                  <button
                    onClick={() => setPromptQuery("")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </div>

              {/* Merged Type/Source Multi-Select */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7 rounded-full"
                  >
                    Filters
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.includes("image")}
                    onCheckedChange={(checked) =>
                      setFilterOptions((prev) =>
                        checked
                          ? [...prev.filter((x) => x !== "image"), "image"]
                          : prev.filter((x) => x !== "image")
                      )
                    }
                  >
                    Images
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.includes("video")}
                    onCheckedChange={(checked) =>
                      setFilterOptions((prev) =>
                        checked
                          ? [...prev.filter((x) => x !== "video"), "video"]
                          : prev.filter((x) => x !== "video")
                      )
                    }
                  >
                    Videos
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.includes("generated")}
                    onCheckedChange={(checked) =>
                      setFilterOptions((prev) =>
                        checked
                          ? [
                              ...prev.filter((x) => x !== "generated"),
                              "generated",
                            ]
                          : prev.filter((x) => x !== "generated")
                      )
                    }
                  >
                    Generated
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filterOptions.includes("product")}
                    onCheckedChange={(checked) =>
                      setFilterOptions((prev) =>
                        checked
                          ? [...prev.filter((x) => x !== "product"), "product"]
                          : prev.filter((x) => x !== "product")
                      )
                    }
                  >
                    Product
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Sort (icon) */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-full"
                    title="Sort"
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuItem onSelect={() => setSortBy("newest")}>
                    Newest
                  </DropdownMenuItem>
                  <DropdownMenuItem onSelect={() => setSortBy("oldest")}>
                    Oldest
                  </DropdownMenuItem>
                  <DropdownMenuItem onSelect={() => setSortBy("name")}>
                    Name
                  </DropdownMenuItem>
                  <DropdownMenuItem onSelect={() => setSortBy("type")}>
                    Type
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Clear Filters */}
              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="h-7 px-2 text-xs rounded-full hover:bg-muted/50 focus:ring-0 focus:ring-offset-0"
                  title="Clear all filters"
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear
                </Button>
              )}

              {/* Item count (right-aligned) */}
              <span className="ml-auto inline-flex items-center rounded-full bg-muted text-muted-foreground px-2.5 py-1 text-xs font-medium">
                {filteredAssets.length} items
              </span>
            </div>
          </div>

          {/* Grid */}
          <div className="px-3 pt-1 pb-10">
            <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
              {filteredAssets.map((asset) => (
                <div key={asset.id} className="w-full aspect-square">
                  <Thumbnail
                    asset={asset}
                    isSelected={false}
                    onSelect={onSelect}
                    isGenerated={asset.sourceType === "ai_generated"}
                    showBorder={false}
                    draggable={true}
                    onAction={onAssetAction}
                    onThumbnailClick={onThumbnailClick}
                    allAssets={filteredAssets}
                  />
                </div>
              ))}
            </div>

            {/* Bottom sentinel for infinite scroll */}
            {hasMore && <div ref={sentinelRef} className="w-full h-10" />}
          </div>

          {/* Loading more indicator */}
          {isLoadingMore && (
            <div className="absolute bottom-2 left-1/2 -translate-x-1/2 text-xs text-muted-foreground">
              <RefreshCw className="h-4 w-4 animate-spin inline mr-1" />
              Loading more…
            </div>
          )}
        </div>
      )}
    </div>
  );
};
