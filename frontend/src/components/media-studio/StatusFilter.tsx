import React from "react";
import { MultiSelectFilter } from "./MultiSelectFilter";
import { CircleDot } from "lucide-react";

interface StatusFilterProps {
  options: string[]; // ignored; always normalized to Active/Draft/Archived
  selected: string[];
  onChange: (values: string[]) => void;
}

const ALLOWED = ["active", "draft", "archived"] as const;
const toTitle = (s: string) =>
  s.charAt(0).toUpperCase() + s.slice(1).toLowerCase();

export const StatusFilter: React.FC<StatusFilterProps> = ({
  selected,
  onChange,
}) => {
  // Normalize incoming selection (case-insensitive) and restrict to allowed values
  const normalizedSelectedLower = Array.from(
    new Set((selected || []).map((s) => s.toLowerCase()))
  ).filter((s) => ALLOWED.includes(s as any));

  const displayOptions = ALLOWED.map(toTitle); // ["Active","Draft","Archived"]
  const displaySelected = normalizedSelectedLower.map(toTitle);

  const handleChange = (values: string[]) => {
    const lowered = Array.from(
      new Set(values.map((v) => v.toLowerCase()))
    ).filter((v) => ALLOWED.includes(v as any));
    onChange(lowered);
  };

  return (
    <MultiSelectFilter
      label="Status"
      options={displayOptions}
      selected={displaySelected}
      onChange={handleChange}
      searchable={false}
      icon={<CircleDot className="h-3 w-3" />}
      hideLabel
      ariaLabel="Status"
    />
  );
};
