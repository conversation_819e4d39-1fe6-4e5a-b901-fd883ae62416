import React from "react";
import { MultiSelectFilter } from "./MultiSelectFilter";
import { Layers } from "lucide-react";

interface TypesFilterProps {
  options: string[];
  selected: string[];
  onChange: (values: string[]) => void;
}

export const TypesFilter: React.FC<TypesFilterProps> = ({
  options,
  selected,
  onChange,
}) => {
  return (
    <MultiSelectFilter
      label="Type"
      options={options}
      selected={selected}
      onChange={onChange}
      searchable
      icon={<Layers className="h-3 w-3" />}
      hideLabel
      ariaLabel="Type"
    />
  );
};
