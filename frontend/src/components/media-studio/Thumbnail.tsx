import React, { useState } from "react";
import Image from "next/image";
import { Asset as AssetServiceAsset } from "@/services/assetService";
import { Asset as MediaServiceAsset } from "@/services/mediaService";
import { cn } from "@/lib/utils";
import { Play, AlertCircle, Pencil } from "lucide-react";
import { Card } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import type { AssetAction } from "@/types/mediaStudio";

// Union type to handle both Asset types
type Asset = AssetServiceAsset | MediaServiceAsset;

interface ThumbnailProps {
  asset: Asset;
  isSelected: boolean;
  onSelect: (asset: Asset, isMultiSelect: boolean) => void;
  isGenerated?: boolean;
  isFailedPlaceholder?: boolean;
  draggable?: boolean;
  showBorder?: boolean;
  forceSquare?: boolean; // when false, fills parent container
  onAction?: (action: AssetAction, asset: Asset) => void;
  onThumbnailClick?: (asset: Asset, allAssets: Asset[]) => void;
  allAssets?: Asset[];
}

const BaseThumbnail: React.FC<ThumbnailProps> = ({
  asset,
  isSelected,
  onSelect,
  isGenerated = false,
  isFailedPlaceholder = false,
  draggable = false,
  showBorder = true,
  forceSquare = true,
  onAction,
  onThumbnailClick,
  allAssets = [],
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    // If we have a thumbnail click handler and assets, open lightbox
    if (onThumbnailClick && allAssets.length > 0) {
      onThumbnailClick(asset, allAssets);
    } else {
      // Otherwise, handle selection
      onSelect(asset, e.ctrlKey || e.metaKey);
    }
  };

  const handleDragStart = (e: React.DragEvent) => {
    if (!draggable || asset.type === "video") {
      e.preventDefault();
      return;
    }

    setIsDragging(true);

    // Set drag data with comprehensive asset information
    const dragData = {
      type: "image",
      assetId: asset.id,
      filename: asset.filename,
      displayName: asset.displayName || asset.filename,
      url: asset.url,
      productId: asset.productId,
      sourceType: asset.sourceType,
    };

    e.dataTransfer.setData("application/json", JSON.stringify(dragData));
    e.dataTransfer.setData("text/plain", asset.filename); // Fallback
    e.dataTransfer.effectAllowed = "copy";

    // Create a custom drag image
    const dragImage = document.createElement("div");
    dragImage.innerHTML = `
      <div style="
        background: rgba(59, 130, 246, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        max-width: 200px;
      ">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="9" cy="9" r="2"/>
          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
        </svg>
        <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
          ${asset.displayName || asset.filename}
        </span>
      </div>
    `;

    dragImage.style.position = "absolute";
    dragImage.style.top = "-1000px";
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 10, 10);

    // Clean up the drag image after a short delay
    setTimeout(() => {
      if (document.body.contains(dragImage)) {
        document.body.removeChild(dragImage);
      }
    }, 0);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const isPlaceholder = asset.id.startsWith("temp_");
  const isVideo = asset.type === "video";
  const isText = asset.type === "text";
  const canDelete = isGenerated || asset.sourceType === "ai_generated";

  return (
    <Card
      className={cn(
        "relative overflow-hidden cursor-pointer transition-all duration-200 group rounded-none hover:shadow-none focus:ring-0 focus:ring-offset-0",
        forceSquare ? "aspect-square" : "w-full h-full",
        showBorder
          ? isSelected
            ? "border-2 border-primary ring-2 ring-primary/20"
            : "border-2 border-border hover:border-border/80"
          : "border-none shadow-none hover:bg-transparent",
        isDragging && "opacity-50 scale-95",
        isFailedPlaceholder && showBorder && "border-destructive",
        draggable && "hover:shadow-lg"
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      draggable={draggable}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      title={`${asset.displayName || asset.filename}${isGenerated ? " (Generated)" : ""}`}
    >
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            type="button"
            onClick={(event) => {
              event.stopPropagation();
            }}
            className="absolute top-2 left-2 z-30 flex h-8 w-8 items-center justify-center rounded-full bg-black/45 text-white opacity-0 transition-opacity hover:bg-black/60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40 group-hover:opacity-100"
            aria-label="Open asset actions"
          >
            <Pencil className="h-4 w-4" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="start"
          side="right"
          sideOffset={4}
          onClick={(event) => event.stopPropagation()}
          className="w-48"
        >
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem
            disabled
            onSelect={(event) => event.preventDefault()}
          >
            Edit (coming soon)
          </DropdownMenuItem>
          <DropdownMenuItem
            disabled
            onSelect={(event) => event.preventDefault()}
          >
            Video (coming soon)
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onSelect={(event) => {
              event.preventDefault();
              event.stopPropagation();
              onAction?.("add_to_models", asset);
            }}
          >
            Add to models
          </DropdownMenuItem>
          <DropdownMenuItem
            onSelect={(event) => {
              event.preventDefault();
              event.stopPropagation();
              onAction?.("add_to_outfits", asset);
            }}
          >
            Add to outfits
          </DropdownMenuItem>
          <DropdownMenuItem
            onSelect={(event) => {
              event.preventDefault();
              event.stopPropagation();
              onAction?.("add_to_scenes", asset);
            }}
          >
            Add to scenes
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            disabled={!canDelete}
            className={cn(
              !canDelete ? "" : "text-destructive focus:text-destructive"
            )}
            onSelect={(event) => {
              event.preventDefault();
              event.stopPropagation();
              if (canDelete) {
                onAction?.("delete", asset);
              }
            }}
          >
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Image/Video/Text Content */}
      {isPlaceholder ? (
        <div className="w-full h-full bg-muted flex items-center justify-center">
          {isFailedPlaceholder ? (
            <AlertCircle className="h-8 w-8 text-destructive" />
          ) : (
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent" />
          )}
        </div>
      ) : isText ? (
        <div className="w-full h-full bg-muted/20 flex items-center justify-center p-2">
          <div className="text-xs text-center text-muted-foreground">
            {(asset as MediaServiceAsset).textContent || "Text Asset"}
          </div>
        </div>
      ) : (
        <>
          <Image
            src={asset.url}
            alt={asset.displayName || asset.filename}
            fill
            sizes="(max-width: 768px) 128px, 192px"
            style={{ objectFit: "contain" }}
            loading="lazy"
          />

          {/* Video overlay */}
          {isVideo && (
            <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
              <Play className="h-6 w-6 text-white drop-shadow-lg" />
            </div>
          )}

          {/* Gallery hover overlay - subtle transparent overlay */}
          {isHovered && !showBorder && !isVideo && !isText && (
            <div className="absolute inset-0 bg-black/10" />
          )}
        </>
      )}

      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
          <svg
            className="w-3 h-3 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      )}

      {/* No extra hover/drag icons to keep thumbnails minimal */}
    </Card>
  );
};

export const Thumbnail = React.memo(BaseThumbnail);
