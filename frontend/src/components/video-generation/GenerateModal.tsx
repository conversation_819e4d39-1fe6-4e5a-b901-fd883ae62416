"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Sparkles,
  Play,
  Square,
  Monitor,
  Smartphone,
  Loader2,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { useQuery, useMutation } from "@tanstack/react-query";
import VideoService from "@/services/videoService";
import { toast } from "sonner";

interface GenerateModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedProducts: string[];
}

interface JobStatus {
  id: string;
  status: "pending" | "processing" | "completed" | "failed";
  progress?: number;
  variants?: Array<{
    id: string;
    productId: string;
    variantName: string;
    status: "generating" | "ready" | "failed";
    videoUrl?: string;
    thumbnailUrl?: string;
  }>;
  error?: string;
}

export function GenerateModal({
  open,
  onOpenChange,
  selectedProducts,
}: GenerateModalProps) {
  const [step, setStep] = useState<"configure" | "generating" | "completed">(
    "configure"
  );
  const [templateId, setTemplateId] = useState("");
  const [voiceId, setVoiceId] = useState("");
  const [aspectRatio, setAspectRatio] = useState("16:9");
  const [ctaText, setCtaText] = useState("Shop Now");
  const [customScript, setCustomScript] = useState("");
  const [currentJob, setCurrentJob] = useState<JobStatus | null>(null);

  // Fetch templates and voices
  const { data: templatesData } = useQuery({
    queryKey: ["video-templates"],
    queryFn: () => VideoService.getTemplates(),
    enabled: open,
  });

  const { data: voicesData } = useQuery({
    queryKey: ["video-voices"],
    queryFn: () => VideoService.getVoices(),
    enabled: open,
  });

  const templates = templatesData?.templates || [];
  const voices = voicesData?.voices || [];

  // Generate videos mutation
  const generateMutation = useMutation({
    mutationFn: (params: {
      productIds: string[];
      templateId: string;
      voiceId: string;
      aspectRatio: string;
      ctaText: string;
      customScript?: string;
    }) => VideoService.generateVideos(params),
    onSuccess: (data) => {
      setCurrentJob(data.job);
      setStep("generating");
      // Start polling for job status
      pollJobStatus(data.job.id);
    },
    onError: (error) => {
      toast.error("Failed to start video generation");
      console.error("Generation error:", error);
    },
  });

  // Poll job status
  const pollJobStatus = async (jobId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const status = await VideoService.getJobStatus(jobId);
        setCurrentJob(status);

        if (status.status === "completed") {
          clearInterval(pollInterval);
          setStep("completed");
          toast.success("Video generation completed!");
        } else if (status.status === "failed") {
          clearInterval(pollInterval);
          toast.error("Video generation failed");
        }
      } catch (error) {
        console.error("Failed to poll job status:", error);
        clearInterval(pollInterval);
      }
    }, 2000);

    // Clean up after 10 minutes
    setTimeout(() => clearInterval(pollInterval), 10 * 60 * 1000);
  };

  const handleGenerate = () => {
    if (!templateId || !voiceId) {
      toast.error("Please select a template and voice");
      return;
    }

    generateMutation.mutate({
      productIds: selectedProducts,
      templateId,
      voiceId,
      aspectRatio,
      ctaText,
      customScript: customScript.trim() || undefined,
    });
  };

  const handleClose = () => {
    if (step === "generating") {
      // Warn user about closing during generation
      if (
        confirm(
          "Video generation is in progress. Are you sure you want to close?"
        )
      ) {
        onOpenChange(false);
        resetModal();
      }
    } else {
      onOpenChange(false);
      resetModal();
    }
  };

  const resetModal = () => {
    setStep("configure");
    setCurrentJob(null);
    setTemplateId("");
    setVoiceId("");
    setAspectRatio("16:9");
    setCtaText("Shop Now");
    setCustomScript("");
  };

  const aspectRatioOptions = [
    {
      value: "16:9",
      label: "Landscape (16:9)",
      icon: Monitor,
      description: "Perfect for YouTube, websites",
    },
    {
      value: "9:16",
      label: "Portrait (9:16)",
      icon: Smartphone,
      description: "Ideal for TikTok, Instagram Stories",
    },
    {
      value: "1:1",
      label: "Square (1:1)",
      icon: Square,
      description: "Great for Instagram posts, Facebook",
    },
  ];

  const getProgressValue = () => {
    if (!currentJob) return 0;
    if (currentJob.status === "completed") return 100;
    return 0;
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Sparkles className="mr-2 h-5 w-5 text-purple-600" />
            Generate AI Videos
          </DialogTitle>
          <DialogDescription>
            {step === "configure" &&
              `Create videos for ${selectedProducts.length} selected product${selectedProducts.length !== 1 ? "s" : ""}`}
            {step === "generating" && "Generating your videos..."}
            {step === "completed" && "Video generation completed!"}
          </DialogDescription>
        </DialogHeader>

        {step === "configure" && (
          <div className="space-y-6">
            {/* Template Selection */}
            <div className="space-y-3">
              <Label htmlFor="template">Video Template</Label>
              <Select value={templateId} onValueChange={setTemplateId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a template" />
                </SelectTrigger>
                <SelectContent>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{template.name}</span>
                        <Badge variant="outline" className="ml-2">
                          {template.category}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {templateId && (
                <p className="text-sm text-muted-foreground">
                  {templates.find((t) => t.id === templateId)?.description}
                </p>
              )}
            </div>

            {/* Voice Selection */}
            <div className="space-y-3">
              <Label htmlFor="voice">Voice</Label>
              <Select value={voiceId} onValueChange={setVoiceId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a voice" />
                </SelectTrigger>
                <SelectContent>
                  {voices.map((voice) => (
                    <SelectItem key={voice.id} value={voice.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{voice.name}</span>
                        <div className="flex items-center space-x-2 ml-2">
                          <Badge variant="outline">{voice.gender}</Badge>
                          <Badge variant="outline">{voice.accent}</Badge>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Aspect Ratio */}
            <div className="space-y-3">
              <Label>Aspect Ratio</Label>
              <RadioGroup value={aspectRatio} onValueChange={setAspectRatio}>
                <div className="grid grid-cols-1 gap-3">
                  {aspectRatioOptions.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center space-x-3"
                    >
                      <RadioGroupItem value={option.value} id={option.value} />
                      <Label
                        htmlFor={option.value}
                        className="flex items-center space-x-3 cursor-pointer flex-1"
                      >
                        <option.icon className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-sm text-muted-foreground">
                            {option.description}
                          </div>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>

            {/* CTA Text */}
            <div className="space-y-3">
              <Label htmlFor="cta">Call-to-Action Text</Label>
              <Select value={ctaText} onValueChange={setCtaText}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Shop Now">Shop Now</SelectItem>
                  <SelectItem value="Buy Now">Buy Now</SelectItem>
                  <SelectItem value="Learn More">Learn More</SelectItem>
                  <SelectItem value="Get Yours">Get Yours</SelectItem>
                  <SelectItem value="Order Today">Order Today</SelectItem>
                  <SelectItem value="Discover More">Discover More</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Custom Script */}
            <div className="space-y-3">
              <Label htmlFor="script">Custom Script (Optional)</Label>
              <Textarea
                id="script"
                placeholder="Enter a custom script for your videos. Leave empty to use AI-generated content based on product information."
                value={customScript}
                onChange={(e) => setCustomScript(e.target.value)}
                rows={4}
              />
              <p className="text-sm text-muted-foreground">
                If provided, this script will be used for all selected products.
                Otherwise, AI will generate unique scripts based on each
                product's details.
              </p>
            </div>

            <Separator />

            {/* Cost Estimation */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Cost Estimation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Products selected:</span>
                  <span>{selectedProducts.length}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Variants per product:</span>
                  <span>4</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Total videos:</span>
                  <span>{selectedProducts.length * 4}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Estimated cost:</span>
                  <span>${(selectedProducts.length * 4 * 0.5).toFixed(2)}</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  $0.50 per video variant. Final cost may vary based on video
                  length and complexity.
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {step === "generating" && currentJob && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Videos
                </CardTitle>
                <CardDescription>Job ID: {currentJob.id}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{getProgressValue()}%</span>
                  </div>
                  <Progress value={getProgressValue()} />
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">
                    Status: {currentJob.status}
                  </p>
                  {currentJob.variants && (
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Variants:</p>
                      {currentJob.variants.map((variant) => (
                        <div
                          key={variant.id}
                          className="flex items-center justify-between text-sm"
                        >
                          <span>
                            {variant.variantName} (Product {variant.productId})
                          </span>
                          <div className="flex items-center space-x-2">
                            {variant.status === "ready" && (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            )}
                            {variant.status === "generating" && (
                              <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                            )}
                            {variant.status === "failed" && (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            )}
                            <Badge
                              variant={
                                variant.status === "ready"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {variant.status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {step === "completed" && currentJob && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-green-600">
                  <CheckCircle className="mr-2 h-5 w-5" />
                  Generation Complete!
                </CardTitle>
                <CardDescription>
                  All videos have been generated successfully
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {currentJob.variants?.map((variant) => (
                    <div key={variant.id} className="border rounded-lg p-3">
                      <div className="aspect-video bg-gray-100 rounded mb-2 relative overflow-hidden">
                        {variant.thumbnailUrl ? (
                          <img
                            src={variant.thumbnailUrl}
                            alt={variant.variantName}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full">
                            <Play className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <p className="text-sm font-medium">
                        {variant.variantName}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Product {variant.productId}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <DialogFooter>
          {step === "configure" && (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                onClick={handleGenerate}
                disabled={!templateId || !voiceId || generateMutation.isPending}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              >
                {generateMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Starting...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Generate Videos
                  </>
                )}
              </Button>
            </>
          )}

          {step === "generating" && (
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
          )}

          {step === "completed" && <Button onClick={handleClose}>Done</Button>}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
