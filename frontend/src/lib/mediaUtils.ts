import { MediaItem } from "@/components/ui/media-viewer";
import { Product } from "@/services/productService";

/**
 * Recursively parses all JSON strings in an object.
 * This is useful for parsing product data that may contain JSON strings in various fields.
 */
export function parseJsonStringsRecursively(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === "string") {
    try {
      const parsed = JSON.parse(obj);
      if (typeof parsed === "object" && parsed !== null) {
        return parseJsonStringsRecursively(parsed);
      }
      return parsed;
    } catch {
      return obj;
    }
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => parseJsonStringsRecursively(item));
  }

  if (typeof obj === "object" && obj !== null) {
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = parseJsonStringsRecursively(value);
    }
    return result;
  }

  return obj;
}

/**
 * Helper function to parse featured media from product data.
 */
export function parseFeaturedMedia(product: Product): any | null {
  if (!product.featured_media) {
    return null;
  }

  try {
    const mediaData = JSON.parse(product.featured_media);
    return {
      id: mediaData.id,
      mediaContentType:
        mediaData.media_content_type || mediaData.mediaContentType,
      preview: mediaData.preview,
      alt: mediaData.alt,
      status: mediaData.status,
      src: mediaData.src || mediaData.url,
    };
  } catch (error) {
    console.warn(
      `Failed to parse featured_media for product ${product.id}:`,
      error
    );
    return null;
  }
}

/**
 * Function to detect media URLs in JSON data.
 */
export function detectMediaInJson(
  obj: any,
  path: string = "",
  parentKey: string = ""
): MediaItem[] {
  const mediaItems: MediaItem[] = [];

  if (!obj) {
    return mediaItems;
  }

  if (typeof obj === "string") {
    const url = obj.trim();

    // Check for image URLs
    if (
      url.match(/\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)(\?.*)?$/i) ||
      (url.includes("cdn.shopify.com") && path.toLowerCase().includes("image"))
    ) {
      mediaItems.push({
        type: "detected_image",
        id: `detected_${path}_${url.slice(-10)}`,
        src: url,
        alt: `Detected image from ${path}`,
        path,
        key: parentKey,
        data: { url, path, key: parentKey },
      });
    }

    // Check for video URLs
    if (
      url.match(/\.(mp4|webm|ogg|avi|mov|wmv|flv|m4v|mkv)(\?.*)?$/i) ||
      (url.includes("cdn.shopify.com") && path.toLowerCase().includes("video"))
    ) {
      mediaItems.push({
        type: "detected_video",
        id: `detected_${path}_${url.slice(-10)}`,
        src: url,
        alt: `Detected video from ${path}`,
        path,
        key: parentKey,
        data: { url, path, key: parentKey },
      });
    }

    return mediaItems;
  }

  if (Array.isArray(obj)) {
    obj.forEach((item, index) => {
      mediaItems.push(
        ...detectMediaInJson(item, `${path}[${index}]`, parentKey)
      );
    });
    return mediaItems;
  }

  if (typeof obj === "object" && obj !== null) {
    Object.entries(obj).forEach(([key, value]) => {
      const newPath = path ? `${path}.${key}` : key;
      mediaItems.push(...detectMediaInJson(value, newPath, key));
    });
    return mediaItems;
  }

  return mediaItems;
}

/**
 * Get all available media for a product.
 */
export function getAllMediaForProduct(product: Product): MediaItem[] {
  const mediaItems: MediaItem[] = [];

  // Add product images
  if (product.images && Array.isArray(product.images)) {
    product.images.forEach((image, index) => {
      mediaItems.push({
        type: "image",
        id: `image_${image.id || index}`,
        src: image.src_url || image.src || "",
        alt: image.alt || `${product.title} - Image ${index + 1}`,
        data: image,
      });
    });
  }

  // Add featured media
  const featuredMedia = parseFeaturedMedia(product);
  if (featuredMedia && featuredMedia.src) {
    const mediaType =
      featuredMedia.mediaContentType === "VIDEO"
        ? "featured_video"
        : "featured_image";
    mediaItems.push({
      type: mediaType,
      id: `featured_${featuredMedia.id || "media"}`,
      src: featuredMedia.src,
      alt: featuredMedia.alt || `${product.title} - Featured Media`,
      data: featuredMedia,
    });
  }

  // Parse the entire product object to detect any embedded media URLs
  const parsedProduct = parseJsonStringsRecursively(product);
  const detectedMedia = detectMediaInJson(parsedProduct, "product");
  mediaItems.push(...detectedMedia);

  // Remove duplicates based on src URL
  const uniqueMedia = mediaItems.filter(
    (item, index, self) => index === self.findIndex((m) => m.src === item.src)
  );

  return uniqueMedia;
}
