'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'

interface ImageItem {
  file: File
  url: string
  label: string
}

interface CropArea {
  x: number
  y: number
  width: number
  height: number
}

export default function TestImageGrid() {
  const [images, setImages] = useState<ImageItem[]>([])
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [cropModalOpen, setCropModalOpen] = useState(false)
  const [cropImageIndex, setCropImageIndex] = useState<number | null>(null)
  const cropCanvasRef = useRef<HTMLCanvasElement>(null)
  const [cropArea, setCropArea] = useState<CropArea>({ x: 0, y: 0, width: 0, height: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    const totalImages = images.length + files.length
    if (totalImages > 9) {
      alert(`Cannot add ${files.length} images. Maximum 9 images allowed. Currently have ${images.length}.`)
      return
    }
    const newImages = files.map(file => ({
      file,
      url: URL.createObjectURL(file),
      label: ''
    }))
    setImages(prev => [...prev, ...newImages])
  }

  const updateLabel = (index: number, label: string) => {
    setImages(prev => prev.map((img, i) => i === index ? { ...img, label } : img))
  }

  const openCropModal = (index: number) => {
    setCropImageIndex(index)
    setCropModalOpen(true)
    setCropArea({ x: 0, y: 0, width: 0, height: 0 })
  }

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index))
  }

  const applyCrop = () => {
    if (cropImageIndex === null) return

    const img = images[cropImageIndex]
    if (!img) return

    // Create cropped image from original image
    const croppedCanvas = document.createElement('canvas')
    const croppedCtx = croppedCanvas.getContext('2d')
    if (!croppedCtx) return

    croppedCanvas.width = cropArea.width
    croppedCanvas.height = cropArea.height

    // Load original image and crop from it
    const originalImage = new Image()
    originalImage.onload = () => {
      // Calculate the scale factor used in the display canvas
      const maxWidth = 600
      const maxHeight = 400
      const scale = Math.min(maxWidth / originalImage.width, maxHeight / originalImage.height, 1)

      // Convert crop coordinates back to original image coordinates
      const originalX = cropArea.x / scale
      const originalY = cropArea.y / scale
      const originalWidth = cropArea.width / scale
      const originalHeight = cropArea.height / scale

      // Draw cropped portion from original image
      croppedCtx.drawImage(
        originalImage,
        originalX, originalY, originalWidth, originalHeight,
        0, 0, cropArea.width, cropArea.height
      )

      // Convert to blob and create new URL
      croppedCanvas.toBlob(blob => {
        if (blob) {
          const newUrl = URL.createObjectURL(blob)
          setImages(prev => prev.map((img, i) =>
            i === cropImageIndex
              ? { ...img, url: newUrl }
              : img
          ))
          setCropModalOpen(false)
          setCropImageIndex(null)
        }
      }, 'image/png', 1.0) // Maximum quality PNG
    }
    originalImage.src = img.url
  }

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!cropCanvasRef.current) return

    const canvas = cropCanvasRef.current
    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    setIsDragging(true)
    setDragStart({ x, y })
    setCropArea({ x, y, width: 0, height: 0 })
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging || !cropCanvasRef.current) return

    const canvas = cropCanvasRef.current
    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const width = x - dragStart.x
    const height = y - dragStart.y

    setCropArea({
      x: Math.min(dragStart.x, x),
      y: Math.min(dragStart.y, y),
      width: Math.abs(width),
      height: Math.abs(height)
    })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const drawCropRectangle = () => {
    if (!cropCanvasRef.current || cropImageIndex === null) return

    const canvas = cropCanvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Redraw the image first
    const img = images[cropImageIndex]
    if (!img) return

    const image = new Image()
    image.onload = () => {
      // Calculate canvas size to fit image
      const maxWidth = 600
      const maxHeight = 400
      const scale = Math.min(maxWidth / image.width, maxHeight / image.height, 1)

      canvas.width = Math.floor(image.width * scale)
      canvas.height = Math.floor(image.height * scale)

      // Draw the image
      ctx.drawImage(image, 0, 0, canvas.width, canvas.height)

      // Draw crop rectangle if area is selected
      if (cropArea.width > 0 && cropArea.height > 0) {
        ctx.strokeStyle = '#ef4444'
        ctx.lineWidth = 2
        ctx.setLineDash([5, 5])
        ctx.strokeRect(cropArea.x, cropArea.y, cropArea.width, cropArea.height)

        // Fill with semi-transparent overlay
        ctx.fillStyle = 'rgba(239, 68, 68, 0.1)'
        ctx.fillRect(cropArea.x, cropArea.y, cropArea.width, cropArea.height)
      }
    }
    image.src = img.url
  }

  const drawPreviewCanvas = () => {
    const canvas = canvasRef.current
    if (!canvas || images.length === 0) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set preview canvas size (smaller for display)
    const previewSize = 512
    canvas.width = previewSize
    canvas.height = previewSize

    // Fill with white background
    ctx.fillStyle = 'white'
    ctx.fillRect(0, 0, previewSize, previewSize)

    const cols = Math.ceil(Math.sqrt(images.length))
    const rows = Math.ceil(images.length / cols)
    const cellWidth = previewSize / cols
    const labelHeight = 35 // Smaller label height for preview
    const imageCellHeight = (previewSize / rows) - labelHeight
    const cellHeight = previewSize / rows

    let loadedCount = 0
    images.forEach((img, index) => {
      const image = new Image()
      image.onload = () => {
        const col = index % cols
        const row = Math.floor(index / cols)
        const x = col * cellWidth
        const y = row * cellHeight

        // Calculate scale to fit image in the image area
        const scaleX = cellWidth / image.width
        const scaleY = imageCellHeight / image.height
        const scale = Math.min(scaleX, scaleY)

        const scaledWidth = image.width * scale
        const scaledHeight = image.height * scale

        // Center the image in the image area
        const offsetX = x + (cellWidth - scaledWidth) / 2
        const offsetY = y + (imageCellHeight - scaledHeight) / 2

        ctx.drawImage(image, offsetX, offsetY, scaledWidth, scaledHeight)

        // Draw label below the image
        if (img.label.trim()) {
          ctx.fillStyle = 'black'
          ctx.font = 'bold 25px Arial'
          ctx.textAlign = 'center'
          ctx.textBaseline = 'top'

          const textY = offsetY + scaledHeight + 5
          const textX = x + cellWidth / 2

          ctx.fillText(img.label, textX, textY)
        }

        loadedCount++
      }
      image.src = img.url
    })
  }

  useEffect(() => {
    drawPreviewCanvas()
  }, [images])

  useEffect(() => {
    if (cropModalOpen && cropImageIndex !== null) {
      console.log('Crop modal opened, loading image for index:', cropImageIndex)

      // Add a small delay to ensure the canvas is mounted
      setTimeout(() => {
        if (!cropCanvasRef.current) {
          console.error('Canvas ref not available')
          return
        }

        const canvas = cropCanvasRef.current
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          console.error('Could not get canvas context')
          return
        }

        // Set initial canvas size
        canvas.width = 600
        canvas.height = 400

        // Clear canvas initially
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.fillStyle = '#f3f4f6'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        ctx.fillStyle = '#6b7280'
        ctx.font = '16px Arial'
        ctx.textAlign = 'center'
        ctx.fillText('Loading image...', canvas.width / 2, canvas.height / 2)

        const img = images[cropImageIndex]
        if (!img) {
          console.error('Image not found at index:', cropImageIndex)
          return
        }

        console.log('Loading image from URL:', img.url)
        const image = new Image()
        image.crossOrigin = 'anonymous'
        image.onload = () => {
          console.log('Image loaded successfully, dimensions:', image.width, 'x', image.height)
          // Calculate canvas size to fit image
          const maxWidth = 600
          const maxHeight = 400
          const scale = Math.min(maxWidth / image.width, maxHeight / image.height, 1)

          // Set canvas to exact image dimensions scaled
          canvas.width = Math.floor(image.width * scale)
          canvas.height = Math.floor(image.height * scale)

          // Clear canvas
          ctx.clearRect(0, 0, canvas.width, canvas.height)

          // Draw the image
          ctx.drawImage(image, 0, 0, canvas.width, canvas.height)
          console.log('Image drawn to canvas')
        }
        image.onerror = (e) => {
          console.error('Failed to load image for cropping:', e)
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          ctx.fillStyle = '#dc2626'
          ctx.font = '16px Arial'
          ctx.textAlign = 'center'
          ctx.fillText('Failed to load image', canvas.width / 2, canvas.height / 2)
        }
        image.src = img.url
      }, 100) // Small delay to ensure canvas is mounted
    }
  }, [cropModalOpen, cropImageIndex, images])

  // Draw crop rectangle when crop area changes
  useEffect(() => {
    if (cropModalOpen && cropImageIndex !== null) {
      drawCropRectangle()
    }
  }, [cropArea, cropModalOpen, cropImageIndex])

  const generateCombinedImage = () => {
    if (images.length === 0) return

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 2048x2048 resolution
    const canvasWidth = 2048
    const canvasHeight = 2048
    canvas.width = canvasWidth
    canvas.height = canvasHeight

    // Fill with white background
    ctx.fillStyle = 'white'
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)

    const cols = Math.ceil(Math.sqrt(images.length))
    const rows = Math.ceil(images.length / cols)
    const cellWidth = canvasWidth / cols
    const labelHeight = 70 // Reserve space for label (50px font + 10px padding + buffer)
    const imageCellHeight = (canvasHeight / rows) - labelHeight
    const cellHeight = canvasHeight / rows

    let loadedCount = 0
    images.forEach((img, index) => {
      const image = new Image()
      image.onload = () => {
        const col = index % cols
        const row = Math.floor(index / cols)
        const x = col * cellWidth
        const y = row * cellHeight

        // Calculate scale to fit image in the image area (excluding label space) while maintaining aspect ratio
        const scaleX = cellWidth / image.width
        const scaleY = imageCellHeight / image.height
        const scale = Math.min(scaleX, scaleY)

        const scaledWidth = image.width * scale
        const scaledHeight = image.height * scale

        // Center the image in the image area (top portion of cell)
        const offsetX = x + (cellWidth - scaledWidth) / 2
        const offsetY = y + (imageCellHeight - scaledHeight) / 2

        ctx.drawImage(image, offsetX, offsetY, scaledWidth, scaledHeight)

        // Draw label below the image
        if (img.label.trim()) {
          ctx.fillStyle = 'black'
          ctx.font = 'bold 50px Arial'
          ctx.textAlign = 'center'
          ctx.textBaseline = 'top'

          const textY = offsetY + scaledHeight + 10
          const textX = x + cellWidth / 2

          ctx.fillText(img.label, textX, textY)
        }

        loadedCount++
        if (loadedCount === images.length) {
          canvas.toBlob(blob => {
            if (blob) {
              const url = URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = 'combined-outfit.png'
              a.click()
              URL.revokeObjectURL(url)
            }
          }, 'image/png', 1.0) // Maximum quality PNG
        }
      }
      image.src = img.url
    })
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Upload Outfit Images</h1>

      <div className="mb-4">
        <Label htmlFor="file-upload">Select Images (1-9)</Label>
        <Input
          id="file-upload"
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileChange}
          className="mt-2"
        />
      </div>

      {images.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left side - Image grid */}
          <div>
            <h2 className="text-lg font-semibold mb-4">Images ({images.length}/9)</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 mb-4">
              {images.map((img, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <img src={img.url} alt={`Outfit ${index + 1}`} className="w-full h-32 object-contain bg-gray-100 mb-2" />
                    <div className="flex gap-2 mb-2">
                      <Button
                        onClick={() => openCropModal(index)}
                        variant="outline"
                        size="sm"
                        className="flex-1"
                      >
                        Crop
                      </Button>
                      <Button
                        onClick={() => removeImage(index)}
                        variant="destructive"
                        size="sm"
                        className="flex-1"
                      >
                        Remove
                      </Button>
                    </div>
                    <Label htmlFor={`label-${index}`}>Label</Label>
                    <Input
                      id={`label-${index}`}
                      value={img.label}
                      onChange={(e) => updateLabel(index, e.target.value)}
                      placeholder="Enter label"
                      className="mt-1"
                    />
                  </CardContent>
                </Card>
              ))}
            </div>

            <Button onClick={generateCombinedImage} className="w-full">
              Download Combined Image (2048x2048)
            </Button>
          </div>

          {/* Right side - Preview canvas */}
          <div>
            <h2 className="text-lg font-semibold mb-4">Preview</h2>
            <div className="border rounded-lg p-4 bg-gray-50">
              <canvas
                ref={canvasRef}
                className="w-full max-w-md mx-auto border"
                style={{ aspectRatio: '1' }}
              />
              <p className="text-sm text-gray-600 mt-2 text-center">
                Real-time preview of your combined image
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Crop Modal */}
      <Dialog open={cropModalOpen} onOpenChange={setCropModalOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Crop Image</DialogTitle>
          </DialogHeader>
          <div className="flex justify-center">
            <canvas
              ref={cropCanvasRef}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              className="border cursor-crosshair max-w-full max-h-96"
              style={{ imageRendering: 'pixelated' }}
            />
          </div>
          <div className="text-sm text-gray-600 text-center">
            Click and drag to select crop area
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCropModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={applyCrop} disabled={cropArea.width === 0 || cropArea.height === 0}>
              Apply Crop
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}