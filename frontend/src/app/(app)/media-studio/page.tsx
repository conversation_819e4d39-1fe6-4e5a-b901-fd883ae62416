"use client";

import React, {
  useState,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useDeferredValue,
} from "react";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { isAxiosError } from "axios";

import { useLayout } from "@/contexts/LayoutContext";
import { cn } from "@/lib/utils";
import { productService, Product } from "@/services/productService";
import {
  mediaService,
  Asset,
  MediaGenerateRequest,
} from "@/services/mediaService";
import { assetService, Asset as GalleryAsset } from "@/services/assetService";
import { api } from "@/services/api";
import {
  MainTab,
  GenerationMode,
  ImageSettings,
  VideoSettings,
  BatchState,
  PromptInputValue,
  AssetAction,
} from "@/types/mediaStudio";
import { MainTabs } from "@/components/media-studio/MainTabs";
import { ProductGrid } from "@/components/media-studio/ProductGrid";
import { PreviewPane } from "@/components/media-studio/PreviewPane";
import { Thumbnail } from "@/components/media-studio/Thumbnail";
import { GenerateSidebar } from "@/components/media-studio/GenerateSidebar";
import { PromptEditor } from "@/components/media-studio/PromptEditor";
import { CollectionsFilter } from "@/components/media-studio/CollectionsFilter";
import { MultiSelectFilter } from "@/components/media-studio/MultiSelectFilter";
import { VendorsFilter } from "@/components/media-studio/VendorsFilter";
import { StatusFilter } from "@/components/media-studio/StatusFilter";
import { TypesFilter } from "@/components/media-studio/TypesFilter";
import { MediaFilter } from "@/components/media-studio/MediaFilter";

import { SortDropdown } from "@/components/media-studio/SortDropdown";
import { SceneLibrary } from "@/components/media-studio/SceneLibrary";
import { SCENE_PRESETS } from "@/components/media-studio/scenePresets";
import { ImageLightbox } from "@/components/media-studio/ImageLightbox";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Search,
  AlertCircle,
  X,
  UploadCloud,
  Tag as TagIcon,
  SlidersHorizontal,
  PanelRight,
  PanelRightClose,
} from "lucide-react";
import { toast } from "sonner";
import { DebugLogPanel } from "@/components/DebugLogPanel";

const MODELS = [
  { id: "banana", name: "Banana", type: "image" as const },
  { id: "banana-video", name: "Banana Video", type: "video" as const },
];

type SelectionGroup = {
  id: string;
  name: string;
  productIds: string[];
  createdAt: number;
};

const SELECTION_GROUPS_KEY = "media-studio-selection-groups";

const createDefaultPromptValue = (productTitle: string): PromptInputValue => ({
  text: `A professional product shot of a ${productTitle.toLowerCase()}, high-resolution, on a clean studio background with soft lighting.`,
  attachments: [],
});

const clonePromptValue = (value?: PromptInputValue): PromptInputValue => ({
  text: value?.text ?? "",
  attachments: (value?.attachments ?? []).map((attachment) => ({
    ...attachment,
  })),
});

interface SavedAssetsPanelProps {
  assets: Asset[];
  emptyTitle: string;
  emptyDescription: string;
  action: AssetAction;
  onAction: (action: AssetAction, asset: Asset) => void;
  onThumbnailClick?: (asset: Asset, allAssets: Asset[]) => void;
}

const SavedAssetsPanel: React.FC<SavedAssetsPanelProps> = ({
  assets,
  emptyTitle,
  emptyDescription,
  action,
  onAction,
  onThumbnailClick,
}) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isDragActive, setIsDragActive] = useState(false);

  const handleFiles = useCallback((fileList: FileList | File[] | null) => {
    if (!fileList || fileList.length === 0) return;
    toast.info("Uploading new images to collections is coming soon.");
  }, []);

  const handleFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      handleFiles(event.target.files);
      event.target.value = "";
    },
    [handleFiles]
  );

  const parseAssetFromData = useCallback((data: string): Asset | null => {
    try {
      const payload = JSON.parse(data);
      const id = payload.assetId || payload.id;
      const url = payload.url || payload.previewUrl || payload.fileUrl;
      if (!id || !url) {
        return null;
      }

      return {
        id: String(id),
        productId: payload.productId ? String(payload.productId) : "",
        url,
        type:
          payload.type === "video"
            ? "video"
            : payload.type === "text"
              ? "text"
              : "image",
        filename:
          payload.filename ||
          payload.displayName ||
          payload.assetId ||
          "asset.jpg",
        displayName:
          payload.displayName ||
          payload.prompt ||
          payload.filename ||
          String(id),
        sourceType: payload.sourceType || "product",
        fileUrl: url,
        previewUrl: payload.previewUrl || url,
      };
    } catch (error) {
      return null;
    }
  }, []);

  const handleDroppedAsset = useCallback(
    (asset: Asset | null) => {
      if (!asset) {
        toast.error("We couldn't read that image. Try again.");
        return;
      }
      onAction(action, asset);
    },
    [action, onAction]
  );

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLButtonElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragActive(false);

      const { dataTransfer } = event;
      const jsonData = dataTransfer.getData("application/json");
      if (jsonData) {
        handleDroppedAsset(parseAssetFromData(jsonData));
        return;
      }

      if (dataTransfer.files && dataTransfer.files.length > 0) {
        handleFiles(dataTransfer.files);
        return;
      }

      const textData = dataTransfer.getData("text/plain");
      if (textData) {
        handleDroppedAsset(parseAssetFromData(textData));
      }
    },
    [handleDroppedAsset, parseAssetFromData, handleFiles]
  );

  const handleDragEnter = useCallback(
    (event: React.DragEvent<HTMLButtonElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragActive(true);
    },
    []
  );

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLButtonElement>) => {
      event.preventDefault();
      event.stopPropagation();
      event.dataTransfer.dropEffect = "copy";
      setIsDragActive(true);
    },
    []
  );

  const handleDragLeave = useCallback(
    (event: React.DragEvent<HTMLButtonElement>) => {
      if (
        event.currentTarget.contains(event.relatedTarget as Node) ||
        event.currentTarget === event.relatedTarget
      ) {
        return;
      }
      setIsDragActive(false);
    },
    []
  );

  const handleUploadClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  return (
    <div className="h-full flex flex-col">
      <div className="p-4">
        <div className="grid grid-cols-2 gap-3">
          <div className="flex flex-col gap-1">
            <button
              type="button"
              className={cn(
                "group relative flex aspect-square flex-col items-center justify-center gap-2 rounded-lg border-2 border-dashed border-muted/40 bg-muted/20 p-4 text-center transition",
                "hover:border-primary/70 hover:bg-primary/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40",
                isDragActive && "border-primary bg-primary/10"
              )}
              aria-label="Upload or add image"
              onClick={handleUploadClick}
              onDragEnter={handleDragEnter}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <UploadCloud className="h-7 w-7 text-muted-foreground transition group-hover:text-primary" />
              <div className="flex flex-col text-xs text-muted-foreground">
                <span className="font-medium text-foreground">
                  Upload image
                </span>
                <span>Click or drop to add</span>
              </div>
              <span className="pointer-events-none absolute inset-0 rounded-lg" />
            </button>
          </div>
          {assets.map((asset) => (
            <div key={asset.id} className="flex flex-col gap-1">
              <Thumbnail
                asset={asset}
                isSelected={false}
                onSelect={() => {}}
                isGenerated={asset.sourceType === "ai_generated"}
                showBorder={false}
                draggable={true}
                onAction={onAction}
                onThumbnailClick={onThumbnailClick}
                allAssets={assets}
              />
              <span className="truncate text-xs text-muted-foreground">
                {asset.displayName || asset.filename}
              </span>
            </div>
          ))}
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          className="hidden"
          onChange={handleFileInputChange}
        />
        {assets.length === 0 && (
          <div className="mt-6 text-center">
            <div className="text-base font-semibold text-foreground mb-1">
              {emptyTitle}
            </div>
            <div className="text-sm text-muted-foreground">
              {emptyDescription}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const MediaStudioPage: React.FC = () => {
  const { sidebarCollapsed, isMobile } = useLayout();

  // Core state
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [productsError, setProductsError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const itemsPerPage = 50;

  const [hasMoreProducts, setHasMoreProducts] = useState(true);
  const [isLoadingMoreProducts, setIsLoadingMoreProducts] = useState(false);

  const productsSentinelRef = React.useRef<HTMLDivElement | null>(null);

  const productsScrollRef = React.useRef<HTMLDivElement | null>(null);
  const productsContainerRef = React.useRef<HTMLDivElement | null>(null);
  const [gridColumns, setGridColumns] = useState<number>(2);

  // Selection state
  const [selectedAssetIds, setSelectedAssetIds] = useState<Set<string>>(
    new Set()
  );
  const [selectedProductIds, setSelectedProductIds] = useState<Set<string>>(
    new Set()
  );
  const [selectionGroups, setSelectionGroups] = useState<SelectionGroup[]>(
    () => {
      if (typeof window !== "undefined") {
        try {
          const stored = window.localStorage.getItem(SELECTION_GROUPS_KEY);
          return stored ? (JSON.parse(stored) as SelectionGroup[]) : [];
        } catch (error) {
          console.warn("Failed to parse selection groups", error);
        }
      }
      return [];
    }
  );
  const [isSavingGroup, setIsSavingGroup] = useState(false);

  const [activeAsset, setActiveAsset] = useState<Asset | null>(null);

  // UI state
  const [activeMainTab, setActiveMainTab] = useState<MainTab>("gallery");
  const [generationMode, setGenerationMode] = useState<GenerationMode>("image");
  const [selectedModelId, setSelectedModelId] = useState<string>("banana");

  // Generation state
  const [settings, setSettings] = useState<ImageSettings | VideoSettings>({
    size: "1024x1024",
    guidance: 7.5,
    steps: 25,
    strength: 0.8,
    seed: Math.floor(Math.random() * 100000),
    upscale: true,
    safety: true,
    aspectRatio: "1:1",
    quality: "Standard",
  });
  // Right slide-out panel state
  const [rightPanelOpen, setRightPanelOpen] = useState<boolean>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-right-panel-open");
      if (saved !== null) {
        try {
          return JSON.parse(saved);
        } catch (error) {
          console.warn("Failed to parse right panel state", error);
        }
      }
    }
    return false;
  });
  const [rightPanelWidth, setRightPanelWidth] = useState<number>(380);
  const resizeStateRef = React.useRef<{
    startX: number;
    startWidth: number;
  } | null>(null);

  const onRightPanelDragStart = (e: React.MouseEvent) => {
    resizeStateRef.current = { startX: e.clientX, startWidth: rightPanelWidth };
    const onMove = (ev: MouseEvent) => {
      if (!resizeStateRef.current) return;
      const dx = resizeStateRef.current.startX - ev.clientX;
      let next = resizeStateRef.current.startWidth + dx;
      next = Math.max(0, Math.min(1000, next));
      setRightPanelWidth(next);
      setRightPanelOpen(next > 8);
    };
    const onUp = () => {
      resizeStateRef.current = null;
      window.removeEventListener("mousemove", onMove);
      window.removeEventListener("mouseup", onUp);
    };
    window.addEventListener("mousemove", onMove);
    window.addEventListener("mouseup", onUp);
  };

  const handleTogglePanel = useCallback(() => {
    if (rightPanelOpen) {
      setRightPanelOpen(false);
      setRightPanelWidth(0);
      setGridColumns(4);
    } else {
      // When opening, set the right panel to half of the current product list width
      const baseWidth = productsContainerRef.current?.offsetWidth || 760;
      const targetWidth = Math.max(320, Math.round(baseWidth / 2));
      setRightPanelOpen(true);
      setRightPanelWidth(targetWidth);
      setGridColumns(2);
    }
  }, [rightPanelOpen]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "media-studio-right-panel-open",
        JSON.stringify(rightPanelOpen)
      );
    }
  }, [rightPanelOpen]);

  // Prompts and filters with persistence
  const [prompts, setPrompts] = useState<Record<string, PromptInputValue>>({});

  const [collectionFilters, setCollectionFilters] = useState<string[]>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-collection-filters");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });

  const [searchQuery, setSearchQuery] = useState<string>(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("media-studio-search-query") || "";
    }
    return "";
  });

  // Keep the UI responsive while typing
  const deferredSearchQuery = useDeferredValue(searchQuery);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [moreFiltersOpen, setMoreFiltersOpen] = useState(false);

  const filtersButtonRef = React.useRef<HTMLButtonElement | null>(null);
  const filtersPopoverRef = React.useRef<HTMLDivElement | null>(null);

  const [filtersPopoverPos, setFiltersPopoverPos] = useState<{
    top: number;
    left: number;
  } | null>(null);

  const computeFiltersPopoverPos = useCallback(() => {
    const el = filtersButtonRef.current;
    if (!el) return;
    const rect = el.getBoundingClientRect();
    const spacing = 8;
    const top = Math.round(rect.bottom + spacing);
    // Initial guess aligns to button; we'll clamp after measuring real popover width
    const left = Math.round(rect.right);
    setFiltersPopoverPos({ top, left });
  }, []);

  // After render, clamp popover horizontally based on its real width so it fits viewport
  useEffect(() => {
    if (!moreFiltersOpen) return;
    const pop = filtersPopoverRef.current;
    const btn = filtersButtonRef.current;
    if (!pop || !btn) return;
    const rect = btn.getBoundingClientRect();
    const width = pop.offsetWidth;
    const maxLeft = window.innerWidth - 16 - width; // leave 8px margin on right
    const desiredLeft = Math.min(maxLeft, Math.max(8, rect.right - width));
    setFiltersPopoverPos((pos) =>
      pos ? { top: pos.top, left: desiredLeft } : pos
    );
  }, [moreFiltersOpen]);
  useEffect(() => {
    if (!moreFiltersOpen) return;
    computeFiltersPopoverPos();
    const onWin = () => computeFiltersPopoverPos();
    window.addEventListener("resize", onWin);
    window.addEventListener("scroll", onWin, true);
    return () => {
      window.removeEventListener("resize", onWin);
      window.removeEventListener("scroll", onWin, true);
    };
  }, [moreFiltersOpen, computeFiltersPopoverPos]);

  // Toggle-based columns: 2 when panel is open, 4 when hidden
  useEffect(() => {
    setGridColumns(rightPanelOpen ? 2 : 4);
  }, [rightPanelOpen]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    try {
      window.localStorage.setItem(
        SELECTION_GROUPS_KEY,
        JSON.stringify(selectionGroups)
      );
    } catch (error) {
      console.warn("Failed to persist selection groups", error);
    }
  }, [selectionGroups]);

  const [filterSelectedOnly, setFilterSelectedOnly] = useState<boolean>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-filter-selected-only");
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  });

  const [filterHasGenerated, setFilterHasGenerated] = useState<boolean>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-filter-has-generated");
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  });

  const [tagFilters, setTagFilters] = useState<string[]>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-tag-filters");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });

  // New filter states (Shopify-like) with localStorage init
  const [vendorFilters, setVendorFilters] = useState<string[]>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-vendor-filters");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });
  const [statusFilters, setStatusFilters] = useState<string[]>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-status-filters");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });
  const [typeFilters, setTypeFilters] = useState<string[]>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-type-filters");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });
  const [mediaSources, setMediaSources] = useState<
    ("original" | "generated")[]
  >(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-media-sources");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });
  const [mediaTypes, setMediaTypes] = useState<("image" | "video")[]>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-media-types");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });
  const [minMedia, setMinMedia] = useState<number | null>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-min-media");
      return saved ? JSON.parse(saved) : null;
    }
    return null;
  });

  const [sortField, setSortField] = useState<
    | "product_title"
    | "created"
    | "updated"
    | "inventory"
    | "product_type"
    | "vendor"
    | null
  >(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-sort-field");
      return (saved as any) || null;
    }
    return null;
  });
  const [sortDir, setSortDir] = useState<"asc" | "desc">(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("media-studio-sort-dir");
      return (saved as any) || "desc";
    }
    return "desc";
  });

  // Generation batch state
  const [generationBatch, setGenerationBatch] = useState<BatchState | null>(
    null
  );
  const [isInitiating, setIsInitiating] = useState(false);
  const [initiationMessage, setInitiationMessage] = useState<string | null>(
    null
  );

  // Debug panel state
  const [isDebugPanelOpen, setIsDebugPanelOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Debug logging helper (disabled)
  const addDebugLog = useCallback((action: string, data: any) => {
    // Debug logging disabled to prevent localStorage quota issues
  }, []);

  // Batch polling state
  const [activeBatches, setActiveBatches] = useState<Set<string>>(new Set());

  // Generated assets
  const [generatedImages, setGeneratedImages] = useState<
    Record<string, Asset[]>
  >({});
  const [isLoadingAssets, setIsLoadingAssets] = useState(true);
  const [assetsError, setAssetsError] = useState<string | null>(null);

  const generatedAssetsFlat = useMemo(() => {
    return Object.values(generatedImages || {}).flat();
  }, [generatedImages]);

  // Gallery pagination state
  const [galleryAssets, setGalleryAssets] = useState<GalleryAsset[]>([]);
  const [galleryPage, setGalleryPage] = useState(1);
  const [galleryHasMore, setGalleryHasMore] = useState(true);
  const [isLoadingMoreGallery, setIsLoadingMoreGallery] = useState(false);
  const [modelAssets, setModelAssets] = useState<Asset[]>([]);
  const [outfitAssets, setOutfitAssets] = useState<Asset[]>([]);
  const [sceneAssets, setSceneAssets] = useState<Asset[]>([]);
  const [activeSceneTab, setActiveSceneTab] = useState<"presets" | "saved">(
    "presets"
  );

  // Debounced search query for API calls
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchQuery);

  // Lightbox state
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [lightboxAssets, setLightboxAssets] = useState<GalleryAsset[]>([]);
  const [lightboxCurrentIndex, setLightboxCurrentIndex] = useState(0);

  // Lightbox handlers
  const handleThumbnailClick = useCallback(
    (asset: Asset, allAssets: Asset[]) => {
      // Convert Asset[] to GalleryAsset[] for lightbox compatibility
      const convertedAssets: GalleryAsset[] = allAssets
        .filter((a) => a.type === "image" || a.type === "video") // Filter out text assets
        .map((a) => ({
          id: a.id,
          productId: a.productId,
          url: a.url,
          type: a.type as "image" | "video",
          filename: a.filename,
          displayName: a.displayName,
          fileUrl: a.fileUrl,
          previewUrl: a.previewUrl,
          prompt: a.prompt,
          sourceType: a.sourceType,
          generatedAt: a.generatedAt,
        }));

      const assetIndex = convertedAssets.findIndex((a) => a.id === asset.id);
      setLightboxAssets(convertedAssets);
      setLightboxCurrentIndex(assetIndex >= 0 ? assetIndex : 0);
      setIsLightboxOpen(true);
    },
    []
  );

  const handleLightboxClose = useCallback(() => {
    setIsLightboxOpen(false);
  }, []);

  const handleLightboxIndexChange = useCallback((index: number) => {
    setLightboxCurrentIndex(index);
  }, []);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setCurrentPage(1); // Reset to first page when searching
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [
    tagFilters,
    collectionFilters,
    vendorFilters,
    statusFilters,
    typeFilters,
    mediaSources,
    mediaTypes,
    minMedia,
    sortField,
    sortDir,
  ]);
  // Check for authentication token (gate queries)
  const [hasToken, setHasToken] = useState(false);
  useEffect(() => {
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("token");
      setHasToken(!!token);
    }
  }, []);

  // React Query: infinite products query
  const backendSearch = useMemo(
    () => (debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : ""),
    [debouncedSearchQuery]
  );

  const productsQuery = useInfiniteQuery({
    queryKey: [
      "media-studio-products",
      backendSearch,
      itemsPerPage,
      vendorFilters,
      tagFilters,
      statusFilters,
      typeFilters,
      collectionFilters,
      mediaSources,
      mediaTypes,
      minMedia,
      sortField,
      sortDir,
    ],
    initialPageParam: 1,
    queryFn: async ({ pageParam, signal }) =>
      productService.getProductsOptimized(
        (pageParam as number) || 1,
        itemsPerPage,
        {
          search: backendSearch || undefined,
          vendors: vendorFilters,
          tags: tagFilters,
          status: statusFilters,
          types: typeFilters,
          collections: collectionFilters,
          media_sources: mediaSources,
          media_types: mediaTypes,
          min_media: minMedia,
          sort_field: sortField || undefined,
          sort_dir: sortDir,
        },
        { signal }
      ),
    getNextPageParam: (lastPage) => {
      if (!lastPage) return undefined;
      const next = (lastPage.page || 1) + 1;
      const total = lastPage.total_pages || 1;
      return next <= total ? next : undefined;
    },
    staleTime: 60 * 1000,
    gcTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 2,
    enabled: hasToken,
  });

  // Lightweight performance instrumentation
  const firstPageStartRef = useRef<number | null>(null);
  const filterChangeAtRef = useRef<number | null>(null);

  useEffect(() => {
    if (firstPageStartRef.current === null)
      firstPageStartRef.current = performance.now();
  }, []);

  // Track when backendSearch changes (server query input)
  useEffect(() => {
    if (backendSearch !== undefined) {
      filterChangeAtRef.current = performance.now();
    }
  }, [backendSearch]);

  // Log time to first page and filter-to-first-result
  useEffect(() => {
    if (
      productsQuery.status === "success" &&
      productsQuery.data?.pages?.length
    ) {
      if (firstPageStartRef.current !== null) {
        const dt = Math.round(performance.now() - firstPageStartRef.current);
        // eslint-disable-next-line no-console
        console.log("[media-studio] time-to-first-page:", dt, "ms");
        firstPageStartRef.current = null;
      }
      if (filterChangeAtRef.current !== null) {
        const df = Math.round(performance.now() - filterChangeAtRef.current);
        // eslint-disable-next-line no-console
        console.log("[media-studio] filter-to-first-result:", df, "ms");
        filterChangeAtRef.current = null;
      }
    }
  }, [productsQuery.status, productsQuery.dataUpdatedAt]);

  // Clear all filters function
  const handleClearAllFilters = useCallback(() => {
    setSearchQuery("");
    setTagFilters([]);
    setCollectionFilters([]);
    setVendorFilters([]);
    setStatusFilters([]);
    setTypeFilters([]);
    setMediaSources([]);
    setMediaTypes([]);
    setMinMedia(null);

    setSortField(null);
    setSortDir("desc");
  }, []);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return (
      searchQuery.trim() !== "" ||
      tagFilters.length > 0 ||
      collectionFilters.length > 0 ||
      vendorFilters.length > 0 ||
      statusFilters.length > 0 ||
      typeFilters.length > 0 ||
      mediaSources.length > 0 ||
      mediaTypes.length > 0 ||
      minMedia !== null ||
      sortField !== null
    );
  }, [
    searchQuery,
    tagFilters,
    collectionFilters,
    vendorFilters,
    statusFilters,
    typeFilters,
    mediaSources,
    mediaTypes,
    minMedia,
    sortField,
  ]);

  // Helper functions for parsing product data
  const parseProductTags = useCallback((tagsString?: string): string[] => {
    if (!tagsString) return [];

    // First try to parse as JSON array
    try {
      const parsed = JSON.parse(tagsString);
      if (Array.isArray(parsed)) {
        return parsed.filter(Boolean);
      }
    } catch {
      // Not JSON, continue to string parsing
    }

    // Parse as comma-separated string (most common format)
    const result = tagsString
      .split(",")
      .map((tag) => tag.trim())
      .filter(Boolean);

    return result;
  }, []);

  const parseProductCollections = useCallback(
    (
      collectionsString?: string | null
    ): Array<{ id: string; name: string }> => {
      if (
        !collectionsString ||
        collectionsString === "null" ||
        typeof collectionsString !== "string"
      )
        return [];

      try {
        const parsed = JSON.parse(collectionsString);
        if (Array.isArray(parsed)) {
          return parsed
            .map((collection) => ({
              id: collection.id || collection.handle || String(collection.name),
              name:
                collection.name || collection.title || String(collection.id),
            }))
            .filter((c) => c.id && c.name);
        }
      } catch {
        // Not JSON, continue to fallback
      }

      // Fallback: treat as comma-separated collection names
      return collectionsString
        .split(",")
        .map((name) => name.trim())
        .filter(Boolean)
        .map((name) => ({
          id: name.toLowerCase().replace(/\s+/g, "-"),
          name: name,
        }));
    },
    []
  );

  // Precomputed accessors (prefer precomputed fields if present)
  const getParsedTags = useCallback(
    (p: Product): string[] => p.parsed_tags ?? parseProductTags(p.tags),
    [parseProductTags]
  );
  const getParsedCollections = useCallback(
    (p: Product): Array<{ id: string; name: string; color?: string }> =>
      p.parsed_collections ?? parseProductCollections(p.collections),
    [parseProductCollections]
  );

  // Global facets (full set across user's stores)
  const { data: facets } = useQuery({
    queryKey: ["product-facets"],
    queryFn: () => productService.getProductFacets(),
    staleTime: 1000 * 60 * 10,
    enabled: hasToken,
  });

  // Get all available tags and collections from products
  const availableTags = useMemo(() => {
    const tagSet = new Set<string>();
    products.forEach((product) => {
      const tags = getParsedTags(product);
      tags.forEach((tag) => tagSet.add(tag));
    });
    const local = Array.from(tagSet).sort();
    return facets?.tags && facets.tags.length ? facets.tags : local;
  }, [products, getParsedTags, facets]);

  const availableCollections = useMemo(() => {
    if (facets?.collections && facets.collections.length) {
      return facets.collections.map((c: any) => ({
        id: String(c.id),
        name: String(c.name),
        color: "#3B82F6",
      }));
    }
    const collectionsMap = new Map<
      string,
      { id: string; name: string; color: string }
    >();
    products.forEach((product) => {
      const collections = getParsedCollections(product);
      collections.forEach((collection) => {
        if (!collectionsMap.has(collection.id)) {
          collectionsMap.set(collection.id, {
            id: collection.id,
            name: collection.name,
            color: "#3B82F6",
          });
        }
      });
    });
    return Array.from(collectionsMap.values()).sort((a, b) =>
      a.name.localeCompare(b.name)
    );
  }, [facets, products, getParsedCollections]);
  const availableVendors = useMemo(() => {
    const set = new Set<string>();
    products.forEach((p) => {
      if (p.vendor) set.add(p.vendor);
    });
    const local = Array.from(set).sort();
    return facets?.vendors && facets.vendors.length ? facets.vendors : local;
  }, [products, facets]);

  const availableTypes = useMemo(() => {
    const set = new Set<string>();
    products.forEach((p) => {
      if (p.product_type) set.add(p.product_type);
    });
    const local = Array.from(set).sort();
    return facets?.types && facets.types.length ? facets.types : local;
  }, [products, facets]);

  const availableStatuses = useMemo(() => {
    const set = new Set<string>(["active", "draft", "archived"]);
    products.forEach((p) => {
      if (p.status) set.add(p.status);
    });
    const local = Array.from(set).sort();
    return facets?.status && facets.status.length ? facets.status : local;
  }, [products, facets]);

  // Comprehensive product filtering
  const filteredProducts = useMemo(() => {
    let filtered = [...products];

    // Search filter - only apply client-side search for short queries
    // (longer queries are handled by backend search)
    if (deferredSearchQuery.trim() && deferredSearchQuery.length < 3) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter((product) => {
        // Search in basic fields (use precomputed when available)
        const searchableText =
          product.searchable_text ??
          [
            product.title,
            product.description,
            product.vendor,
            product.product_type,
            product.handle,
          ]
            .filter(Boolean)
            .join(" ")
            .toLowerCase();

        if (searchableText.includes(query)) return true;

        // Search in tags
        const tags = getParsedTags(product);
        if (tags.some((tag) => tag.toLowerCase().includes(query))) return true;

        // Search in collections
        const collections = getParsedCollections(product);
        if (
          collections.some((collection) =>
            collection.name.toLowerCase().includes(query)
          )
        )
          return true;

        // Search in metafields (use precomputed when available)
        if (
          (product.metafields && Array.isArray(product.metafields)) ||
          product.metafields_text
        ) {
          const metafieldText =
            product.metafields_text ??
            product.metafields
              ?.map((mf) => `${mf.key} ${mf.value}`)
              .join(" ")
              .toLowerCase();
          if (metafieldText && metafieldText.includes(query)) return true;
        }

        return false;
      });
    }

    // Collection filters
    if (collectionFilters.length > 0) {
      filtered = filtered.filter((product) => {
        const productCollections = getParsedCollections(product);
        return collectionFilters.some((filterId) =>
          productCollections.some((collection) => collection.id === filterId)
        );
      });
    }

    // Tag filters
    if (tagFilters.length > 0) {
      filtered = filtered.filter((product) => {
        const productTags = getParsedTags(product);
        return tagFilters.some((filterTag) => productTags.includes(filterTag));
      });
    }

    return filtered;
  }, [
    products,
    deferredSearchQuery,
    searchQuery,
    collectionFilters,
    tagFilters,
    selectedProductIds,
    generatedImages,
    getParsedTags,
    getParsedCollections,
  ]);

  // Persist filter states
  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      localStorage.setItem(
        "media-studio-collection-filters",
        JSON.stringify(collectionFilters)
      );
    }, 300);
    return () => clearTimeout(t);
  }, [collectionFilters]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      localStorage.setItem("media-studio-search-query", searchQuery);
    }, 300);
    return () => clearTimeout(t);
  }, [searchQuery]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      localStorage.setItem(
        "media-studio-tag-filters",
        JSON.stringify(tagFilters)
      );
    }, 300);
  }, [tagFilters]);

  // Persist new filter states
  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      localStorage.setItem(
        "media-studio-vendor-filters",
        JSON.stringify(vendorFilters)
      );
    }, 300);
    return () => clearTimeout(t);
  }, [vendorFilters]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      localStorage.setItem(
        "media-studio-status-filters",
        JSON.stringify(statusFilters)
      );
    }, 300);
    return () => clearTimeout(t);
  }, [statusFilters]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      localStorage.setItem(
        "media-studio-type-filters",
        JSON.stringify(typeFilters)
      );
    }, 300);
    return () => clearTimeout(t);
  }, [typeFilters]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      localStorage.setItem(
        "media-studio-media-sources",
        JSON.stringify(mediaSources)
      );
    }, 300);
    return () => clearTimeout(t);
  }, [mediaSources]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      localStorage.setItem(
        "media-studio-media-types",
        JSON.stringify(mediaTypes)
      );
    }, 300);
    return () => clearTimeout(t);
  }, [mediaTypes]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      if (minMedia == null) {
        localStorage.removeItem("media-studio-min-media");
      } else {
        localStorage.setItem(
          "media-studio-min-media",
          JSON.stringify(minMedia)
        );
      }
    }, 300);
    return () => clearTimeout(t);
  }, [minMedia]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      if (sortField == null) {
        localStorage.removeItem("media-studio-sort-field");
      } else {
        localStorage.setItem("media-studio-sort-field", sortField);
      }
    }, 300);
    return () => clearTimeout(t);
  }, [sortField]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const t = setTimeout(() => {
      localStorage.setItem("media-studio-sort-dir", sortDir);
    }, 300);
    return () => clearTimeout(t);
  }, [sortDir]);

  useEffect(() => {
    // Set loading state while the first page is loading
    const isInitialLoading =
      productsQuery.status === "pending" ||
      (productsQuery.isFetching &&
        (productsQuery.data?.pages?.length || 0) === 0);
    setIsLoadingProducts(isInitialLoading);

    if (productsQuery.error) {
      setProductsError("Failed to load products. Please try again.");
    } else {
      setProductsError(null);
    }

    const pages = productsQuery.data?.pages ?? [];
    const allItems: any[] = pages.flatMap((p) => p?.items || []);

    const transformedProductsRaw = allItems.map((item: any) => {
      const assets: Asset[] = [];
      const productForNaming = {
        title: item.title || item.name || "Product",
      } as any;

      if (item.assets && Array.isArray(item.assets)) {
        item.assets.forEach((assetItem: any) => {
          const asset: Asset = {
            id: assetItem.id,
            productId: item.product_id || item.id,
            url: assetItem.src,
            type: assetItem.type as "image" | "video",
            filename: assetItem.src?.split("/").pop() || "asset.jpg",
            displayName: assetItem.alt || `${productForNaming.title} - Asset`,
            generatedAt:
              assetItem.created_at || assetItem.updated_at || undefined,
          };
          assets.push(asset);
        });
      } else if (item.images && Array.isArray(item.images)) {
        // Fallback to images if assets not available
        item.images.forEach((img: any, idx: number) => {
          const idPart =
            img.id ?? img.external_id ?? img.src_url ?? img.src ?? idx;
          const url = img.src_url || img.src;
          const filename =
            (img.src_url || img.src || "image.jpg").split("/").pop() ||
            "image.jpg";
          const asset: Asset = {
            id: `asset_${String(idPart)}`,
            productId: item.product_id || item.id,
            url,
            type: "image" as const,
            filename,
            displayName: "",
            generatedAt: img.created_at || img.updated_at || undefined,
          };
          asset.displayName = `${productForNaming.title} - Image`;
          assets.push(asset);
        });
      }

      const imageAssets = assets.filter((a) => a.type === "image");
      const primaryThumb = imageAssets[0]?.url || assets[0]?.url;
      const searchableText = [
        item.title || item.name,
        item.description,
        item.vendor,
        item.product_type,
        item.handle,
      ]
        .filter(Boolean)
        .join(" ")
        .toLowerCase();
      const metafieldsText = Array.isArray(item.metafields)
        ? item.metafields
            .map((mf: any) => `${mf.key ?? ""} ${mf.value ?? ""}`)
            .join(" ")
            .toLowerCase()
        : undefined;

      const idVal = item.product_id ?? item.id;
      if (idVal == null) return null;

      return {
        id: idVal,
        external_id: item.external_id,
        title: item.title || item.name,
        handle: item.handle,
        vendor: item.vendor,
        product_type: item.product_type,
        description: item.description,
        variants: item.variants || [],
        collections: item.collections,
        parsed_collections: parseProductCollections(item.collections),
        assets: assets.map((a) => ({
          type: a.type,
          id: a.id,
          src: a.url,
          alt: a.displayName,
          path: a.url,
          key: a.id,
          data: {
            product_id: a.productId,
            generated_at: a.generatedAt,
            source_type: a.sourceType,
          },
        })),
        image_count: assets.length,
        image_count_images: imageAssets.length,
        primary_thumbnail: primaryThumb,
        searchable_text: searchableText,
        metafields_text: metafieldsText,

        tags: item.tags,
        parsed_tags: parseProductTags(item.tags),
        status: item.status || "active",
        published: item.published ?? true,
        store_id: item.store_id || 1,
        created_at: item.created_at || new Date().toISOString(),
        updated_at: item.updated_at || new Date().toISOString(),
      } as Product;
    });

    const transformedProducts = transformedProductsRaw.filter(
      Boolean
    ) as Product[];
    setProducts(transformedProducts);

    // Update paging info
    const first = pages[0];
    setTotalPages(first?.total_pages || 1);
    setTotalProducts(first?.total || transformedProducts.length || 0);
    setCurrentPage(pages.length || 1);
    setHasMoreProducts(!!productsQuery.hasNextPage);

    // Initialize prompts for newly loaded products without clobbering user input
    setPrompts((prev) => {
      const next: Record<string, PromptInputValue> = { ...prev };
      const activeIds = new Set(
        transformedProducts.map((product) => product.id.toString())
      );

      Object.keys(next).forEach((key) => {
        if (!activeIds.has(key)) {
          delete next[key];
        }
      });

      transformedProducts.forEach((product) => {
        const key = product.id.toString();
        if (!next[key]) {
          next[key] = createDefaultPromptValue(product.title || "product");
        }
      });

      return next;
    });
  }, [
    productsQuery.status,
    productsQuery.isFetching,
    productsQuery.data,
    productsQuery.hasNextPage,
  ]);

  // Infinite scroll: load more products
  const loadMoreProducts = useCallback(async () => {
    addDebugLog("LOAD_MORE_PRODUCTS_CALLED", {
      isLoadingMoreProducts,
      hasMoreProducts,
      currentPage,
    });

    if (productsQuery.isFetchingNextPage || !productsQuery.hasNextPage) return;

    try {
      await productsQuery.fetchNextPage();
      addDebugLog("INFINITE_SCROLL_SUCCESS_QUERY", {
        nextPage: (productsQuery.data?.pages?.length || 0) + 1,
        hasNextPage: productsQuery.hasNextPage,
      });
    } catch (e: any) {
      addDebugLog("INFINITE_SCROLL_ERROR_QUERY", {
        error: e?.message,
      });
    }
  }, [
    productsQuery.isFetchingNextPage,
    productsQuery.hasNextPage,
    productsQuery.fetchNextPage,
    productsQuery.data?.pages?.length,
    addDebugLog,
    isLoadingMoreProducts,
    hasMoreProducts,
    currentPage,
  ]);

  // Keep loading-more state in sync with React Query
  useEffect(() => {
    setIsLoadingMoreProducts(productsQuery.isFetchingNextPage);
  }, [productsQuery.isFetchingNextPage]);

  // IntersectionObserver for product infinite scroll
  useEffect(() => {
    const el = productsSentinelRef.current;
    const scrollContainer = productsScrollRef.current;
    if (!el || !scrollContainer || !hasMoreProducts || isLoadingMoreProducts)
      return;

    const observer = new IntersectionObserver(
      (entries) => {
        const first = entries[0];
        if (first.isIntersecting && hasMoreProducts && !isLoadingMoreProducts) {
          addDebugLog("PRODUCTS_SENTINEL_INTERSECTION", {
            isIntersecting: first.isIntersecting,
            hasMoreProducts,
            isLoadingMoreProducts,
            currentPage,
          });
          // Debounce the load more call to prevent rapid firing
          setTimeout(() => {
            if (hasMoreProducts && !isLoadingMoreProducts) {
              loadMoreProducts();
            }
          });
        }
      },
      { root: scrollContainer, rootMargin: "800px", threshold: 0.1 }
    );

    observer.observe(el);
    return () => {
      observer.unobserve(el);
      observer.disconnect();
    };
  }, [
    loadMoreProducts,
    hasMoreProducts,
    isLoadingMoreProducts,
    addDebugLog,
    currentPage,
  ]);

  // Prevent duplicate gallery calls and cache results
  const [isGalleryLoading, setIsGalleryLoading] = useState(false);
  const [galleryCache, setGalleryCache] = useState<{
    data: any;
    timestamp: number;
  } | null>(null);
  const GALLERY_CACHE_DURATION = 30000; // 30 seconds

  const processGeneratedAssetData = useCallback(
    (data: any) => {
      const assetItems = Array.isArray(data?.assets)
        ? data.assets
        : Array.isArray(data?.items)
          ? data.items
          : [];

      if (assetItems.length === 0) {
        setGeneratedImages({});
        return 0;
      }

      const byProduct: Record<string, Asset[]> = {};

      for (const item of assetItems) {
        const productId =
          item?.product_id ??
          item?.productId ??
          item?.data?.product_id ??
          item?.product?.id;
        if (!productId) continue;

        const fileUri = item?.file_uri ?? item?.fileUri ?? item?.src;
        const previewUri =
          item?.preview_uri ??
          item?.previewUri ??
          item?.thumbnail_url ??
          item?.thumbnailUrl ??
          item?.src ??
          item?.url ??
          fileUri;

        if (!fileUri && !previewUri) continue;

        const resolvedProductId = String(productId);
        const resolvedFileUrl = fileUri ?? previewUri;
        const resolvedPreviewUrl = previewUri ?? fileUri ?? resolvedFileUrl;

        const asset: Asset = {
          id: String(item.id),
          productId: resolvedProductId,
          url: resolvedPreviewUrl,
          type: (item.type as "image" | "video") || "image",
          filename:
            (resolvedFileUrl?.split("/").pop() ??
              item.filename ??
              item.display_name ??
              "generated-asset") ||
            "generated-asset",
          displayName:
            item.prompt ??
            item.display_name ??
            item.alt ??
            item.filename ??
            "Generated Asset",
          fileUrl: resolvedFileUrl,
          previewUrl: resolvedPreviewUrl,
          prompt: item.prompt ?? item.data?.prompt,
          generatedAt:
            item.generated_at ??
            item.generatedAt ??
            item.created_at ??
            item.createdAt ??
            item.updated_at ??
            item.updatedAt ??
            item.data?.generated_at,
          sourceType: "ai_generated",
        };

        if (!byProduct[resolvedProductId]) {
          byProduct[resolvedProductId] = [];
        }
        byProduct[resolvedProductId].push(asset);
      }

      setGeneratedImages(byProduct);
      return assetItems.length;
    },
    [setGeneratedImages]
  );

  // Load generated assets from completed jobs
  const loadGeneratedAssetsFromJobs = useCallback(async () => {
    try {
      addDebugLog("LOAD_GENERATED_ASSETS_FROM_JOBS_START", {
        timestamp: Date.now(),
      });

      // Get recent completed jobs
      const response = await fetch(
        "http://localhost:8123/api/media/jobs?status=completed&limit=50",
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to load jobs: ${response.status}`);
      }

      const data = await response.json();
      const jobs = data.jobs || [];

      // Process job variants into generated assets
      const byProduct: Record<string, Asset[]> = {};

      for (const job of jobs) {
        if (!job.variants || job.variants.length === 0) continue;

        // Extract product ID from job (assuming it's stored in the job)
        const productId = String(job.product_id || "unknown");

        for (const variant of job.variants) {
          if (variant.status !== "COMPLETED") continue;

          const imageUrl = variant.image_url;
          const videoUrl = variant.video_url;
          const thumbnailUrl = variant.thumbnail_url;

          if (!imageUrl && !videoUrl) continue;

          const asset: Asset = {
            id: variant.variant_id,
            productId: productId,
            url: imageUrl || thumbnailUrl || videoUrl,
            type: imageUrl ? "image" : "video",
            filename: `${variant.variant_name || "generated"}.${imageUrl ? "jpg" : "mp4"}`,
            displayName: variant.variant_name || "Generated Asset",
            fileUrl: imageUrl || videoUrl,
            previewUrl: thumbnailUrl || imageUrl || videoUrl,
            generatedAt: new Date().toISOString(),
            sourceType: "ai_generated",
          };

          if (!byProduct[productId]) {
            byProduct[productId] = [];
          }
          byProduct[productId].push(asset);
        }
      }

      setGeneratedImages(byProduct);

      addDebugLog("LOAD_GENERATED_ASSETS_FROM_JOBS_SUCCESS", {
        jobsCount: jobs.length,
        assetsCount: Object.values(byProduct).flat().length,
        productIds: Object.keys(byProduct),
      });
    } catch (error: any) {
      console.error("Failed to load generated assets from jobs:", error);
      addDebugLog("LOAD_GENERATED_ASSETS_FROM_JOBS_ERROR", {
        error: error.message,
      });
    }
  }, [addDebugLog]);

  // Load generated assets with retry logic (legacy method for compatibility)
  const loadGeneratedAssets = useCallback(
    async (retryCount = 0) => {
      addDebugLog("LOAD_GENERATED_ASSETS_START", {
        retryCount,
        isGalleryLoading,
        hasCache: !!galleryCache,
        timestamp: Date.now(),
      });

      // First try to load from jobs
      await loadGeneratedAssetsFromJobs();

      // Check cache first for fallback
      const now = Date.now();
      if (
        galleryCache &&
        now - galleryCache.timestamp < GALLERY_CACHE_DURATION
      ) {
        const cachedCount = processGeneratedAssetData(galleryCache.data);
        addDebugLog("GALLERY_CACHE_HIT", {
          cacheAge: now - galleryCache.timestamp,
          reason: "Using cached data as fallback",
          assetCount: cachedCount,
        });
        return;
      }

      // Prevent duplicate calls
      if (isGalleryLoading) {
        addDebugLog("GALLERY_LOAD_SKIPPED", {
          reason: "Already loading",
        });
        return;
      }

      try {
        setIsGalleryLoading(true);
        setIsLoadingAssets(true);
        setAssetsError(null);

        addDebugLog("GALLERY_LOAD_START", {
          timestamp: new Date().toISOString(),
          retryCount,
        });

        // Call the assets endpoint using the shared API client so the base
        // URL and auth headers stay consistent across environments.
        const response = await api.get("/api/assets/", {
          params: {
            page: 1,
            limit: 20,
          },
          timeout: 15000,
        });

        addDebugLog("GALLERY_API_RESPONSE", {
          status: response.status,
          statusText: response.statusText,
        });

        const data = response.data;

        // Cache the response
        setGalleryCache({
          data,
          timestamp: Date.now(),
        });

        const processedCount = processGeneratedAssetData(data);

        addDebugLog("GALLERY_DATA_RECEIVED", {
          assetsCount: processedCount,
          totalAssets: data?.total ?? processedCount,
        });

        if (processedCount === 0) {
          addDebugLog("GALLERY_EMPTY_RESPONSE", {
            message: "Gallery API returned 0 items, skipping data processing",
          });
        }

        // Do not populate gallery here; gallery is loaded from all assets separately
      } catch (error: any) {
        console.error("Failed to load generated assets:", error);
        const axiosError = isAxiosError(error) ? error : null;
        const status = axiosError?.response?.status;
        const isTimeout =
          axiosError?.code === "ECONNABORTED" ||
          error?.message?.toLowerCase?.().includes("timeout");

        addDebugLog("GALLERY_LOAD_ERROR", {
          error: error?.message,
          status,
          statusText: axiosError?.response?.statusText,
          isTimeout,
          retryCount,
        });

        // Retry logic for timeouts (max 2 retries)
        if (isTimeout && retryCount < 2) {
          addDebugLog("GALLERY_RETRY_ATTEMPT", {
            retryCount: retryCount + 1,
            reason: "Timeout occurred",
          });
          // Wait a bit before retrying
          setTimeout(
            () => {
              loadGeneratedAssets(retryCount + 1);
            },
            2000 * (retryCount + 1)
          ); // Exponential backoff: 2s, 4s
          return;
        }

        // Provide more specific error messages
        if (isTimeout) {
          setAssetsError(
            "Gallery loading timed out after multiple attempts. The server may be busy. Please try again later."
          );
        } else if (
          axiosError?.code === "ERR_NETWORK" ||
          error?.message?.includes("Failed to fetch")
        ) {
          setAssetsError(
            "Network error loading gallery. Please check your connection."
          );
        } else {
          setAssetsError("Failed to load generated assets. Please try again.");
        }
      } finally {
        setIsLoadingAssets(false);
        setIsGalleryLoading(false);
      }
    },
    [
      addDebugLog,
      isGalleryLoading,
      galleryCache,
      GALLERY_CACHE_DURATION,
      processGeneratedAssetData,
    ]
  );
  // Load initial gallery assets (all images from all products, not just generated)
  const loadInitialGalleryAssets = useCallback(async () => {
    try {
      setIsLoadingAssets(true);
      setAssetsError(null);

      const response = await assetService.getAssets({
        page: 1,
        limit: 60,
      });

      // Map product/library assets
      const libAssets: GalleryAsset[] = response.items.map((item: any) => ({
        id: item.id,
        productId: item.data?.product_id || "",
        url: item.src,
        type: (item.type as "image" | "video") || "image",
        filename: item.src?.split("/").pop() || "asset.jpg",
        displayName: item.alt,
        fileUrl: item.src,
        previewUrl: item.data?.preview_uri,
        prompt: item.data?.prompt,
        sourceType:
          ((item.source_type || item.data?.source_type) as
            | "product"
            | "ai_generated") || "product",
        generatedAt:
          item.data?.generated_at ||
          item.data?.created_at ||
          item.data?.updated_at,
      }));

      // Use only library assets on initial load; generated assets are fetched separately
      setGalleryAssets(libAssets);
      setGalleryPage(1);
      setGalleryHasMore(response.total_pages > 1);
    } catch (error) {
      console.error("Failed to load gallery assets:", error);
      setAssetsError("Failed to load gallery assets");
    } finally {
      setIsLoadingAssets(false);
    }
  }, []);

  // Load more gallery assets for infinite scroll
  const loadMoreGalleryAssets = useCallback(async () => {
    addDebugLog("LOAD_MORE_GALLERY_CALLED", {
      isLoadingMoreGallery,
      galleryHasMore,
      galleryPage,
    });
    if (isLoadingMoreGallery || !galleryHasMore) return;

    try {
      setIsLoadingMoreGallery(true);
      const nextPage = galleryPage + 1;

      const response = await assetService.getAssets({
        page: nextPage,
        limit: 60,
      });

      if (response.items.length > 0) {
        // Transform new assets
        const newAssets: GalleryAsset[] = response.items.map((item: any) => ({
          id: item.id,
          productId: item.data?.product_id || "",
          url: item.src,
          type: (item.type as "image" | "video") || "image",
          filename: item.src?.split("/").pop() || "asset.jpg",
          displayName: item.alt,
          fileUrl: item.src,
          previewUrl: item.data?.preview_uri,
          prompt: item.data?.prompt,
          sourceType:
            ((item.source_type || item.data?.source_type) as
              | "product"
              | "ai_generated") || "product",
          generatedAt:
            item.data?.generated_at ||
            item.data?.created_at ||
            item.data?.updated_at,
        }));

        setGalleryAssets((prev) => [...prev, ...newAssets]);
        setGalleryPage(nextPage);
        setGalleryHasMore(response.total_pages > nextPage);
      } else {
        setGalleryHasMore(false);
      }
    } catch (error) {
      console.error("Failed to load more gallery assets:", error);
      setGalleryHasMore(false);
    } finally {
      setIsLoadingMoreGallery(false);
    }
  }, [galleryPage, galleryHasMore, isLoadingMoreGallery, addDebugLog]);

  useEffect(() => {
    loadGeneratedAssets();
    loadInitialGalleryAssets();
  }, [loadGeneratedAssets, loadInitialGalleryAssets]);

  // Ensure gallery loads when tab changes to Products (former Gallery)
  useEffect(() => {
    if (activeMainTab === "gallery" && galleryAssets.length === 0) {
      loadInitialGalleryAssets();
    }
  }, [activeMainTab, galleryAssets.length, loadInitialGalleryAssets]);

  // Poll job status for active jobs
  const pollJobStatus = useCallback(
    async (jobId: string) => {
      // Skip polling for invalid job IDs
      if (!jobId || typeof jobId !== "string" || jobId === "None") {
        console.warn("Skipping poll for invalid job ID:", jobId);
        setActiveBatches((prev) => {
          const newSet = new Set(prev);
          newSet.delete(jobId);
          return newSet;
        });
        return;
      }

      try {
        const jobStatus = await mediaService.getJobStatus(jobId);

        // Check if job is complete
        if (
          jobStatus.status?.toLowerCase() === "completed" ||
          jobStatus.status?.toLowerCase() === "failed"
        ) {
          // Remove from active jobs
          setActiveBatches((prev) => {
            const newSet = new Set(prev);
            newSet.delete(jobId);
            return newSet;
          });

          // Count completed jobs
          const completedJobs = (jobStatus.jobs || []).filter(
            (j) => j.status === "COMPLETED"
          ).length;
          const failedJobs = (jobStatus.jobs || []).filter(
            (j) => j.status === "FAILED"
          ).length;

          // Show notification
          if (completedJobs > 0) {
            toast.success(
              `✅ Generation complete! ${completedJobs} job(s) generated`
            );
            // Refresh generated assets to show new images
            loadGeneratedAssetsFromJobs();
          }

          if (failedJobs > 0) {
            toast.error(`❌ ${failedJobs} job(s) failed`);
          }

          // Update generation batch state
          setGenerationBatch((prev) => {
            if (!prev) return null;
            const newCompleted = prev.completed + completedJobs;
            const newFailed = prev.failed + failedJobs;
            return {
              ...prev,
              completed: newCompleted,
              failed: newFailed,
              status:
                newCompleted + newFailed >= prev.total
                  ? "completed"
                  : prev.status,
            };
          });
        } else {
          // Update progress for ongoing job
          setGenerationBatch((prev) => {
            if (!prev) return null;
            const completedJobs = (jobStatus.jobs || []).filter(
              (j) => j.status === "COMPLETED"
            ).length;
            return {
              ...prev,
              completed: Math.max(prev.completed, completedJobs),
            };
          });
        }
      } catch (error) {
        console.error("Failed to poll job status:", error);
        // Remove failed job from active polling
        setActiveBatches((prev) => {
          const newSet = new Set(prev);
          newSet.delete(jobId);
          return newSet;
        });
      }
    },
    [loadGeneratedAssets]
  );

  // Polling effect for active jobs
  useEffect(() => {
    if (activeBatches.size === 0) return;

    const interval = setInterval(() => {
      activeBatches.forEach((jobId) => {
        pollJobStatus(jobId);
      });
    }, 5000); // Poll every 5 seconds

    return () => clearInterval(interval);
  }, [activeBatches, pollJobStatus]);

  // Update settings when mode changes
  useEffect(() => {
    if (generationMode === "image") {
      setSettings({
        size: "1024x1024",
        guidance: 7.5,
        steps: 25,
        strength: 0.8,
        seed: Math.floor(Math.random() * 100000),
        upscale: true,
        safety: true,
        aspectRatio: "1:1",
        quality: "Standard",
      });
      setSelectedModelId(MODELS.filter((m) => !m.id.includes("veo"))[0].id);
    } else if (generationMode === "video") {
      setSettings({
        duration: 4,
        fps: 24,
        resolution: "1080p",
        aspectRatio: "16:9",
        motionStrength: 5,
        seed: Math.floor(Math.random() * 100000),
        audio: false,
        quality: "Standard",
      });
      setSelectedModelId("veo-3.0-generate-preview");
    }
  }, [generationMode]);

  const handleAssetSelect = useCallback(
    (asset: Asset, isMultiSelect: boolean) => {
      setSelectedAssetIds((prevSelectedAssets: Set<string>) => {
        let isToggleOff = false;
        const newAssetSet = new Set(prevSelectedAssets);

        if (isMultiSelect) {
          if (newAssetSet.has(asset.id)) {
            newAssetSet.delete(asset.id);
            isToggleOff = true;
          } else {
            newAssetSet.add(asset.id);
            // In video mode, limit to max 1 selected image per row
            if (generationMode === "video" && asset.type === "image") {
              const row = products.find(
                (p: Product) => p.id === Number(asset.productId)
              );
              const imgIds = (row?.assets || [])
                .filter((a: any) => a.type === "image")
                .map((a: any) => a.id);
              for (const id of imgIds) {
                if (id !== asset.id) newAssetSet.delete(id);
              }
            }
          }
        } else {
          const productAssets =
            products
              .find((p: Product) => p.id === Number(asset.productId))
              ?.assets?.map((a: any) => a.id) || [];

          for (const assetId of productAssets) {
            newAssetSet.delete(assetId);
          }

          if (!(productAssets.length === 1 && productAssets[0] === asset.id)) {
            newAssetSet.add(asset.id);
          } else {
            isToggleOff = true;
          }
        }

        // Update product selection based on asset selection
        const productHasSelection = products
          .find((p) => p.id === Number(asset.productId))
          ?.assets?.some((a: any) => newAssetSet.has(a.id));

        setSelectedProductIds((prevSelectedProducts) => {
          const newProductsSet = new Set(prevSelectedProducts);
          if (productHasSelection) {
            newProductsSet.add(asset.productId);
          } else {
            newProductsSet.delete(asset.productId);
          }
          return newProductsSet;
        });

        return newAssetSet;
      });
    },
    [products, generationMode]
  );

  const handleAssetAction = useCallback(
    (action: AssetAction, asset: Asset) => {
      const normalized: Asset = {
        ...asset,
        type: asset.type || "image",
        filename: asset.filename || asset.id || "asset.jpg",
        displayName:
          asset.displayName || asset.filename || asset.id || "Selected Asset",
        productId: asset.productId ?? "",
      };

      const addToCollection = (
        setter: React.Dispatch<React.SetStateAction<Asset[]>>,
        collectionName: string
      ) => {
        let didAdd = false;
        setter((prev) => {
          if (prev.some((item) => item.id === normalized.id)) {
            return prev;
          }
          didAdd = true;
          return [...prev, normalized];
        });
        if (didAdd) {
          toast.success(`Added to ${collectionName}`);
        }
      };

      const removeFromCollections = () => {
        let removed = false;

        setModelAssets((prev) => {
          const filtered = prev.filter((item) => item.id !== normalized.id);
          if (filtered.length !== prev.length) {
            removed = true;
            return filtered;
          }
          return prev;
        });

        setOutfitAssets((prev) => {
          const filtered = prev.filter((item) => item.id !== normalized.id);
          if (filtered.length !== prev.length) {
            removed = true;
            return filtered;
          }
          return prev;
        });

        setSceneAssets((prev) => {
          const filtered = prev.filter((item) => item.id !== normalized.id);
          if (filtered.length !== prev.length) {
            removed = true;
            return filtered;
          }
          return prev;
        });

        setGalleryAssets((prev) => {
          const filtered = prev.filter((item) => item.id !== normalized.id);
          if (filtered.length !== prev.length) {
            removed = true;
            return filtered;
          }
          return prev;
        });

        setSelectedAssetIds((prev) => {
          if (!prev.has(normalized.id)) {
            return prev;
          }
          const next = new Set(prev);
          next.delete(normalized.id);
          removed = true;
          return next;
        });

        setPrompts((prev) => {
          let changed = false;
          const next: Record<string, PromptInputValue> = { ...prev };
          Object.entries(prev).forEach(([key, value]) => {
            if (!value) return;
            const filteredAttachments = value.attachments.filter(
              (attachment) => attachment.assetId !== normalized.id
            );
            if (filteredAttachments.length !== value.attachments.length) {
              changed = true;
              next[key] = {
                ...value,
                attachments: filteredAttachments,
              };
            }
          });
          if (!changed) {
            return prev;
          }
          removed = true;
          return next;
        });

        setGeneratedImages((prev) => {
          let changed = false;
          const next: Record<string, Asset[]> = {};
          Object.entries(prev).forEach(([productId, list]) => {
            const filtered = list.filter((item) => item.id !== normalized.id);
            if (filtered.length !== list.length) {
              changed = true;
              removed = true;
            }
            if (filtered.length > 0) {
              next[productId] = filtered;
            }
          });
          return changed ? next : prev;
        });

        if (removed) {
          toast.success("Image deleted");
        } else {
          toast.error("Unable to delete image");
        }
      };

      switch (action) {
        case "add_to_models":
          addToCollection(setModelAssets, "Models");
          break;
        case "add_to_outfits":
          addToCollection(setOutfitAssets, "Outfits");
          break;
        case "add_to_scenes":
          addToCollection(setSceneAssets, "Scenes");
          break;
        case "delete":
          if (normalized.sourceType !== "ai_generated") {
            toast.error("Only generated images can be deleted");
            return;
          }
          removeFromCollections();
          break;
        default:
          break;
      }
    },
    [
      setModelAssets,
      setOutfitAssets,
      setSceneAssets,
      setGalleryAssets,
      setSelectedAssetIds,
      setPrompts,
      setGeneratedImages,
    ]
  );

  const handleProductSelectionChange = useCallback(
    (productId: string, isChecked: boolean) => {
      addDebugLog("PRODUCT_SELECTION_CHANGE_START", {
        productId,
        isChecked,
        productIdType: typeof productId,
        productsCount: products.length,
        sampleProductIds: products
          .slice(0, 3)
          .map((p) => ({ id: p.id, type: typeof p.id })),
      });

      const product = products.find((p) => p.id === parseInt(productId, 10));
      if (!product) {
        addDebugLog("PRODUCT_NOT_FOUND", {
          productId,
          availableIds: products.slice(0, 5).map((p) => p.id),
        });
        return;
      }

      addDebugLog("PRODUCT_SELECTION_CHANGE", { productId, isChecked });

      setSelectedProductIds((prev) => {
        const newSet = new Set(prev);
        if (isChecked) newSet.add(productId);
        else newSet.delete(productId);
        addDebugLog("PRODUCT_SELECTION_UPDATED", {
          productId,
          isChecked,
          newSelectedCount: newSet.size,
          selectedProductIds: Array.from(newSet),
        });
        return newSet;
      });

      if (isChecked) {
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);

          if (!product.assets || product.assets.length === 0) return newSet;

          // Check if this product already has any selected assets
          const productHasSelectedAssets = product.assets.some((asset) =>
            prev.has(asset.id)
          );

          if (!productHasSelectedAssets) {
            // If no assets selected for this product, select the first image as default
            const firstImg = product.assets.find((a) => a.type === "image");
            if (firstImg) newSet.add(firstImg.id);
          }
          // If product already has selected assets, keep them as-is

          return newSet;
        });
      } else {
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          if (product.assets) {
            product.assets.forEach((asset) => newSet.delete(asset.id));
          }
          return newSet;
        });
      }
    },
    [products, generationMode, addDebugLog]
  );

  const handleSaveSelectionGroup = useCallback(
    async (groupName: string) => {
      const trimmed = groupName.trim();
      if (!trimmed) {
        toast.error("Please enter a group name.");
        throw new Error("Group name is required");
      }
      if (selectedProductIds.size === 0) {
        toast.error("Select at least one product to save a group.");
        throw new Error("No products selected");
      }
      const exists = selectionGroups.some(
        (group) => group.name.toLowerCase() === trimmed.toLowerCase()
      );
      if (exists) {
        toast.error("A group with that name already exists.");
        throw new Error("Group name already exists");
      }

      setIsSavingGroup(true);
      try {
        const createdAt = Date.now();
        const newGroup: SelectionGroup = {
          id: `group-${createdAt}`,
          name: trimmed,
          productIds: Array.from(selectedProductIds),
          createdAt,
        };
        setSelectionGroups((prev) => [newGroup, ...prev]);
        addDebugLog("SELECTION_GROUP_SAVED", newGroup);
        toast.success(
          `Saved ${newGroup.productIds.length} product${
            newGroup.productIds.length === 1 ? "" : "s"
          } to "${trimmed}"`
        );
      } finally {
        setIsSavingGroup(false);
      }
    },
    [selectedProductIds, selectionGroups, addDebugLog]
  );

  const handleSelectAllProducts = useCallback(
    (isChecked: boolean) => {
      if (isChecked) {
        // Select all filtered products - let individual product logic handle asset selection
        const allFilteredProductIds = new Set(
          filteredProducts.map((p) => p.id.toString())
        );
        setSelectedProductIds(allFilteredProductIds);

        // For each product being selected, apply the same logic as individual product selection
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          filteredProducts.forEach((product) => {
            if (!product.assets || product.assets.length === 0) return;

            // Check if this product already has any selected assets
            const productHasSelectedAssets = product.assets.some((asset) =>
              prev.has(asset.id)
            );

            if (!productHasSelectedAssets) {
              // If no assets selected for this product, select the first image (default behavior)
              const firstImg = product.assets.find((a) => a.type === "image");
              if (firstImg) newSet.add(firstImg.id);
            }
            // If product already has selected assets, keep them as-is
          });
          return newSet;
        });
      } else {
        setSelectedProductIds(new Set());
        setSelectedAssetIds(new Set());
        setActiveAsset(null);
      }
    },
    [filteredProducts, generationMode]
  );

  const handlePromptChange = useCallback(
    (productId: string, value: PromptInputValue) => {
      setPrompts((prev) => ({
        ...prev,
        [productId]: clonePromptValue(value),
      }));
    },
    []
  );

  const handleCopyPromptToAll = useCallback(
    (sourceProductId: string) => {
      const sourcePrompt = prompts[sourceProductId];
      if (!sourcePrompt) return;

      setPrompts((prev) => {
        const next: Record<string, PromptInputValue> = { ...prev };
        products.forEach((product: Product) => {
          next[product.id.toString()] = clonePromptValue(sourcePrompt);
        });
        return next;
      });
    },
    [prompts, products]
  );

  // Image attachment handlers for generation composition
  const handleGenerate = useCallback(async () => {
    const selectedCount = selectedProductIds.size;
    if (selectedCount === 0) {
      toast.error("Please select at least one product to generate media for.");
      return;
    }
    if (selectedCount === 0) return;

    if (generationMode === "text") {
      toast.info("Text generation coming soon.");
      return;
    }

    const modeCap = generationMode === "video" ? 3 : 5;
    if (selectedCount > modeCap) {
      toast.error(
        `Maximum ${modeCap} products allowed for ${generationMode} generation.`
      );
      return;
    }

    try {
      setIsInitiating(true);
      setInitiationMessage(
        `Batch started (${selectedCount} items) • Mode: ${generationMode} • Model: ${MODELS.find((m) => m.id === selectedModelId)?.name || selectedModelId}`
      );

      const productIds = Array.from(selectedProductIds).slice(0, modeCap);

      // Create provider config from settings (shared across all requests)
      const providerConfig: Record<string, any> = {
        size: (settings as any).size || "1024x1024",
        guidance: (settings as any).guidance || 7.5,
        steps: (settings as any).steps || 25,
        strength: (settings as any).strength || 0.8,
        seed: settings.seed,
        upscale: (settings as any).upscale ?? true,
        safety: (settings as any).safety ?? true,
        aspect_ratio: settings.aspectRatio || "1:1",
        quality: (settings as any).quality || "Standard",
      };

      // Prepare all requests
      const requests = productIds.map((productId) => {
        const product = products.find((p) => p.id === productId);
        const imageAssetIds = (product?.assets || [])
          .filter((a) => a.type === "image")
          .map((a) => a.id);
        const selectedInRow = Array.from(selectedAssetIds).filter((aid) =>
          imageAssetIds.includes(aid)
        );

        const promptValue =
          prompts[productId] ??
          createDefaultPromptValue(product?.title || "product");
        const attachmentUrls = (promptValue.attachments || [])
          .map((attachment) => attachment.url)
          .filter(Boolean);

        const referenceImages: string[] = (() => {
          if (attachmentUrls.length > 0) {
            const deduped = [...attachmentUrls];

            const productGeneratedImages = generatedImages[productId] || [];
            const allImages = [
              ...(product?.assets?.filter((a) => a.type === "image") || []),
              ...(product?.assets?.filter((a) => a.type === "image") || []),
              ...productGeneratedImages.filter((a) => a.type === "image"),
            ];

            const additionalSelected = allImages.filter((asset) =>
              selectedInRow.includes(asset.id)
            );

            additionalSelected.forEach((asset) => {
              const assetUrl = (asset as any).url ?? (asset as any).src;
              if (!deduped.includes(assetUrl)) {
                deduped.push(assetUrl);
              }
            });

            const finalUrls = deduped.slice(0, 10);

            return finalUrls;
          }

          // Prefer any manually selected images in this product row
          const productGeneratedImages = generatedImages[productId] || [];
          const allImages = [
            ...(product?.assets?.filter((a) => a.type === "image") || []),
            ...(product?.assets?.filter((a) => a.type === "image") || []),
            ...productGeneratedImages.filter((a) => a.type === "image"),
          ];

          // Get selected images for this product
          const selectedInProduct = allImages.filter((a) =>
            selectedAssetIds.has(a.id)
          );
          if (selectedInProduct.length > 0) {
            return selectedInProduct
              .filter((img: any) => img.url && typeof img.url === "string")
              .map((img: any) => img.url);
          }

          // Fallback to first image if no selection
          const firstImg = allImages.find((a) => a.type === "image");
          return firstImg &&
            (firstImg as any).url &&
            typeof (firstImg as any).url === "string"
            ? [(firstImg as any).url]
            : [];
        })();

        const request: MediaGenerateRequest = {
          media_type: generationMode,
          provider: selectedModelId,
          item: {
            product_id: productId,
            prompt: (() => {
              const text = promptValue.text?.trim();
              if (!text) {
                return `Professional product shot of ${product?.title}`;
              }
              return text;
            })(),
            provider_config: providerConfig,
            reference_images: referenceImages,
            quantity: 1,
          },
          shop_id: product?.store_id,
        };

        return request;
      });

      // Send all requests in parallel
      const results = await Promise.allSettled(
        requests.map((request) => mediaService.startGeneration(request))
      );

      // Collect all successful job IDs
      const allJobIds: string[] = [];
      let successCount = 0;
      let failureCount = 0;

      results.forEach((result, index) => {
        if (result.status === "fulfilled") {
          const jobIds = result.value.jobs
            .map((job) => job.job_id)
            .filter(
              (jobId) => jobId && typeof jobId === "string" && jobId !== "None"
            ); // Filter out invalid job IDs
          allJobIds.push(...jobIds);
          successCount++;
        } else {
          console.error(
            `Generation failed for product ${productIds[index]}:`,
            result.reason
          );
          failureCount++;
        }
      });

      // Create a batch state for UI compatibility
      setGenerationBatch({
        batchId: allJobIds[0] || "batch", // Use first job ID as batch ID for UI
        total: allJobIds.length * 4, // Assume 4 variants per job
        completed: 0,
        failed: 0,
        status: "processing",
      });

      // Add all job IDs to active batches for polling
      setActiveBatches((prev) => {
        const newSet = new Set(prev);
        allJobIds.forEach((jobId) => newSet.add(jobId));
        return newSet;
      });

      if (successCount > 0) {
        toast.success(`✅ Generation started for ${successCount} product(s)!`);
      }

      if (failureCount > 0) {
        toast.error(`❌ ${failureCount} product(s) failed to start generation`);
      }
    } catch (error: any) {
      console.error("Generation failed:", error);
      setErrorMessage(error?.message || "Failed to start generation");
      toast.error(`❌ Generation failed: ${error.message}`);
    } finally {
      setTimeout(() => {
        setIsInitiating(false);
        setInitiationMessage(null);
      }, 6000);
    }
  }, [
    selectedProductIds,
    generationMode,
    selectedModelId,
    settings,
    products,
    selectedAssetIds,
    prompts,
    generatedImages,
  ]);

  const clearSelection = useCallback(() => {
    setSelectedAssetIds(new Set());
    setSelectedProductIds(new Set());
    setActiveAsset(null);
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        clearSelection();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [clearSelection]);

  useEffect(() => {
    if (typeof window === "undefined") {
      return;
    }

    const handleOpenDebug = () => setIsDebugPanelOpen(true);
    window.addEventListener("media-studio:open-debug", handleOpenDebug);
    return () => {
      window.removeEventListener("media-studio:open-debug", handleOpenDebug);
    };
  }, []);

  const overLimit =
    selectedProductIds.size > (generationMode === "video" ? 3 : 5);
  const overLimitMessage = `Maximum ${generationMode === "video" ? 3 : 5} products allowed for ${generationMode} generation.`;

  // Derived selection helpers for sidebar controls
  const allSelected =
    selectedProductIds.size > 0 &&
    selectedProductIds.size === filteredProducts.length &&
    filteredProducts.every((p) => selectedProductIds.has(p.id.toString()));

  const selectionAllLabel =
    selectedProductIds.size > 0
      ? `${selectedProductIds.size}/${filteredProducts.length}`
      : `${filteredProducts.length}/${totalProducts}`;

  return (
    <div className="flex flex-col bg-background font-sans text-foreground h-full min-h-0">
      {/* Main Content */}
      <main className="flex flex-1 min-h-0 overflow-hidden">
        <GenerateSidebar
          className="flex-shrink-0"
          selectedCount={selectedProductIds.size}
          generationMode={generationMode}
          onModeChange={setGenerationMode}
          settings={settings}
          setSettings={setSettings}
          selectedModelId={selectedModelId}
          onModelChange={setSelectedModelId}
          onGenerate={handleGenerate}
          generationBatch={generationBatch}
          isInitiating={isInitiating}
          initiationMessage={initiationMessage}
          overLimit={overLimit}
          overLimitMessage={overLimitMessage}
          errorMessage={errorMessage}
          onDismissMessage={() => setErrorMessage(null)}
          onDismissInitiationMessage={() => setInitiationMessage(null)}
          // Moved selection controls
          selectionAllLabel={selectionAllLabel}
          allSelected={allSelected}
          onSelectAllChange={(checked) => handleSelectAllProducts(checked)}
          filterSelectedOnly={filterSelectedOnly}
          onFilterSelectedOnlyChange={(v) => {
            setFilterSelectedOnly(v);
            if (typeof window !== "undefined") {
              localStorage.setItem(
                "media-studio-filter-selected-only",
                JSON.stringify(v)
              );
            }
          }}
          filteredCount={filteredProducts.length}
          totalCount={totalProducts}
          onSaveGroup={handleSaveSelectionGroup}
          isSavingGroup={isSavingGroup}
        />
        <div className="flex flex-1 min-h-0 overflow-hidden">
          {/* Product Panel */}
          <div className="relative flex flex-1 flex-col border-r border-[#EDEDED] dark:border-border bg-background min-h-0 min-w-0">
            {/* Left Panel Header */}
            <div className="flex-shrink-0 h-12 border-b border-[#EDEDED] dark:border-border bg-card">
              <div className="h-full flex items-center gap-3 px-4">
                {/* Search */}
                <div className="relative w-[160px] md:w-[180px] flex-none">
                  {!isSearchFocused && !searchQuery && (
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  )}
                  <Input
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onFocus={() => setIsSearchFocused(true)}
                    onBlur={() => setIsSearchFocused(false)}
                    className={`${!isSearchFocused && !searchQuery ? "pl-10" : "pl-3"} h-8 transition-[padding]`}
                  />
                </div>

                {!rightPanelOpen && (
                  <>
                    <VendorsFilter
                      options={availableVendors}
                      selected={vendorFilters}
                      onChange={setVendorFilters}
                    />

                    <MultiSelectFilter
                      label="Tags"
                      options={availableTags}
                      selected={tagFilters}
                      onChange={setTagFilters}
                      searchable
                      icon={<TagIcon className="h-3 w-3" />}
                      hideLabel
                      ariaLabel="Tags"
                    />

                    <StatusFilter
                      options={availableStatuses}
                      selected={statusFilters}
                      onChange={setStatusFilters}
                    />

                    <TypesFilter
                      options={availableTypes}
                      selected={typeFilters}
                      onChange={setTypeFilters}
                    />

                    <MediaFilter
                      sources={mediaSources}
                      onSourcesChange={setMediaSources}
                      types={mediaTypes}
                      onTypesChange={setMediaTypes}
                      minCount={minMedia}
                      onMinCountChange={setMinMedia}
                    />

                    <CollectionsFilter
                      options={availableCollections}
                      selectedIds={collectionFilters}
                      onChange={setCollectionFilters}
                      compact
                      iconOnly
                    />
                  </>
                )}

                {/* Overflow filters menu */}
                <div className="relative flex items-center gap-2">
                  <Button
                    ref={filtersButtonRef}
                    variant="outline"
                    size="sm"
                    onClick={() => setMoreFiltersOpen((v) => !v)}
                    className="px-2.5 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-muted/50 transition-colors"
                    aria-label="More filters"
                    title="More filters"
                  >
                    <SlidersHorizontal className="h-3 w-3" />
                  </Button>

                  {/* Clear all filters */}
                  {hasActiveFilters && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleClearAllFilters}
                      className="pl-2.5 pr-3 py-1.5 text-xs rounded-full h-8 focus:ring-0 focus:ring-offset-0 hover:bg-destructive/10 hover:text-destructive transition-colors duration-200 text-muted-foreground"
                    >
                      <X className="h-3 w-3 mr-1.5" />
                      Clear all
                    </Button>
                  )}

                  {moreFiltersOpen && filtersPopoverPos && (
                    <div
                      ref={filtersPopoverRef}
                      className="fixed z-50 rounded-md border bg-popover text-popover-foreground shadow-lg p-3"
                      style={{
                        top: filtersPopoverPos.top,
                        left: filtersPopoverPos.left,
                        width: "max-content",
                        maxWidth: "min(480px, calc(100vw - 16px))",
                      }}
                    >
                      <div
                        className={
                          rightPanelOpen
                            ? "grid grid-cols-1 gap-3"
                            : "grid grid-cols-2 gap-3"
                        }
                      >
                        <div className="col-span-1">
                          <div className="text-[11px] font-medium text-muted-foreground mb-1 px-0.5">
                            Vendors
                          </div>
                          <VendorsFilter
                            options={availableVendors}
                            selected={vendorFilters}
                            onChange={setVendorFilters}
                          />
                        </div>
                        <div className="col-span-1">
                          <div className="text-[11px] font-medium text-muted-foreground mb-1 px-0.5">
                            Tags
                          </div>
                          <MultiSelectFilter
                            label="Tags"
                            options={availableTags}
                            selected={tagFilters}
                            onChange={setTagFilters}
                            searchable
                          />
                        </div>
                        <div className="col-span-1">
                          <div className="text-[11px] font-medium text-muted-foreground mb-1 px-0.5">
                            Status
                          </div>
                          <StatusFilter
                            options={availableStatuses}
                            selected={statusFilters}
                            onChange={setStatusFilters}
                          />
                        </div>
                        <div className="col-span-1">
                          <div className="text-[11px] font-medium text-muted-foreground mb-1 px-0.5">
                            Types
                          </div>
                          <TypesFilter
                            options={availableTypes}
                            selected={typeFilters}
                            onChange={setTypeFilters}
                          />
                        </div>
                        <div
                          className={
                            rightPanelOpen ? "col-span-1" : "col-span-2"
                          }
                        >
                          <div className="text-[11px] font-medium text-muted-foreground mb-1 px-0.5">
                            Media
                          </div>
                          <MediaFilter
                            sources={mediaSources}
                            onSourcesChange={setMediaSources}
                            types={mediaTypes}
                            onTypesChange={setMediaTypes}
                            minCount={minMedia}
                            onMinCountChange={setMinMedia}
                          />
                        </div>
                        <div
                          className={
                            rightPanelOpen ? "col-span-1" : "col-span-2"
                          }
                        >
                          <div className="text-[11px] font-medium text-muted-foreground mb-1 px-0.5">
                            Collections
                          </div>
                          <CollectionsFilter
                            options={availableCollections}
                            selectedIds={collectionFilters}
                            onChange={setCollectionFilters}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {moreFiltersOpen && (
                    <div
                      className="fixed inset-0 z-40"
                      onClick={() => setMoreFiltersOpen(false)}
                    />
                  )}
                </div>

                <div className="ml-auto">
                  <SortDropdown
                    field={sortField}
                    dir={sortDir}
                    onChange={(f, d) => {
                      setSortField(f);
                      setSortDir(d);
                    }}
                  />
                </div>

                {/* Toggle right panel (always visible, icon-only) */}
                {rightPanelOpen ? (
                  <span
                    role="button"
                    tabIndex={0}
                    onClick={handleTogglePanel}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        handleTogglePanel();
                      }
                    }}
                    className="ml-2 inline-flex items-center justify-center text-foreground/70 hover:text-foreground"
                    aria-label="Collapse panel"
                    title="Collapse panel"
                  >
                    <PanelRightClose className="h-4 w-4" />
                  </span>
                ) : (
                  <Button
                    size="sm"
                    onClick={handleTogglePanel}
                    className="ml-2 h-8 pl-3 pr-2 text-xs font-semibold bg-primary text-primary-foreground hover:bg-primary/90"
                  >
                    Library
                    <PanelRight className="ml-2 -mr-1 h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            {/* Product Grid */}
            {/* Loading indicator at top when refreshing products */}
            {isLoadingProducts && products.length > 0 && (
              <div className="px-4 py-2 bg-muted/50 border-b">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
                  Refreshing products...
                </div>
              </div>
            )}

            <div
              className="flex-1 overflow-y-auto overflow-x-hidden min-h-0"
              ref={productsScrollRef}
            >
              {isLoadingProducts && products.length === 0 ? (
                // Initial Loading State
                <div className="flex items-center justify-center h-full bg-muted/10">
                  <div className="text-center p-8 bg-background rounded-lg border shadow-sm">
                    <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary border-t-transparent mx-auto mb-4"></div>
                    <div className="text-xl font-semibold text-foreground mb-2">
                      Loading Products...
                    </div>
                    <div className="text-muted-foreground mb-4">
                      Fetching your product catalog
                    </div>
                    <div className="text-xs text-muted-foreground">
                      This may take a few moments...
                    </div>
                  </div>
                </div>
              ) : productsError ? (
                // Error State
                <div className="flex items-center justify-center h-full">
                  <div className="text-center max-w-md">
                    <div className="text-red-500 text-6xl mb-4">
                      <AlertCircle className="h-16 w-16 mx-auto" />
                    </div>
                    <div className="text-xl font-semibold text-foreground mb-2">
                      Failed to Load Products
                    </div>
                    <div className="text-muted-foreground mb-4">
                      {productsError}
                    </div>
                    {!hasToken && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4 text-left">
                        <div className="text-sm font-medium text-yellow-800 mb-2">
                          🔑 Authentication Required
                        </div>
                        <div className="text-xs text-yellow-700 space-y-1">
                          <div>1. Open Developer Tools (F12)</div>
                          <div>2. Go to Application → Local Storage</div>
                          <div>
                            3. Add key:{" "}
                            <code className="bg-yellow-100 px-1 rounded">
                              token
                            </code>
                          </div>
                          <div>
                            4. Add value:{" "}
                            <code className="bg-yellow-100 px-1 rounded text-xs break-all">
                              eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.Of9UgZ03uFSZeNPEPIa9T5V9a6gFI7KXlXd2DbhjCMA
                            </code>
                          </div>
                          <div>5. Refresh the page</div>
                        </div>
                      </div>
                    )}
                    <Button
                      onClick={() => window.location.reload()}
                      variant="outline"
                    >
                      Try Again
                    </Button>
                  </div>
                </div>
              ) : products.length === 0 ? (
                // Empty State
                <div className="flex items-center justify-center h-full">
                  <div className="text-center max-w-md">
                    <div className="text-6xl text-muted-foreground mb-4">
                      📦
                    </div>
                    <div className="text-xl font-semibold text-foreground mb-2">
                      No Products Found
                    </div>
                    <div className="text-muted-foreground mb-4">
                      Your store doesn't have any products yet. Add some
                      products to get started with media generation.
                    </div>
                    <Button
                      onClick={() => window.location.reload()}
                      variant="outline"
                    >
                      Refresh
                    </Button>
                  </div>
                </div>
              ) : (
                // Products Grid
                <>
                  <div className="px-6 py-6" ref={productsContainerRef}>
                    <ProductGrid
                      products={filteredProducts}
                      selectedAssetIds={selectedAssetIds}
                      onAssetSelect={handleAssetSelect}
                      onAssetAction={handleAssetAction}
                      prompts={prompts}
                      onPromptChange={handlePromptChange}
                      onTabChange={setActiveMainTab}
                      selectedProductIds={selectedProductIds}
                      onProductSelectionChange={handleProductSelectionChange}
                      onSelectAllProducts={handleSelectAllProducts}
                      onCopyPromptToAll={handleCopyPromptToAll}
                      generatedImages={generatedImages as any}
                      availableCollections={availableCollections}
                      collectionFilters={collectionFilters}
                      onCollectionFiltersChange={setCollectionFilters}
                      searchQuery={searchQuery}
                      onSearchQueryChange={setSearchQuery}
                      productTotalCount={totalProducts}
                      onDebug={addDebugLog}
                      onThumbnailClick={handleThumbnailClick}
                      scrollParentRef={productsScrollRef}
                      columns={gridColumns}
                    />
                  </div>
                  {/* Infinite scroll sentinel for products */}
                  <div ref={productsSentinelRef} className="h-10" />
                </>
              )}
            </div>
          </div>

          {/* Right Panel */}
          <div
            className="relative flex-shrink-0 flex flex-col bg-background min-h-0"
            style={{
              width: rightPanelOpen ? rightPanelWidth : 0,
              transition: "width 200ms ease",
            }}
          >
            {/* Right Panel Header */}
            <div className="flex-shrink-0 h-12 border-b border-[#EDEDED] dark:border-border bg-card">
              <div className="h-full flex items-center justify-between px-4">
                <div className="flex h-full items-end gap-1">
                  {[
                    { label: "Outfits", value: "outfits" },
                    { label: "Models", value: "models" },
                    { label: "Scenes", value: "scenes" },
                    { label: "Products", value: "gallery" }, // former Gallery
                    { label: "Generated", value: "generated" },
                    { label: "Brandbook", value: "brandbook" },
                  ].map(({ label, value }) => (
                    <Button
                      key={label}
                      variant="ghost"
                      onClick={() => setActiveMainTab(value as MainTab)}
                      className={`h-full -mb-px px-3 py-0 text-sm font-medium transition-colors duration-150 rounded-none border-b-2 border-transparent hover:bg-transparent focus:ring-0 focus:ring-offset-0 ${
                        activeMainTab === (value as MainTab)
                          ? "border-primary text-foreground"
                          : "text-muted-foreground hover:text-foreground"
                      }`}
                    >
                      {label}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Panel Content */}
            <div className="flex-1 overflow-y-auto overflow-x-hidden min-h-0">
              {activeMainTab === "gallery" ? (
                <PreviewPane
                  assets={galleryAssets}
                  loading={isLoadingAssets}
                  error={assetsError}
                  onRefresh={loadInitialGalleryAssets}
                  onLoadMore={loadMoreGalleryAssets}
                  hasMore={galleryHasMore}
                  isLoadingMore={isLoadingMoreGallery}
                  onAssetAction={handleAssetAction}
                  onThumbnailClick={handleThumbnailClick}
                />
              ) : activeMainTab === "generated" ? (
                <PreviewPane
                  assets={generatedAssetsFlat}
                  loading={isLoadingAssets}
                  error={assetsError}
                  onRefresh={loadGeneratedAssets}
                  onAssetAction={handleAssetAction}
                  onThumbnailClick={handleThumbnailClick}
                />
              ) : activeMainTab === "brandbook" ? (
                <div className="h-full min-h-[200px] flex items-center justify-center text-muted-foreground">
                  <div className="text-center max-w-md">
                    <div className="text-sm font-medium mb-2">Brandbook</div>
                    <div className="text-xs">
                      Your brand assets will appear here soon.
                    </div>
                  </div>
                </div>
              ) : activeMainTab === "models" ? (
                <SavedAssetsPanel
                  assets={modelAssets}
                  action="add_to_models"
                  onAction={handleAssetAction}
                  emptyTitle="No models yet"
                  emptyDescription="Use the upload card or drag images in to curate your model references."
                  onThumbnailClick={handleThumbnailClick}
                />
              ) : activeMainTab === "outfits" ? (
                <SavedAssetsPanel
                  assets={outfitAssets}
                  action="add_to_outfits"
                  onAction={handleAssetAction}
                  emptyTitle="No outfits yet"
                  emptyDescription="Add outfit references by dropping existing images or uploading new ones."
                  onThumbnailClick={handleThumbnailClick}
                />
              ) : activeMainTab === "scenes" ? (
                <div className="flex h-full flex-col">
                  <div className="border-b border-border bg-background/80 px-4 pb-3 pt-4">
                    <div className="flex flex-wrap items-center justify-between gap-3">
                      <div>
                        <h3 className="text-sm font-semibold text-foreground">
                          Scenes
                        </h3>
                        <p className="text-xs text-muted-foreground">
                          Drag a preset or saved scene onto any prompt to append
                          its styling description.
                        </p>
                      </div>
                      <div className="inline-flex gap-2">
                        <Button
                          size="sm"
                          variant={
                            activeSceneTab === "presets" ? "default" : "ghost"
                          }
                          className="rounded-full px-4"
                          onClick={() => setActiveSceneTab("presets")}
                        >
                          Presets
                        </Button>
                        <Button
                          size="sm"
                          variant={
                            activeSceneTab === "saved" ? "default" : "ghost"
                          }
                          className="rounded-full px-4"
                          onClick={() => setActiveSceneTab("saved")}
                        >
                          Saved
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1 overflow-hidden">
                    {activeSceneTab === "presets" ? (
                      <div className="h-full overflow-y-auto px-4 py-4">
                        <SceneLibrary scenes={SCENE_PRESETS} />
                      </div>
                    ) : (
                      <div className="h-full overflow-y-auto">
                        <SavedAssetsPanel
                          assets={sceneAssets}
                          action="add_to_scenes"
                          onAction={handleAssetAction}
                          emptyTitle="No saved scenes yet"
                          emptyDescription="Collect scene inspiration by dragging thumbnails here or using the upload card."
                          onThumbnailClick={handleThumbnailClick}
                        />
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-lg font-semibold mb-2">Canvas</div>
                    <div className="text-muted-foreground">Canvas view</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Loading Indicator - Fixed near bottom of viewport */}
      {isLoadingMoreProducts && hasMoreProducts && (
        <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-background/95 backdrop-blur-sm border rounded-lg shadow-lg text-sm text-foreground">
            <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
            Loading more products…
          </div>
        </div>
      )}

      {/* Debug Panel */}
      <DebugLogPanel
        isOpen={isDebugPanelOpen}
        onClose={() => setIsDebugPanelOpen(false)}
      />

      {/* Image Lightbox */}
      <ImageLightbox
        isOpen={isLightboxOpen}
        onClose={handleLightboxClose}
        assets={lightboxAssets}
        currentIndex={lightboxCurrentIndex}
        onIndexChange={handleLightboxIndexChange}
        selectedAssetIds={new Set()}
        onAssetSelect={() => {}}
        onAction={handleAssetAction}
      />
    </div>
  );
};

export default MediaStudioPage;
