"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>olderSyncIcon,
  CheckCircle,
  Loader2,
  RefreshC<PERSON>,
  Zap,
  Clock,
  Bell,
  Eye,
  Download,
} from "lucide-react";
import { toast } from "sonner";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/services/api";

interface SyncStatus {
  products: { status: "success" | "syncing" | "error"; lastSync?: string };
}

interface SyncProgress {
  id: number;
  store_id: number;
  sync_type: string;
  status: "running" | "completed" | "failed" | "paused";
  total_items: number;
  processed_items: number;
  current_batch: number;
  total_batches: number;
  last_update: string;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

interface StoreDetails {
  external_id: string;
  shop_domain?: string;
  shop_name?: string;
  is_active?: boolean;
}

const SyncSettings: React.FC = () => {
  const searchParams = useSearchParams();
  const storeId = searchParams.get("storeId");

  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    products: { status: "success", lastSync: "5 minutes ago" },
  });
  const [syncProgress, setSyncProgress] = useState<SyncProgress | null>(null);
  const [progressHistory, setProgressHistory] = useState<string[]>([]);
  const [isDisconnected, setIsDisconnected] = useState(false);
  const [confirmStoreName, setConfirmStoreName] = useState("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // User preferences
  const [pollingFrequency, setPollingFrequency] = useState(30); // seconds
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [compactView, setCompactView] = useState(false);

  // Fetch store details using React Query
  const {
    data: storeDetails,
    isLoading: isLoadingStoreDetails,
    error: storeDetailsError,
  } = useQuery<StoreDetails>({
    queryKey: ["store-details", storeId],
    queryFn: async () => {
      if (!storeId) throw new Error("Store ID is required");
      const response = await api.get(`/api/stores/${storeId}`);
      return response.data;
    },
    enabled: !!storeId,
    retry: 2,
    retryDelay: 1000,
  });

  // Fetch sync progress using React Query with polling
  const { data: syncProgressData, refetch: refetchProgress } =
    useQuery<SyncProgress>({
      queryKey: ["sync-progress", storeId, "products"],
      queryFn: async () => {
        if (!storeId) throw new Error("Store ID is required");
        const response = await api.get(
          `/api/stores/${storeId}/sync-progress/products`
        );
        return response.data;
      },
      enabled: !!storeId,
      refetchInterval: pollingFrequency * 1000, // Use user preference for polling
      retry: 1,
      retryDelay: 1000,
    });

  // Fetch sync checkpoints
  const { data: syncCheckpoints, isLoading: isLoadingCheckpoints } = useQuery({
    queryKey: ["sync-checkpoints", storeId],
    queryFn: async () => {
      if (!storeId) throw new Error("Store ID is required");
      const response = await api.get(`/api/stores/${storeId}/sync-checkpoints`);
      return response.data;
    },
    enabled: !!storeId,
    retry: 2,
    retryDelay: 1000,
  });

  // Fetch Airbyte service status
  const {
    data: airbyteStatus,
    isLoading: isLoadingAirbyteStatus,
    refetch: refetchAirbyteStatus,
  } = useQuery({
    queryKey: ["airbyte-status", storeId],
    queryFn: async () => {
      if (!storeId) throw new Error("Store ID is required");
      const response = await api.get(`/api/stores/${storeId}/airbyte-status`);
      return response.data;
    },
    enabled: !!storeId,
    retry: 2,
    retryDelay: 1000,
    refetchInterval: pollingFrequency * 1000, // Use user preference for polling
  });

  // Update sync progress and history
  useEffect(() => {
    if (syncProgressData) {
      setSyncProgress(syncProgressData);

      // Update sync status based on progress
      if (syncProgressData.status === "running") {
        setSyncStatus((prev) => ({
          ...prev,
          products: {
            status: "syncing",
            lastSync: `Processing ${syncProgressData.processed_items}/${syncProgressData.total_items} items`,
          },
        }));

        // Add to progress history for rolling updates
        const progressMessage = `${syncProgressData.processed_items} products synced`;
        setProgressHistory((prev) => {
          const newHistory = [progressMessage, ...prev.slice(0, 4)]; // Keep last 5 updates
          return newHistory;
        });
      } else if (syncProgressData.status === "completed") {
        setSyncStatus((prev) => ({
          ...prev,
          products: {
            status: "success",
            lastSync: `Completed at ${new Date(syncProgressData.completed_at || "").toLocaleTimeString()}`,
          },
        }));
      } else if (syncProgressData.status === "failed") {
        setSyncStatus((prev) => ({
          ...prev,
          products: {
            status: "error",
            lastSync: `Failed: ${syncProgressData.error_message || "Unknown error"}`,
          },
        }));
      }
    }
  }, [syncProgressData]);

  // Update disconnection status based on store details
  useEffect(() => {
    if (storeDetails) {
      setIsDisconnected(!storeDetails.is_active);
    }
  }, [storeDetails]);

  const storeName =
    storeDetails?.shop_name || storeDetails?.shop_domain || "Unnamed Store";

  const queryClient = useQueryClient();

  const syncMutation = useMutation({
    mutationFn: async ({
      syncType,
      mode,
    }: {
      syncType: string;
      mode: "full" | "incremental";
    }) => {
      if (!storeId) throw new Error("Store ID is required");

      const response = await api.post(`/api/stores/${storeId}/sync/products`, {
        mode: mode,
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      const modeText = variables.mode === "full" ? "Full" : "Incremental";
      toast.success(`${modeText} product sync job has been queued`);
      // Update sync status to show it's in progress
      setSyncStatus((prev) => ({
        ...prev,
        products: { status: "syncing", lastSync: "Starting..." },
      }));
      // Clear previous progress history
      setProgressHistory([]);
      // Trigger immediate progress fetch
      refetchProgress();
    },
    onError: (error) => {
      console.error("Failed to start sync:", error);
      toast.error("Failed to start sync job");
    },
  });

  const testConnectionMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error("Store ID is required");

      const response = await api.post(`/api/stores/${storeId}/test-connection`);
      return response.data;
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(`Connection test successful: ${data.message}`);
      } else {
        toast.error(`Connection test failed: ${data.message}`);
      }
    },
    onError: (error: any) => {
      console.error("Failed to test connection:", error);
      toast.error(
        `Connection test failed: ${error?.response?.data?.detail || "Unknown error"}`
      );
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error("Store ID is required");

      const response = await api.delete(`/api/stores/${storeId}`);
      return response.data;
    },
    onSuccess: () => {
      toast.success("Store deleted successfully");
      // Redirect to stores list
      window.location.href = "/stores";
    },
    onError: (error: any) => {
      console.error("Failed to delete store:", error);
      toast.error(
        `Failed to delete store: ${error?.response?.data?.detail || "Unknown error"}`
      );
    },
  });

  const disconnectMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error("Store ID is required");

      const response = await api.post(`/api/stores/${storeId}/disconnect`);
      return response.data;
    },
    onSuccess: () => {
      setIsDisconnected(true);
      toast.success("Store disconnected successfully");
      queryClient.invalidateQueries({ queryKey: ["store-details", storeId] });
      // After disconnecting, redirect to connect page
      setTimeout(() => {
        if (storeDetails?.external_id) {
          window.location.href = `/stores/connect?storeId=${storeDetails.external_id}`;
        } else {
          window.location.href = "/stores/connect";
        }
      }, 1000);
    },
    onError: (error: any) => {
      console.error("Failed to disconnect store:", error);
      toast.error(
        `Failed to disconnect store: ${error?.response?.data?.detail || "Unknown error"}`
      );
    },
  });

  const enableSyncMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error("Store ID is required");

      const response = await api.post(`/api/stores/${storeId}/enable-sync`);
      return response.data;
    },
    onSuccess: () => {
      toast.success("Sync operations enabled successfully");
      // Refresh Airbyte status to show updated state
      refetchAirbyteStatus();
    },
    onError: (error: any) => {
      console.error("Failed to enable sync:", error);
      toast.error(
        `Failed to enable sync: ${error?.response?.data?.detail || "Unknown error"}`
      );
    },
  });

  const disableSyncMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error("Store ID is required");

      const response = await api.post(`/api/stores/${storeId}/disable-sync`);
      return response.data;
    },
    onSuccess: () => {
      toast.success("Sync operations disabled successfully");
      // Refresh Airbyte status to show updated state
      refetchAirbyteStatus();
    },
    onError: (error: any) => {
      console.error("Failed to disable sync:", error);
      toast.error(
        `Failed to disable sync: ${error?.response?.data?.detail || "Unknown error"}`
      );
    },
  });

  if (isLoadingStoreDetails) {
    return (
      <div className="space-y-8">
        {/* Loading Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-64 mt-2 animate-pulse"></div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 bg-gray-300 rounded-full animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>
          <div className="h-24 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>

        {/* Loading Content */}
        <div className="grid gap-6 md:grid-cols-2">
          <div className="h-48 bg-gray-100 rounded-lg animate-pulse"></div>
          <div className="h-48 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>

        <div className="h-96 bg-gray-100 rounded-lg animate-pulse"></div>
      </div>
    );
  }

  if (!storeId) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-red-600 mb-4">⚠️ Store ID is required</div>
            <p className="text-muted-foreground">
              Please navigate to this page from the stores list.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show errors if any queries failed
  const hasErrors = storeDetailsError;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Header Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
            >
              ← Back to Stores
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Store Settings</h1>
              <p className="text-sm text-muted-foreground">
                {storeDetails?.shop_name ||
                  storeDetails?.shop_domain ||
                  "Unnamed Store"}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div
                className={`h-3 w-3 rounded-full ${
                  hasErrors
                    ? "bg-red-500 animate-pulse"
                    : airbyteStatus?.airbyte_service?.service_health ===
                        "unhealthy"
                      ? "bg-red-500"
                      : airbyteStatus?.connection?.status === "inactive"
                        ? "bg-orange-500"
                        : isDisconnected
                          ? "bg-orange-500"
                          : airbyteStatus?.connection?.status === "active"
                            ? "bg-green-500"
                            : "bg-green-500"
                }`}
              ></div>
              <span className="text-sm text-muted-foreground">
                {hasErrors
                  ? "Issues Detected"
                  : airbyteStatus?.airbyte_service?.service_health ===
                      "unhealthy"
                    ? "Airbyte Service Down"
                    : airbyteStatus?.connection?.status === "inactive"
                      ? "Connection Inactive"
                      : isDisconnected
                        ? "Store Disconnected"
                        : airbyteStatus?.connection?.status === "active"
                          ? "Fully Connected"
                          : "Connected"}
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchAirbyteStatus()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Options
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64">
                <DropdownMenuLabel>User Preferences</DropdownMenuLabel>
                <DropdownMenuSeparator />

                <div className="p-3 space-y-4">
                  <div>
                    <Label className="text-sm font-medium">
                      Polling Frequency
                    </Label>
                    <div className="flex items-center space-x-2 mt-2">
                      <Slider
                        value={[pollingFrequency]}
                        onValueChange={(value) => setPollingFrequency(value[0])}
                        max={300}
                        min={5}
                        step={5}
                        className="flex-1"
                      />
                      <span className="text-xs text-muted-foreground w-12">
                        {pollingFrequency}s
                      </span>
                    </div>
                  </div>

                  <DropdownMenuCheckboxItem
                    checked={notificationsEnabled}
                    onCheckedChange={setNotificationsEnabled}
                  >
                    <Bell className="h-4 w-4 mr-2" />
                    Enable Notifications
                  </DropdownMenuCheckboxItem>

                  <DropdownMenuCheckboxItem
                    checked={compactView}
                    onCheckedChange={setCompactView}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Compact View
                  </DropdownMenuCheckboxItem>
                </div>

                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Download className="h-4 w-4 mr-2" />
                  Export Logs
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {hasErrors && (
        <div className="mx-6 mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <div className="text-red-600">⚠️</div>
            <div>
              <h4 className="font-medium text-red-900">
                Failed to load some data
              </h4>
              <p className="text-sm text-red-700">
                {storeDetailsError && "Store details could not be loaded. "}
                Please try refreshing the page.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="px-6 py-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column: Status Overview */}
        <div className="space-y-6">
          {/* Airbyte Service Status */}
          {airbyteStatus && (
            <Card
              className={`border-2 ${
                airbyteStatus.airbyte_service?.service_health === "healthy"
                  ? "border-green-200 bg-green-50"
                  : "border-red-200 bg-red-50"
              }`}
            >
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div
                      className={`h-3 w-3 rounded-full ${
                        airbyteStatus.airbyte_service?.service_health ===
                        "healthy"
                          ? "bg-green-500"
                          : "bg-red-500"
                      }`}
                    ></div>
                    <span className="text-sm">Airbyte Status</span>
                    {isLoadingAirbyteStatus && (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => refetchAirbyteStatus()}
                    disabled={isLoadingAirbyteStatus}
                  >
                    <RefreshCw
                      className={`h-4 w-4 ${isLoadingAirbyteStatus ? "animate-spin" : ""}`}
                    />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Service Health</span>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        airbyteStatus.airbyte_service?.service_health ===
                        "healthy"
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {airbyteStatus.airbyte_service?.service_health ===
                      "healthy"
                        ? "Healthy"
                        : "Unhealthy"}
                    </span>
                  </div>
                  {airbyteStatus.connection && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Connection</span>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          airbyteStatus.connection.status === "active"
                            ? "bg-green-100 text-green-800"
                            : airbyteStatus.connection.status === "inactive"
                              ? "bg-orange-100 text-orange-800"
                              : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {airbyteStatus.connection.status === "active"
                          ? "Active"
                          : airbyteStatus.connection.status === "inactive"
                            ? "Inactive"
                            : "Unknown"}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Sync Checkpoints */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FolderSyncIcon className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Sync Status</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {syncCheckpoints && syncCheckpoints.length > 0 ? (
                  syncCheckpoints.map((checkpoint: any) => (
                    <div
                      key={checkpoint.id}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center space-x-2">
                        {checkpoint.last_sync_status === "success" ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : checkpoint.last_sync_status === "running" ? (
                          <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
                        ) : checkpoint.last_sync_status === "failed" ? (
                          <Clock className="h-4 w-4 text-red-600" />
                        ) : (
                          <Clock className="h-4 w-4 text-gray-600" />
                        )}
                        <span className="text-sm capitalize">
                          {checkpoint.entity_type}
                        </span>
                      </div>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          checkpoint.last_sync_status === "success"
                            ? "bg-green-100 text-green-800"
                            : checkpoint.last_sync_status === "running"
                              ? "bg-blue-100 text-blue-800"
                              : checkpoint.last_sync_status === "failed"
                                ? "bg-red-100 text-red-800"
                                : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {checkpoint.last_sync_status?.toUpperCase() ||
                          "PENDING"}
                      </span>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground text-sm">
                    {isLoadingCheckpoints ? "Loading..." : "No sync data"}
                  </div>
                )}
              </div>

              {/* Simple Sync Trends Visualization */}
              {syncCheckpoints && syncCheckpoints.length > 0 && (
                <div className="mt-4 pt-4 border-t">
                  <div className="text-xs text-muted-foreground mb-2">
                    Sync Trends (Last 7 days)
                  </div>
                  <div className="flex items-end space-x-1 h-8">
                    {[85, 92, 78, 95, 88, 96, 91].map((height, index) => (
                      <div
                        key={index}
                        className="bg-blue-200 rounded-sm flex-1 transition-all hover:bg-blue-300"
                        style={{ height: `${height}%` }}
                        title={`Day ${index + 1}: ${height}% success rate`}
                      />
                    ))}
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>Mon</span>
                    <span>Today</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Middle Column: Sync Management */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-blue-600" />
                <span>Sync Management</span>
              </CardTitle>
              <CardDescription>
                Trigger and monitor sync operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Sync Buttons */}
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    className="h-12"
                    onClick={() =>
                      syncMutation.mutate({
                        syncType: "products",
                        mode: "incremental",
                      })
                    }
                    disabled={syncMutation.isPending}
                  >
                    {syncMutation.isPending ? (
                      <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                    ) : (
                      <RefreshCw className="h-5 w-5 mr-2" />
                    )}
                    <div className="text-center">
                      <div className="font-medium text-sm">Incremental</div>
                      <div className="text-xs text-muted-foreground">
                        Changes only
                      </div>
                    </div>
                  </Button>

                  <Button
                    variant="outline"
                    className="h-12"
                    onClick={() =>
                      syncMutation.mutate({
                        syncType: "products",
                        mode: "full",
                      })
                    }
                    disabled={syncMutation.isPending}
                  >
                    {syncMutation.isPending ? (
                      <Zap className="h-5 w-5 mr-2 animate-spin" />
                    ) : (
                      <Zap className="h-5 w-5 mr-2" />
                    )}
                    <div className="text-center">
                      <div className="font-medium text-sm">Full Sync</div>
                      <div className="text-xs text-muted-foreground">
                        All data
                      </div>
                    </div>
                  </Button>
                </div>

                {/* Progress Bar */}
                {syncProgress && syncProgress.status === "running" && (
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-blue-900">
                        Sync Progress
                      </span>
                      <span className="text-sm text-blue-700">
                        {syncProgress.processed_items}/
                        {syncProgress.total_items}
                      </span>
                    </div>
                    <div className="text-xs text-blue-600 mt-1">
                      Batch {syncProgress.current_batch}/
                      {syncProgress.total_batches}
                    </div>
                  </div>
                )}

                {/* Progress History */}
                {progressHistory.length > 0 && (
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">
                      Recent Updates
                    </h4>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {progressHistory.map((message, index) => (
                        <div
                          key={index}
                          className="text-xs text-gray-600 flex items-center"
                        >
                          <div className="w-1 h-1 bg-blue-500 rounded-full mr-2"></div>
                          {message}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column: Advanced Controls */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-purple-600" />
                <span>Advanced Controls</span>
              </CardTitle>
              <CardDescription>Management and diagnostic tools</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Connection Test */}
                <div className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3 mb-2">
                    <div
                      className={`h-6 w-6 rounded flex items-center justify-center ${
                        testConnectionMutation.isPending
                          ? "bg-yellow-100"
                          : testConnectionMutation.data?.success
                            ? "bg-green-100"
                            : testConnectionMutation.data?.success === false
                              ? "bg-red-100"
                              : "bg-green-100"
                      }`}
                    >
                      {testConnectionMutation.isPending ? (
                        <Loader2 className="h-3 w-3 text-yellow-600 animate-spin" />
                      ) : testConnectionMutation.data?.success ? (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      ) : testConnectionMutation.data?.success === false ? (
                        <Clock className="h-3 w-3 text-red-600" />
                      ) : (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">Test Connection</h4>
                      <p className="text-xs text-muted-foreground">
                        {testConnectionMutation.isPending
                          ? "Testing..."
                          : testConnectionMutation.data?.success
                            ? "Connection OK"
                            : testConnectionMutation.data?.success === false
                              ? "Connection failed"
                              : "Verify connection"}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => testConnectionMutation.mutate()}
                    disabled={testConnectionMutation.isPending}
                  >
                    {testConnectionMutation.isPending
                      ? "Testing..."
                      : "Test Connection"}
                  </Button>
                </div>

                {/* Enable/Disable Sync */}
                <div className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3 mb-2">
                    <div
                      className={`h-6 w-6 rounded flex items-center justify-center ${
                        enableSyncMutation.isPending ||
                        disableSyncMutation.isPending
                          ? "bg-yellow-100"
                          : airbyteStatus?.connection?.status === "active"
                            ? "bg-green-100"
                            : airbyteStatus?.connection?.status === "inactive"
                              ? "bg-orange-100"
                              : "bg-gray-100"
                      }`}
                    >
                      {enableSyncMutation.isPending ||
                      disableSyncMutation.isPending ? (
                        <Loader2 className="h-3 w-3 text-yellow-600 animate-spin" />
                      ) : airbyteStatus?.connection?.status === "active" ? (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      ) : airbyteStatus?.connection?.status === "inactive" ? (
                        <Clock className="h-3 w-3 text-orange-600" />
                      ) : (
                        <Settings className="h-3 w-3 text-gray-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">
                        {airbyteStatus?.connection?.status === "active"
                          ? "Disable Sync"
                          : "Enable Sync"}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {enableSyncMutation.isPending
                          ? "Enabling..."
                          : disableSyncMutation.isPending
                            ? "Disabling..."
                            : airbyteStatus?.connection?.status === "active"
                              ? "Pause operations"
                              : "Resume operations"}
                      </p>
                    </div>
                  </div>
                  {airbyteStatus?.connection?.status === "active" ? (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => disableSyncMutation.mutate()}
                      disabled={
                        disableSyncMutation.isPending ||
                        enableSyncMutation.isPending
                      }
                    >
                      {disableSyncMutation.isPending
                        ? "Disabling..."
                        : "Disable Sync"}
                    </Button>
                  ) : (
                    <Button
                      variant="default"
                      size="sm"
                      className="w-full"
                      onClick={() => enableSyncMutation.mutate()}
                      disabled={
                        enableSyncMutation.isPending ||
                        disableSyncMutation.isPending
                      }
                    >
                      {enableSyncMutation.isPending
                        ? "Enabling..."
                        : "Enable Sync"}
                    </Button>
                  )}
                </div>

                {/* Reconnect Store */}
                <div className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3 mb-2">
                    <div
                      className={`h-6 w-6 rounded flex items-center justify-center ${
                        isDisconnected ? "bg-red-100" : "bg-blue-100"
                      }`}
                    >
                      {isDisconnected ? (
                        <Clock className="h-3 w-3 text-red-600" />
                      ) : (
                        <RefreshCw className="h-3 w-3 text-blue-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">Reconnect Store</h4>
                      <p className="text-xs text-muted-foreground">
                        {isDisconnected
                          ? "Reconnect disconnected store"
                          : "Refresh connection"}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant={isDisconnected ? "default" : "outline"}
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      if (isDisconnected) {
                        if (storeDetails?.external_id) {
                          window.location.href = `/stores/connect?storeId=${storeDetails.external_id}`;
                        } else {
                          window.location.href = "/stores/connect";
                        }
                      } else {
                        disconnectMutation.mutate();
                      }
                    }}
                    disabled={disconnectMutation.isPending}
                  >
                    {disconnectMutation.isPending
                      ? "Disconnecting..."
                      : "Reconnect Store"}
                  </Button>
                </div>

                {/* Delete Store */}
                <div className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3 mb-2">
                    <div
                      className={`h-6 w-6 rounded flex items-center justify-center ${
                        deleteMutation.isPending
                          ? "bg-yellow-100"
                          : !isDisconnected ||
                              airbyteStatus?.connection?.status === "active"
                            ? "bg-gray-100"
                            : "bg-red-100"
                      }`}
                    >
                      {deleteMutation.isPending ? (
                        <Loader2 className="h-3 w-3 text-yellow-600 animate-spin" />
                      ) : (
                        <Settings
                          className={`h-3 w-3 ${
                            !isDisconnected ||
                            airbyteStatus?.connection?.status === "active"
                              ? "text-gray-400"
                              : "text-red-600"
                          }`}
                        />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4
                        className={`text-sm font-medium ${
                          !isDisconnected ||
                          airbyteStatus?.connection?.status === "active"
                            ? "text-gray-400"
                            : ""
                        }`}
                      >
                        Delete Store
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {deleteMutation.isPending
                          ? "Deleting..."
                          : !isDisconnected
                            ? "Disconnect first"
                            : airbyteStatus?.connection?.status === "active"
                              ? "Disable sync first"
                              : "Permanently remove"}
                      </p>
                    </div>
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="w-full"
                        disabled={
                          deleteMutation.isPending ||
                          !isDisconnected ||
                          airbyteStatus?.connection?.status === "active"
                        }
                      >
                        {deleteMutation.isPending
                          ? "Deleting..."
                          : "Delete Store"}
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Delete Store</DialogTitle>
                        <DialogDescription>
                          This action cannot be undone. All data will be
                          permanently removed.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-2 mt-4">
                        <p className="text-sm">
                          To confirm deletion, please type the store name:{" "}
                          <strong>{storeName}</strong>
                        </p>
                        <Input
                          value={confirmStoreName}
                          onChange={(e) => setConfirmStoreName(e.target.value)}
                          placeholder="Enter store name to confirm"
                        />
                      </div>
                      <DialogFooter>
                        <DialogClose asChild>
                          <Button variant="outline">Cancel</Button>
                        </DialogClose>
                        <DialogClose asChild>
                          <Button
                            variant="destructive"
                            onClick={() => deleteMutation.mutate()}
                            disabled={confirmStoreName !== storeName}
                          >
                            Delete
                          </Button>
                        </DialogClose>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SyncSettings;
