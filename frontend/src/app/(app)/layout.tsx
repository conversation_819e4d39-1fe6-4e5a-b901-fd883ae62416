'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AppShell } from '@/components/layout';
import { Loader2 } from 'lucide-react';
import { LayoutProvider } from '@/contexts/LayoutContext';

// Simple protected route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return <>{children}</>;
};

const getPageTitle = (pathname: string): string => {
  const pathSegments = pathname.split('/').filter(Boolean);

  if (pathSegments.length === 0) return 'Dashboard';

  const lastSegment = pathSegments[pathSegments.length - 1];

  // Convert path segments to readable titles
  const titleMap: Record<string, string> = {
    'dashboard': 'Dashboard',
    'products': 'Products',
    'media-studio': 'Media Studio',
    'context-to-prompt': 'Context to Prompt',
    'scraper': 'Web Scraper',
    'stores': 'Stores',
    'orders': 'Orders',
    'customers': 'Customers',
    'webhook-monitor': 'Webhook Monitor',
    'docs': 'Documentation',
    'support': 'Support',
    'help': 'Help',
    'faq': 'FAQ',
    'contact': 'Contact',
  };

  return titleMap[lastSegment] || lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1).replace('-', ' ');
};

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [pageTitle, setPageTitle] = useState<string>('');

  useEffect(() => {
    setPageTitle(getPageTitle(pathname));
  }, [pathname]);

  return (
    <ProtectedRoute>
      <LayoutProvider>
        <AppShell pageTitle={pageTitle} noContentPadding={pathname.startsWith('/media-studio')}>
          {children}
        </AppShell>
      </LayoutProvider>
    </ProtectedRoute>
  );
}
