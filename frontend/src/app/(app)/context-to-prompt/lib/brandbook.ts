import { BrandInsight, BrandSection, BrandbookData, BrandbookSummaryPoint, ConfidenceLevel } from "./types";

interface ExtractionContext {
  doc: Document;
  html: string;
  finalUrl: string;
  domain: string;
  meta: Map<string, string>;
  jsonLd: any[];
  bodyText: string;
  textTokens: string[];
  navItems: string[];
  headings: string[];
  styleContent: string;
}

const CONFIDENCE_WEIGHTS: Record<ConfidenceLevel, number> = {
  high: 1,
  medium: 0.6,
  low: 0.3,
};

const HEX_COLOR_REGEX = /#(?:[0-9a-fA-F]{3}){1,2}\b/g;
const FONT_FAMILY_REGEX = /font-family\s*:\s*([^;{}]+);/gi;

const SOCIAL_DOMAINS = ["instagram.com", "facebook.com", "tiktok.com", "pinterest.com", "youtube.com", "twitter.com", "x.com", "linkedin.com"];

const INDUSTRY_KEYWORDS: Record<string, string[]> = {
  "Fashion & Apparel": ["fashion", "apparel", "clothing", "couture", "streetwear", "wardrobe", "runway", "lookbook"],
  "Beauty & Wellness": ["skincare", "beauty", "cosmetic", "makeup", "spa", "wellness"],
  "Footwear": ["sneaker", "shoe", "boot", "sandals", "footwear"],
  "Jewelry": ["jewelry", "ring", "necklace", "bracelet", "earring"],
  "Home & Lifestyle": ["home", "decor", "interior", "furniture"],
  "Food & Beverage": ["bistro", "cafe", "restaurant", "recipe", "kitchen", "culinary"],
  "Technology": ["tech", "digital", "software", "platform", "AI", "smart"],
};

const TONE_KEYWORDS: Record<string, string[]> = {
  "Sophisticated & Luxury": ["luxury", "premium", "bespoke", "exclusive", "couture"],
  "Bold & Expressive": ["bold", "edgy", "statement", "fearless", "vibrant"],
  "Minimal & Clean": ["minimal", "clean", "timeless", "essential", "understated"],
  "Playful & Youthful": ["playful", "youthful", "fun", "joyful", "vibrant"],
  "Sustainable & Ethical": ["sustainable", "ethical", "organic", "responsible", "conscious"],
};

const AUDIENCE_KEYWORDS: Record<string, string[]> = {
  "Women": ["women", "woman", "female", "ladies"],
  "Men": ["men", "man", "male", "gentlemen"],
  "Unisex": ["unisex", "all genders", "everyone"],
  "Teens": ["teen", "youth", "students", "young"],
  "Kids": ["kids", "children", "boys", "girls", "youth"],
};

const AGE_RANGE_REGEX = /\b(\d{1,2})\s*[-–to]{1,2}\s*(\d{1,2})\b/g;
const PRICE_REGEX = /\$\s?(\d{2,5})(?:\s?-\s?\$?(\d{2,5}))?/g;

function buildMetaMap(doc: Document): Map<string, string> {
  const map = new Map<string, string>();
  const metaTags = Array.from(doc.querySelectorAll("meta"));
  for (const tag of metaTags) {
    const name = tag.getAttribute("name") || tag.getAttribute("property") || tag.getAttribute("itemprop");
    const content = tag.getAttribute("content");
    if (name && content) {
      map.set(name.toLowerCase(), content.trim());
    }
  }
  return map;
}

function extractJsonLd(doc: Document): any[] {
  const scripts = Array.from(doc.querySelectorAll('script[type="application/ld+json"]'));
  const payloads: any[] = [];

  for (const script of scripts) {
    const raw = script.textContent?.trim();
    if (!raw) continue;
    try {
      const parsed = JSON.parse(raw);
      if (Array.isArray(parsed)) {
        payloads.push(...parsed);
      } else {
        payloads.push(parsed);
      }
    } catch (error) {
      continue;
    }
  }

  return payloads;
}

function getBodyText(doc: Document): string {
  return doc.body?.textContent?.replace(/\s+/g, " ").trim() || "";
}

function getNavItems(doc: Document): string[] {
  const selectors = ["nav a", "header nav a", "header a", ".nav a", ".menu a", ".navigation a"];
  const seen = new Set<string>();
  const items: string[] = [];

  for (const selector of selectors) {
    const nodes = Array.from(doc.querySelectorAll<HTMLAnchorElement>(selector));
    for (const node of nodes) {
      const text = node.textContent?.trim();
      if (text && text.length > 1) {
        const key = text.toLowerCase();
        if (!seen.has(key)) {
          seen.add(key);
          items.push(text);
        }
      }
    }
  }

  return items;
}

function getHeadings(doc: Document): string[] {
  const nodes = Array.from(doc.querySelectorAll("h1, h2, h3"));
  return nodes
    .map((node) => node.textContent?.replace(/\s+/g, " ").trim())
    .filter((value): value is string => Boolean(value));
}

function getStyleContent(doc: Document): string {
  const styleTags = Array.from(doc.querySelectorAll("style"));
  const inlineStyles = Array.from(doc.querySelectorAll("[style]"));

  const chunks: string[] = [];
  for (const tag of styleTags) {
    if (tag.textContent) chunks.push(tag.textContent);
  }
  for (const el of inlineStyles) {
    const style = el.getAttribute("style");
    if (style) chunks.push(style);
  }
  return chunks.join("\n");
}

function tokenize(text: string): string[] {
  return text
    .toLowerCase()
    .split(/[^a-z0-9#]+/)
    .filter(Boolean);
}

function normaliseFontName(font: string): string {
  return font
    .split(",")[0]
    .replace(/['"]/g, "")
    .trim();
}

function buildInsight(insight: BrandInsight): BrandInsight {
  return {
    ...insight,
    value:
      Array.isArray(insight.value) && insight.value.length === 0
        ? null
        : insight.value,
  };
}

function pickBrandName(context: ExtractionContext): BrandInsight {
  const { meta, jsonLd, domain } = context;

  const schemaName = jsonLd
    .map((entry) => entry?.name)
    .find((name: unknown): name is string => typeof name === "string" && name.trim().length > 0);

  if (schemaName) {
    return buildInsight({
      id: "brand-name",
      label: "Brand Name",
      value: schemaName.trim(),
      source: "json-ld name",
      confidence: "high",
    });
  }

  const ogSiteName = meta.get("og:site_name");
  if (ogSiteName) {
    return buildInsight({
      id: "brand-name",
      label: "Brand Name",
      value: ogSiteName,
      source: "meta og:site_name",
      confidence: "medium",
    });
  }

  const title = meta.get("og:title") || context.doc.title;
  if (title) {
    return buildInsight({
      id: "brand-name",
      label: "Brand Name",
      value: title.split("|")[0].trim(),
      source: "page title heuristic",
      confidence: "low",
      notes: "Derived from <title> tag",
    });
  }

  return buildInsight({
    id: "brand-name",
    label: "Brand Name",
    value: domain.split(".")[0].replace(/[-_]/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
    source: "domain fallback",
    confidence: "low",
  });
}

function pickBrandDescription(context: ExtractionContext): BrandInsight {
  const { meta, jsonLd, headings } = context;
  const schemaDescription = jsonLd
    .map((entry) => entry?.description)
    .find((desc: unknown): desc is string => typeof desc === "string" && desc.trim().length > 0);
  if (schemaDescription) {
    return buildInsight({
      id: "brand-description",
      label: "Brand Description",
      value: schemaDescription.trim(),
      source: "json-ld description",
      confidence: "high",
    });
  }

  const metaDescription = meta.get("description") || meta.get("og:description");
  if (metaDescription) {
    return buildInsight({
      id: "brand-description",
      label: "Brand Description",
      value: metaDescription.trim(),
      source: "meta description",
      confidence: "medium",
    });
  }

  const heroHeading = headings[0];
  if (heroHeading) {
    return buildInsight({
      id: "brand-description",
      label: "Brand Description",
      value: heroHeading,
      source: "top heading heuristic",
      confidence: "low",
    });
  }

  return buildInsight({
    id: "brand-description",
    label: "Brand Description",
    value: null,
    source: "not found",
    confidence: "low",
  });
}

function detectIndustry(context: ExtractionContext): BrandInsight {
  const { jsonLd, textTokens } = context;

  const schemaCategory = jsonLd
    .map((entry) => entry?.category || entry?.genre || entry?.["@type"])
    .find((category: unknown): category is string => typeof category === "string" && category.trim().length > 0);

  if (schemaCategory) {
    return buildInsight({
      id: "brand-industry",
      label: "Primary Industry",
      value: schemaCategory,
      source: "json-ld category",
      confidence: "high",
    });
  }

  const keywordScores: Record<string, number> = {};
  for (const [industry, keywords] of Object.entries(INDUSTRY_KEYWORDS)) {
    const score = keywords.reduce((acc, keyword) => (textTokens.includes(keyword) ? acc + 1 : acc), 0);
    if (score > 0) {
      keywordScores[industry] = score;
    }
  }

  const ranked = Object.entries(keywordScores).sort((a, b) => b[1] - a[1]);
  if (ranked.length > 0) {
    return buildInsight({
      id: "brand-industry",
      label: "Primary Industry",
      value: ranked[0][0],
      source: "content keyword analysis",
      confidence: ranked[0][1] > 3 ? "medium" : "low",
    });
  }

  return buildInsight({
    id: "brand-industry",
    label: "Primary Industry",
    value: null,
    source: "not detected",
    confidence: "low",
  });
}

function extractColorPalette(context: ExtractionContext): BrandInsight {
  const { styleContent } = context;
  const matches = styleContent.match(HEX_COLOR_REGEX) || [];
  const counts = new Map<string, number>();

  for (const color of matches) {
    const hex = color.toLowerCase();
    if (["#000", "#000000", "#fff", "#ffffff"].includes(hex)) continue;
    counts.set(hex, (counts.get(hex) || 0) + 1);
  }

  const sorted = Array.from(counts.entries()).sort((a, b) => b[1] - a[1]);
  const topColors = sorted.slice(0, 6).map(([hex]) => hex);

  return buildInsight({
    id: "brand-colors",
    label: "Core Color Palette",
    value: topColors,
    source: "css color analysis",
    confidence: topColors.length >= 3 ? "medium" : "low",
    notes: topColors.length === 0 ? "No distinctive color palette detected" : undefined,
  });
}

function extractFonts(context: ExtractionContext): BrandInsight {
  const { styleContent } = context;
  const fonts: string[] = [];
  let match: RegExpExecArray | null;

  while ((match = FONT_FAMILY_REGEX.exec(styleContent))) {
    const font = normaliseFontName(match[1]);
    if (font && !fonts.includes(font) && !/[(\s]?var\(/i.test(font)) {
      fonts.push(font);
    }
  }

  return buildInsight({
    id: "brand-fonts",
    label: "Typography",
    value: fonts.slice(0, 5),
    source: "css font-family scan",
    confidence: fonts.length > 0 ? "medium" : "low",
  });
}

function extractTone(context: ExtractionContext): BrandInsight {
  const { textTokens } = context;
  const toneScores: Record<string, number> = {};

  for (const [tone, keywords] of Object.entries(TONE_KEYWORDS)) {
    const score = keywords.reduce((acc, keyword) => (textTokens.includes(keyword) ? acc + 1 : acc), 0);
    if (score > 0) toneScores[tone] = score;
  }

  const ranked = Object.entries(toneScores).sort((a, b) => b[1] - a[1]);
  if (ranked.length === 0) {
    return buildInsight({
      id: "brand-tone",
      label: "Brand Tone",
      value: null,
      source: "insufficient cues",
      confidence: "low",
    });
  }

  return buildInsight({
    id: "brand-tone",
    label: "Brand Tone",
    value: ranked.map(([tone]) => tone),
    source: "keyword sentiment analysis",
    confidence: ranked[0][1] > 3 ? "medium" : "low",
  });
}

function extractAudience(context: ExtractionContext): BrandInsight {
  const { jsonLd, textTokens, bodyText } = context;
  const schemaAudience = jsonLd
    .map((entry) => entry?.audience?.name || entry?.audience || entry?.hasOfferCatalog?.audience)
    .find((audience: unknown): audience is string => typeof audience === "string" && audience.trim().length > 0);

  if (schemaAudience) {
    return buildInsight({
      id: "brand-audience",
      label: "Target Audience",
      value: schemaAudience,
      source: "json-ld audience",
      confidence: "high",
    });
  }

  const groups: string[] = [];
  for (const [audience, keywords] of Object.entries(AUDIENCE_KEYWORDS)) {
    if (keywords.some((keyword) => textTokens.includes(keyword))) {
      groups.push(audience);
    }
  }

  const ages: string[] = [];
  let ageMatch: RegExpExecArray | null;
  while ((ageMatch = AGE_RANGE_REGEX.exec(bodyText))) {
    ages.push(`${ageMatch[1]}-${ageMatch[2]}`);
  }

  const summary: string[] = [];
  if (groups.length > 0) summary.push(groups.join(", "));
  if (ages.length > 0) summary.push(`Age ranges observed: ${Array.from(new Set(ages)).slice(0, 3).join(", ")}`);

  if (summary.length === 0) {
    return buildInsight({
      id: "brand-audience",
      label: "Target Audience",
      value: null,
      source: "insufficient cues",
      confidence: "low",
    });
  }

  return buildInsight({
    id: "brand-audience",
    label: "Target Audience",
    value: summary,
    source: "content analysis",
    confidence: groups.length >= 2 ? "medium" : "low",
  });
}

function extractPricePositioning(context: ExtractionContext): BrandInsight {
  const { bodyText } = context;
  const prices: string[] = [];
  let match: RegExpExecArray | null;

  while ((match = PRICE_REGEX.exec(bodyText))) {
    const [, low, high] = match;
    if (low && high) {
      prices.push(`$${low}-${high}`);
    } else if (low) {
      prices.push(`$${low}`);
    }
  }

  const unique = Array.from(new Set(prices));
  if (unique.length === 0) {
    return buildInsight({
      id: "brand-price",
      label: "Price Positioning",
      value: null,
      source: "prices not detected",
      confidence: "low",
    });
  }

  return buildInsight({
    id: "brand-price",
    label: "Price Positioning",
    value: unique.slice(0, 5),
    source: "price pattern scan",
    confidence: unique.length >= 2 ? "medium" : "low",
  });
}

function extractSocialPresence(context: ExtractionContext): BrandInsight {
  const { doc } = context;
  const links = Array.from(doc.querySelectorAll<HTMLAnchorElement>("a[href]"));
  const socials = new Map<string, string>();

  for (const link of links) {
    const href = link.href;
    if (!href) continue;
    for (const domain of SOCIAL_DOMAINS) {
      if (href.includes(domain)) {
        socials.set(domain, href);
      }
    }
  }

  return buildInsight({
    id: "brand-social",
    label: "Social Presence",
    value: Array.from(socials.values()),
    source: "link discovery",
    confidence: socials.size > 0 ? "medium" : "low",
  });
}

function extractNavigation(context: ExtractionContext): BrandInsight {
  const { navItems } = context;
  return buildInsight({
    id: "brand-navigation",
    label: "Key Navigation",
    value: navItems.slice(0, 12),
    source: "navigation scan",
    confidence: navItems.length > 0 ? "medium" : "low",
  });
}

function extractHeroImagery(context: ExtractionContext): BrandInsight {
  const { doc } = context;
  const images = Array.from(doc.querySelectorAll<HTMLImageElement>("img[alt]")).slice(0, 12);
  const descriptors = images
    .map((img) => img.alt?.trim())
    .filter((alt): alt is string => Boolean(alt) && alt.length > 3);

  return buildInsight({
    id: "brand-imagery",
    label: "Imagery Themes",
    value: descriptors.slice(0, 8),
    source: "image alt analysis",
    confidence: descriptors.length >= 4 ? "medium" : "low",
  });
}

function summarizeBrandbook(sections: BrandSection[]): BrandbookSummaryPoint[] {
  const points: BrandbookSummaryPoint[] = [];
  const essentials = sections.find((section) => section.id === "essentials");
  const visual = sections.find((section) => section.id === "visual");
  const audience = sections.find((section) => section.id === "audience");

  const name = essentials?.insights.find((insight) => insight.id === "brand-name");
  const industry = essentials?.insights.find((insight) => insight.id === "brand-industry");
  const tone = visual?.insights.find((insight) => insight.id === "brand-tone");
  const colors = visual?.insights.find((insight) => insight.id === "brand-colors");
  const audienceInsight = audience?.insights.find((insight) => insight.id === "brand-audience");

  if (name?.value && industry?.value) {
    points.push({
      id: "summary-positioning",
      text: `${name.value} operates within ${industry.value}.`,
    });
  }

  if (tone?.value) {
    points.push({
      id: "summary-tone",
      text: `Brand tone leans towards ${(Array.isArray(tone.value) ? tone.value.join(" / ") : tone.value).toLowerCase()}.`,
    });
  }

  if (Array.isArray(colors?.value) && colors.value.length > 0) {
    points.push({
      id: "summary-colors",
      text: `Palette highlights: ${colors.value.join(", ")}.`,
    });
  }

  if (audienceInsight?.value) {
    points.push({
      id: "summary-audience",
      text: `Target audience: ${Array.isArray(audienceInsight.value) ? audienceInsight.value.join("; ") : audienceInsight.value}.`,
    });
  }

  return points;
}

function computeMetrics(sections: BrandSection[]): { metrics: BrandbookData["metrics"]; } {
  const insights = sections.flatMap((section) => section.insights);
  const totalInsights = insights.length;
  const insightsWithValue = insights.filter((insight) => {
    if (insight.value === null) return false;
    if (Array.isArray(insight.value)) return insight.value.length > 0;
    return String(insight.value).trim().length > 0;
  }).length;
  const highConfidenceCount = insights.filter((insight) => insight.confidence === "high").length;

  const weighted = insights.reduce((acc, insight) => acc + CONFIDENCE_WEIGHTS[insight.confidence], 0);
  const confidenceScore = totalInsights > 0 ? weighted / totalInsights : 0;
  const coverage = totalInsights > 0 ? insightsWithValue / totalInsights : 0;

  return {
    metrics: {
      totalInsights,
      insightsWithValue,
      highConfidenceCount,
      coverage,
      confidenceScore,
    },
  };
}

export function buildBrandbook(html: string, finalUrl: string): BrandbookData {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");
  const meta = buildMetaMap(doc);
  const jsonLd = extractJsonLd(doc);
  const bodyText = getBodyText(doc);
  const navItems = getNavItems(doc);
  const headings = getHeadings(doc);
  const styleContent = getStyleContent(doc);
  const textTokens = tokenize(`${doc.title} ${bodyText}`);
  const domain = (() => {
    try {
      return new URL(finalUrl).hostname;
    } catch (error) {
      return finalUrl;
    }
  })();

  const context: ExtractionContext = {
    doc,
    html,
    finalUrl,
    domain,
    meta,
    jsonLd,
    bodyText,
    textTokens,
    navItems,
    headings,
    styleContent,
  };

  const essentials: BrandSection = {
    id: "essentials",
    title: "Brand Essentials",
    description: "Core identity elements discovered across meta tags, structured data, and site copy.",
    insights: [
      pickBrandName(context),
      buildInsight({
        id: "brand-domain",
        label: "Official Domain",
        value: context.domain,
        source: "scrape target",
        confidence: "high",
      }),
      pickBrandDescription(context),
      detectIndustry(context),
    ],
  };

  const visual: BrandSection = {
    id: "visual",
    title: "Visual Identity",
    description: "Observed color palettes, typography, and imagery themes.",
    insights: [
      extractColorPalette(context),
      extractFonts(context),
      extractTone(context),
      extractHeroImagery(context),
    ],
  };

  const audience: BrandSection = {
    id: "audience",
    title: "Audience & Positioning",
    description: "Signals about customer focus, pricing, and platform presence.",
    insights: [
      extractAudience(context),
      extractPricePositioning(context),
      extractSocialPresence(context),
      extractNavigation(context),
    ],
  };

  const sections: BrandSection[] = [essentials, visual, audience];
  const { metrics } = computeMetrics(sections);
  const summary = summarizeBrandbook(sections);

  return {
    domain,
    fetchedUrl: finalUrl,
    generatedAt: new Date().toISOString(),
    metrics,
    summary,
    sections,
  };
}
