export type ConfidenceLevel = "high" | "medium" | "low";

export interface BrandInsight {
  id: string;
  label: string;
  value: string | string[] | null;
  confidence: ConfidenceLevel;
  source: string;
  evidence?: string;
  notes?: string;
}

export interface BrandSection {
  id: string;
  title: string;
  description?: string;
  insights: BrandInsight[];
}

export interface BrandbookMetrics {
  totalInsights: number;
  insightsWithValue: number;
  highConfidenceCount: number;
  coverage: number; // 0-1
  confidenceScore: number; // 0-1 weighted score
}

export interface BrandbookSummaryPoint {
  id: string;
  text: string;
}

export interface BrandbookData {
  domain: string;
  fetchedUrl: string;
  generatedAt: string;
  metrics: BrandbookMetrics;
  summary: BrandbookSummaryPoint[];
  sections: BrandSection[];
}

export interface ScrapeReport {
  brandbook: BrandbookData;
  rawHtmlSample?: string;
}
