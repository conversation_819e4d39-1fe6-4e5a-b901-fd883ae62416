"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Globe, ClipboardList, Bug } from "lucide-react";
import { toast } from "sonner";
import { BrandbookView } from "./components/BrandbookView";
import { buildBrandbook } from "./lib/brandbook";
import { BrandbookData } from "./lib/types";

interface ScrapeResponse {
  html: string;
  finalUrl: string;
}

const ContextToPromptPage: React.FC = () => {
  const [webshopUrl, setWebshopUrl] = useState("");
  const [isScraping, setIsScraping] = useState(false);
  const [brandbook, setBrandbook] = useState<BrandbookData | null>(null);
  const [rawSample, setRawSample] = useState<string>("");
  const [lastError, setLastError] = useState<string | null>(null);

  const handleScrape = async () => {
    if (!webshopUrl.trim()) {
      toast.error("Please enter a webshop URL");
      return;
    }

    setIsScraping(true);
    setLastError(null);

    try {
      const target = webshopUrl.trim();
      const response = await fetch("/context-to-prompt/api/scrape", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ url: target }),
      });

      if (!response.ok) {
        const payload = await response.json().catch(() => ({}));
        const message = payload?.error || `Failed to scrape site (status ${response.status})`;
        throw new Error(message);
      }

      const payload: ScrapeResponse = await response.json();
      if (!payload?.html) {
        throw new Error("No HTML returned from scraper");
      }

      const brandbookData = buildBrandbook(payload.html, payload.finalUrl);
      setBrandbook(brandbookData);
      setRawSample(payload.html.slice(0, 2000));

      toast.success("Brand context extracted successfully");
    } catch (error: any) {
      console.error("[context-to-prompt] scraping error", error);
      const message = error?.message || "Unexpected error while scraping";
      setLastError(message);
      toast.error(message);
      setBrandbook(null);
    } finally {
      setIsScraping(false);
    }
  };

  return (
    <div className="flex flex-col bg-background text-foreground h-full min-h-0">
      <main className="flex flex-1 min-h-0 overflow-hidden">
        {/* Left Panel */}
        <div className="w-full lg:w-1/3 xl:w-2/5 flex flex-col border-r border-[#EDEDED] dark:border-border bg-background min-h-0">
          <div className="flex-shrink-0 h-12 border-b border-[#EDEDED] dark:border-border bg-card">
            <div className="h-full flex items-center gap-3 px-4">
              <Globe className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Webshop Scraper</span>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-6 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Scrape Brand Context</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-xs uppercase tracking-wide text-muted-foreground">
                    Webshop URL
                  </label>
                  <Input
                    placeholder="https://example-fashion-store.com"
                    value={webshopUrl}
                    onChange={(event) => setWebshopUrl(event.target.value)}
                    onKeyDown={(event) => {
                      if (event.key === "Enter") {
                        event.preventDefault();
                        handleScrape();
                      }
                    }}
                  />
                </div>
                <Button
                  className="w-full"
                  onClick={handleScrape}
                  disabled={isScraping}
                >
                  {isScraping ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Scraping…
                    </>
                  ) : (
                    <>
                      <ClipboardList className="h-4 w-4 mr-2" />
                      Generate Brandbook
                    </>
                  )}
                </Button>
                <div className="text-xs text-muted-foreground leading-relaxed">
                  The scraper fetches HTML via a server-side proxy, analyzes structured data, meta tags, CSS signals, and copywriting to build a high-confidence brandbook. Keep requests focused on public webshops.
                </div>
              </CardContent>
            </Card>

            {lastError && (
              <Card className="border-red-500/40 bg-red-500/5">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center gap-2 text-red-600 dark:text-red-400">
                    <Bug className="h-4 w-4" />
                    Scrape Failed
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-red-600 dark:text-red-300 leading-relaxed">
                    {lastError}
                  </p>
                </CardContent>
              </Card>
            )}

            {brandbook && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">Snapshot</Badge>
                    Quality Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm text-muted-foreground">
                  <div className="flex items-center justify-between">
                    <span className="uppercase tracking-wide text-xs">Coverage</span>
                    <span className="text-foreground font-medium">
                      {Math.round(brandbook.metrics.coverage * 100)}%
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="uppercase tracking-wide text-xs">High Confidence Fields</span>
                    <span className="text-foreground font-medium">
                      {brandbook.metrics.highConfidenceCount}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="uppercase tracking-wide text-xs">Generated</span>
                    <span>{new Date(brandbook.generatedAt).toLocaleString()}</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {rawSample && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">Debug</Badge>
                    HTML Sample (first 2k chars)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="max-h-48 overflow-y-auto rounded-md bg-muted/40 p-3 text-[11px] leading-4 text-muted-foreground whitespace-pre-wrap">
                    {rawSample}
                  </pre>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Right Panel */}
        <div className="hidden lg:block flex-1 min-w-0 bg-background">
          <div className="flex-shrink-0 h-12 border-b border-[#EDEDED] dark:border-border bg-card">
            <div className="h-full flex items-center gap-3 px-4">
              <ClipboardList className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Structured Brandbook</span>
            </div>
          </div>
          <div className="h-[calc(100%-3rem)] overflow-y-auto p-6">
            <BrandbookView brandbook={brandbook} isLoading={isScraping} />
          </div>
        </div>
      </main>

      {/* Mobile brandbook below controls */}
      <div className="lg:hidden border-t border-[#EDEDED] dark:border-border bg-background">
        <div className="h-12 flex items-center gap-3 px-4 border-b border-[#EDEDED] dark:border-border bg-card">
          <ClipboardList className="h-5 w-5 text-muted-foreground" />
          <span className="font-medium">Structured Brandbook</span>
        </div>
        <div className="p-4">
          <BrandbookView brandbook={brandbook} isLoading={isScraping} />
        </div>
      </div>
    </div>
  );
};

export default ContextToPromptPage;
