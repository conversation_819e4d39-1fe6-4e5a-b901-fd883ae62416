"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { BrandbookData, BrandInsight } from "../lib/types";

const confidenceStyles: Record<BrandInsight["confidence"], string> = {
  high: "bg-emerald-500/10 text-emerald-600 dark:text-emerald-300",
  medium: "bg-amber-500/10 text-amber-600 dark:text-amber-300",
  low: "bg-slate-500/10 text-slate-600 dark:text-slate-300",
};

interface BrandbookViewProps {
  brandbook: BrandbookData | null;
  isLoading: boolean;
}

export const BrandbookView: React.FC<BrandbookViewProps> = ({ brandbook, isLoading }) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-sm">Analyzing brand context…</div>
      </div>
    );
  }

  if (!brandbook) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-sm text-center max-w-xs">
          Scrape a webshop to generate a structured brandbook with confidence ratings and actionable insights.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-col gap-1">
            <span>Brandbook Quality Snapshot</span>
            <span className="text-xs font-normal text-muted-foreground">
              Source: {brandbook.fetchedUrl}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <Metric label="Insights" value={brandbook.metrics.totalInsights} />
            <Metric
              label="Coverage"
              value={`${Math.round(brandbook.metrics.coverage * 100)}%`}
              helper={`${brandbook.metrics.insightsWithValue}/${brandbook.metrics.totalInsights}`}
            />
            <Metric
              label="Confidence"
              value={`${Math.round(brandbook.metrics.confidenceScore * 100)}%`}
              helper={`${brandbook.metrics.highConfidenceCount} high-confidence fields`
                }
            />
          </div>
          {brandbook.summary.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-semibold text-foreground">Highlights</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                {brandbook.summary.map((point) => (
                  <li key={point.id}>• {point.text}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {brandbook.sections.map((section) => (
        <Card key={section.id}>
          <CardHeader>
            <CardTitle className="text-base flex justify-between items-start gap-4">
              <span>{section.title}</span>
              <Badge variant="secondary" className="font-medium">
                {section.insights.filter((insight) => {
                  if (insight.value === null) return false;
                  if (Array.isArray(insight.value)) return insight.value.length > 0;
                  return String(insight.value).trim().length > 0;
                }).length}
                /{section.insights.length} filled
              </Badge>
            </CardTitle>
            {section.description && (
              <p className="text-sm text-muted-foreground leading-relaxed">
                {section.description}
              </p>
            )}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {section.insights.map((insight, index) => (
                <React.Fragment key={insight.id}>
                  {index > 0 && <Separator className="opacity-60" />}
                  <div className="grid gap-1">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-sm text-foreground">
                        {insight.label}
                      </span>
                      <Badge className={`text-xs ${confidenceStyles[insight.confidence]}`}>
                        {insight.confidence.toUpperCase()} CONFIDENCE
                      </Badge>
                    </div>
                    {renderValue(insight.value)}
                    <div className="flex flex-wrap items-center gap-2 text-xs text-muted-foreground">
                      <span className="uppercase tracking-wide">Source:</span>
                      <span>{insight.source}</span>
                      {insight.notes && <span className="text-muted-foreground/80">• {insight.notes}</span>}
                    </div>
                  </div>
                </React.Fragment>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

const Metric: React.FC<{ label: string; value: string | number; helper?: string }> = ({ label, value, helper }) => (
  <div className="rounded-xl border border-border/60 bg-muted/40 px-4 py-3">
    <div className="text-xs uppercase tracking-wide text-muted-foreground">{label}</div>
    <div className="text-xl font-semibold text-foreground">{value}</div>
    {helper && <div className="text-xs text-muted-foreground mt-1">{helper}</div>}
  </div>
);

function renderValue(value: BrandInsight["value"]) {
  if (value === null) {
    return <span className="text-sm text-muted-foreground">Not detected</span>;
  }

  if (Array.isArray(value)) {
    return (
      <div className="flex flex-wrap gap-1">
        {value.map((item, index) => (
          <Badge key={`${item}-${index}`} variant="outline" className="text-xs">
            {item}
          </Badge>
        ))}
      </div>
    );
  }

  return <p className="text-sm text-foreground leading-relaxed whitespace-pre-wrap">{value}</p>;
}
