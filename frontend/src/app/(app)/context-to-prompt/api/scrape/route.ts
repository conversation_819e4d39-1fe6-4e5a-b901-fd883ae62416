import { NextRequest, NextResponse } from "next/server";

const USER_AGENT =
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";

const ALLOWED_PROTOCOLS = new Set(["http:", "https:"]);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const targetUrl = typeof body?.url === "string" ? body.url.trim() : "";

    if (!targetUrl) {
      return NextResponse.json(
        { error: "Missing url in request body" },
        { status: 400 }
      );
    }

    let parsedUrl: URL;
    try {
      parsedUrl = new URL(targetUrl.startsWith("http") ? targetUrl : `https://${targetUrl}`);
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid URL provided" },
        { status: 400 }
      );
    }

    if (!ALLOWED_PROTOCOLS.has(parsedUrl.protocol)) {
      return NextResponse.json(
        { error: "Only http and https protocols are supported" },
        { status: 400 }
      );
    }

    const response = await fetch(parsedUrl.toString(), {
      headers: {
        "User-Agent": USER_AGENT,
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9",
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        {
          error: `Failed to fetch URL (status ${response.status})`,
          status: response.status,
        },
        { status: 502 }
      );
    }

    const html = await response.text();

    return NextResponse.json({
      html,
      finalUrl: response.url || parsedUrl.toString(),
      status: response.status,
    });
  } catch (error) {
    console.error("[context-to-prompt] scrape error", error);
    return NextResponse.json(
      { error: "Unexpected error occurred while scraping" },
      { status: 500 }
    );
  }
}
