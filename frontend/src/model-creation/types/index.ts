// Body measurements in centimeters (ISO 8559-1 names)
export interface BodyMeasurements {
  stature: number; // Height
  chest_circumference: number;
  waist_circumference: number;
  hip_circumference: number;
  inseam_length: number;
  sleeve_length_crown_to_wrist: number;
  neck_circumference: number;
  shoulder_width: number;
  arm_length: number;
  thigh_circumference: number;
  calf_circumference: number;
}

// Input sizing systems
export type SizingSystem =
  | "cm" // Direct centimeters
  | "en13402" // European numeric/letter (38C, 40D)
  | "astm_misses" // US Misses sizes
  | "astm_petite" // US Petite sizes
  | "astm_plus" // US Plus sizes
  | "astm_men" // US Men's sizes
  | "jeans_wl" // Jeans Waist/Length (W34 L38)
  | "neck_sleeve"; // Neck × Sleeve (15.5×34)

export interface SizeInput {
  system: SizingSystem;
  label: string; // e.g., "W34 L38", "38C", "8"
  overrides?: Partial<BodyMeasurements>; // Manual overrides
}

export interface ModelIdentity {
  gender: "woman" | "man";
  age_bracket: "18-24" | "25-34" | "35-44" | "45-54" | "55+";
  mst: number; // 1-10 scale (Muscle-Structure-Tone)
  body_shape: "straight" | "athletic" | "curve" | "slim_thick";
  hair_summary: string; // e.g., "shoulder-length brown hair"
}

export interface GenerationRequest {
  measurements: BodyMeasurements;
  identity: ModelIdentity;
  angles: string[]; // ["front", "45-left", "side", "45-right", "back"]
  referenceImages?: string[]; // Optional reference image URLs
}

export interface GeneratedModel {
  id: string;
  angles: Record<string, string>; // angle -> image URL
  measurements: BodyMeasurements;
  identity: ModelIdentity;
  generatedAt: string;
  metadata: {
    ai_generated: boolean;
    model_version: string;
    prompt_used: string;
  };
}

export interface GenerationStatus {
  isGenerating: boolean;
  progress: number;
  currentAngle?: string;
  error?: string;
}

// UI state
export interface UIState {
  containerWidth: number;
  isCompact: boolean;
  activePanel: "input" | "measurements" | "identity" | "generation" | "results";
}

// Storage interface
export interface ModelStorage {
  saveModel(model: GeneratedModel): Promise<string>;
  getModels(): Promise<GeneratedModel[]>;
  deleteModel(id: string): Promise<void>;
  saveAsset(filename: string, data: Blob): Promise<string>;
}