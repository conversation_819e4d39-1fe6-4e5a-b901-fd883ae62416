"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ModelIdentity } from "../types";

interface IdentityPanelProps {
  identity: ModelIdentity;
  onChange: (identity: ModelIdentity) => void;
  disabled?: boolean;
}

const GENDER_OPTIONS = [
  { value: "woman", label: "Woman" },
  { value: "man", label: "Man" },
] as const;

const AGE_BRACKETS = [
  { value: "18-24", label: "18-24" },
  { value: "25-34", label: "25-34" },
  { value: "35-44", label: "35-44" },
  { value: "45-54", label: "45-54" },
  { value: "55+", label: "55+" },
] as const;

const BODY_SHAPES = [
  { value: "straight", label: "Straight", description: "Balanced proportions" },
  { value: "athletic", label: "Athletic", description: "Muscular build" },
  { value: "curve", label: "Curvy", description: "Fuller figure" },
  { value: "slim_thick", label: "Slim Thick", description: "Slim waist, fuller hips" },
] as const;

const HAIR_PRESETS = [
  "shoulder-length brown hair",
  "long blonde hair",
  "short black hair",
  "curly red hair",
  "pixie cut blonde",
  "long wavy black hair",
  "medium brown hair with highlights",
  "short gray hair",
  "long straight black hair",
  "bob cut brown hair",
];

export function IdentityPanel({ identity, onChange, disabled }: IdentityPanelProps) {
  const handleGenderChange = (gender: "woman" | "man") => {
    onChange({ ...identity, gender });
  };

  const handleAgeChange = (age_bracket: ModelIdentity["age_bracket"]) => {
    onChange({ ...identity, age_bracket });
  };

  const handleMSTChange = (mst: number[]) => {
    onChange({ ...identity, mst: mst[0] });
  };

  const handleBodyShapeChange = (body_shape: ModelIdentity["body_shape"]) => {
    onChange({ ...identity, body_shape });
  };

  const handleHairChange = (hair_summary: string) => {
    onChange({ ...identity, hair_summary });
  };

  const getMSTDescription = (mst: number): string => {
    if (mst <= 2) return "Very slim, minimal muscle definition";
    if (mst <= 4) return "Slim, toned appearance";
    if (mst <= 6) return "Average build, moderate tone";
    if (mst <= 8) return "Athletic, well-defined muscles";
    return "Very muscular, high definition";
  };

  return (
    <div className="space-y-6">
      {/* Gender Selection */}
      <div className="space-y-2">
        <Label>Gender</Label>
        <Select
          value={identity.gender}
          onValueChange={handleGenderChange}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {GENDER_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Age Bracket */}
      <div className="space-y-2">
        <Label>Age Bracket</Label>
        <Select
          value={identity.age_bracket}
          onValueChange={handleAgeChange}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {AGE_BRACKETS.map((bracket) => (
              <SelectItem key={bracket.value} value={bracket.value}>
                {bracket.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Muscle-Structure-Tone (MST) */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label>Muscle & Tone (MST)</Label>
          <Badge variant="secondary">{identity.mst}/10</Badge>
        </div>
        <Slider
          value={[identity.mst]}
          onValueChange={handleMSTChange}
          max={10}
          min={1}
          step={1}
          disabled={disabled}
          className="w-full"
        />
        <p className="text-sm text-muted-foreground">
          {getMSTDescription(identity.mst)}
        </p>
      </div>

      {/* Body Shape */}
      <div className="space-y-3">
        <Label>Body Shape</Label>
        <div className="grid grid-cols-2 gap-2">
          {BODY_SHAPES.map((shape) => (
            <Card
              key={shape.value}
              className={`cursor-pointer transition-all ${
                identity.body_shape === shape.value
                  ? "ring-2 ring-primary bg-primary/5"
                  : "hover:bg-muted/50"
              }`}
              onClick={() => !disabled && handleBodyShapeChange(shape.value)}
            >
              <CardContent className="p-3">
                <div className="font-medium text-sm">{shape.label}</div>
                <div className="text-xs text-muted-foreground mt-1">
                  {shape.description}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Hair Summary */}
      <div className="space-y-3">
        <Label>Hair Style</Label>

        {/* Preset Options */}
        <div className="grid grid-cols-2 gap-2 mb-3">
          {HAIR_PRESETS.slice(0, 6).map((preset) => (
            <Button
              key={preset}
              variant={identity.hair_summary === preset ? "default" : "outline"}
              size="sm"
              onClick={() => handleHairChange(preset)}
              disabled={disabled}
              className="text-xs h-auto py-2 px-3 whitespace-normal text-left justify-start"
            >
              {preset}
            </Button>
          ))}
        </div>

        {/* Custom Input */}
        <div className="space-y-2">
          <Label htmlFor="hair-custom" className="text-sm">Custom Hair Description</Label>
          <Input
            id="hair-custom"
            value={identity.hair_summary}
            onChange={(e) => handleHairChange(e.target.value)}
            placeholder="Describe the hair style..."
            disabled={disabled}
            className="text-sm"
          />
        </div>
      </div>

      {/* Identity Summary */}
      <Card className="bg-muted/50">
        <CardContent className="pt-4">
          <h4 className="font-medium text-sm mb-2">Identity Summary</h4>
          <div className="text-sm space-y-1">
            <div><strong>Gender:</strong> {identity.gender}</div>
            <div><strong>Age:</strong> {identity.age_bracket}</div>
            <div><strong>Build:</strong> {identity.body_shape} (MST: {identity.mst}/10)</div>
            <div><strong>Hair:</strong> {identity.hair_summary}</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}