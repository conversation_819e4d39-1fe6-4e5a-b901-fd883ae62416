"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Eye, Copy, Download } from "lucide-react";
import { toast } from "sonner";

interface PersonModelOptions {
  // PROFILE
  presentation: string;
  age_years: number;
  skin_tone: string;
  undertone: string;
  pose: string;

  // MEASUREMENTS
  chest_cm: number;
  bust_cm: number;
  underbust_cm: number;
  waist_cm: number;
  hip_cm: number;
  shoulder_width_cm: number;
  sleeve_length_cm: number;
  back_length_cm: number;
  neck_circ_cm: number;
  inseam_cm: number;
  thigh_circ_cm: number;
  calf_circ_cm: number;
  head_circ_cm: number;
  hand_length_cm: number;
  weight_kg: number;

  // DERIVED PROPORTIONS
  torso_vs_leg: string;
  shoulder_slope: string;
  posture: string;
  waist_indentation: string;

  // BODY BUILD & SILHOUETTE
  build_intensity: string;
  silhouette_distribution: string;
  limb_emphasis: string[];

  // BREAST SPEC
  cup: string;
  band_cm: number;
  projection: string;
  ptosis_grade: string;
  upper_pole_fullness: string;
  spacing: string;
  shape: string;
  support_state: string;

  // SEAT / GLUTE SPEC
  seat_size_bucket: string;
  glute_projection: string;
  seat_shape: string;
  hip_dips: string;

  // FACIAL FEATURES
  eye_color: string;
  eye_shape: string;
  brow_style: string;
  lip_fullness: string;
  cheekbone_definition: string;
  jawline: string;

  // HAIR
  hair_length: string;
  hair_texture: string;
  hair_style: string[];
  hair_color_base: string;
  hair_color_effects: string[];
  hair_density: string;

  // FACIAL HAIR
  facial_hair: string;
  mustache_style: string;
  beard_density: string;
  beard_edge_grooming: string;

  // MAKEUP
  overall_look: string;
  eyes: string[];
  brows: string[];
  lips: string[];
  complexion_notes: string[];

  // SKIN FEATURES
  freckles: string;
  moles_birthmarks: string;
  tattoos_coverage: string;
  tattoos_locations: string[];
  piercings: string[];
  scar_visibility: string;

  // EYEWEAR
  optical_frame_presence: string;
  optical_frame_style: string;
  frame_shape: string;
  frame_fit: string;
  sun_lens: string;
}

const defaultOptions: PersonModelOptions = {
  presentation: "female",
  age_years: 25,
  skin_tone: "medium",
  undertone: "neutral",
  pose: "standing_front",

  chest_cm: 90,
  bust_cm: 90,
  underbust_cm: 75,
  waist_cm: 70,
  hip_cm: 95,
  shoulder_width_cm: 40,
  sleeve_length_cm: 60,
  back_length_cm: 45,
  neck_circ_cm: 35,
  inseam_cm: 75,
  thigh_circ_cm: 55,
  calf_circ_cm: 35,
  head_circ_cm: 55,
  hand_length_cm: 18,
  weight_kg: 65,

  torso_vs_leg: "balanced",
  shoulder_slope: "normal",
  posture: "neutral",
  waist_indentation: "medium",

  build_intensity: "regular",
  silhouette_distribution: "hourglass_balanced",
  limb_emphasis: [],

  cup: "C",
  band_cm: 75,
  projection: "average",
  ptosis_grade: "1_mild",
  upper_pole_fullness: "medium",
  spacing: "average",
  shape: "round",
  support_state: "unsupported",

  seat_size_bucket: "91–95",
  glute_projection: "mild",
  seat_shape: "round_bubble",
  hip_dips: "mild",

  eye_color: "medium_brown",
  eye_shape: "almond",
  brow_style: "natural",
  lip_fullness: "medium",
  cheekbone_definition: "medium",
  jawline: "medium",

  hair_length: "long_mid_back",
  hair_texture: "2B",
  hair_style: [],
  hair_color_base: "medium_brown",
  hair_color_effects: [],
  hair_density: "medium",

  facial_hair: "none",
  mustache_style: "classic",
  beard_density: "medium",
  beard_edge_grooming: "natural",

  overall_look: "natural_matte",
  eyes: [],
  brows: [],
  lips: [],
  complexion_notes: [],

  freckles: "none",
  moles_birthmarks: "none",
  tattoos_coverage: "none",
  tattoos_locations: [],
  piercings: [],
  scar_visibility: "none",

  optical_frame_presence: "none",
  optical_frame_style: "full_rim_medium",
  frame_shape: "rectangle",
  frame_fit: "medium",
  sun_lens: "solid",
};

export function PersonModelGenerator() {
  const [options, setOptions] = useState<PersonModelOptions>(defaultOptions);
  const [generatedPrompt, setGeneratedPrompt] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);

  const updateOption = (key: keyof PersonModelOptions, value: any) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const generatePrompt = () => {
    setIsGenerating(true);

    // Build comprehensive prompt
    const prompt = buildPersonModelPrompt(options);

    setTimeout(() => {
      setGeneratedPrompt(prompt);
      setIsGenerating(false);
      toast.success("Prompt generated successfully!");
    }, 500);
  };

  const copyPrompt = () => {
    navigator.clipboard.writeText(generatedPrompt);
    toast.success("Prompt copied to clipboard!");
  };

  const downloadPrompt = () => {
    const blob = new Blob([generatedPrompt], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "person-model-prompt.txt";
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Prompt downloaded!");
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Person Model Prompt Generator</h1>
        <p className="text-muted-foreground">
          Configure detailed person model specifications and generate AI prompts
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Options Panel */}
        <div className="lg:col-span-2 space-y-6">
          <ScrollArea className="h-[80vh] pr-4">
            <div className="space-y-6">
              {/* PROFILE */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Profile</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Presentation</Label>
                      <Select value={options.presentation} onValueChange={(v) => updateOption("presentation", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">Male</SelectItem>
                          <SelectItem value="female">Female</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Age (years)</Label>
                      <Input
                        type="number"
                        value={options.age_years}
                        onChange={(e) => updateOption("age_years", parseInt(e.target.value))}
                      />
                    </div>
                  </div>

                  <div>
                    <Label>Pose</Label>
                    <Select value={options.pose} onValueChange={(v) => updateOption("pose", v)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="standing_front">Standing Front</SelectItem>
                        <SelectItem value="standing_side">Standing Side</SelectItem>
                        <SelectItem value="standing_back">Standing Back</SelectItem>
                        <SelectItem value="standing_3quarter">Standing 3/4</SelectItem>
                        <SelectItem value="walking_front">Walking Front</SelectItem>
                        <SelectItem value="walking_side">Walking Side</SelectItem>
                        <SelectItem value="sitting_front">Sitting Front</SelectItem>
                        <SelectItem value="sitting_side">Sitting Side</SelectItem>
                        <SelectItem value="lying_front">Lying Front</SelectItem>
                        <SelectItem value="lying_side">Lying Side</SelectItem>
                        <SelectItem value="posing_confident">Confident Pose</SelectItem>
                        <SelectItem value="posing_relaxed">Relaxed Pose</SelectItem>
                        <SelectItem value="posing_dynamic">Dynamic Pose</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Skin Tone</Label>
                      <Select value={options.skin_tone} onValueChange={(v) => updateOption("skin_tone", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {["porcelain", "fair", "light", "light_medium", "medium", "olive_light", "olive_medium", "tan", "deep_tan", "brown", "deep_brown", "very_deep"].map(tone => (
                            <SelectItem key={tone} value={tone}>{tone.replace("_", " ")}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Undertone</Label>
                      <Select value={options.undertone} onValueChange={(v) => updateOption("undertone", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cool">Cool</SelectItem>
                          <SelectItem value="neutral">Neutral</SelectItem>
                          <SelectItem value="warm">Warm</SelectItem>
                          <SelectItem value="olive">Olive</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                </CardContent>
              </Card>


              {/* MEASUREMENTS */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Measurements (cm)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                      <Label>Chest/Bust</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={options.presentation === "female" ? options.bust_cm : options.chest_cm}
                        onChange={(e) => {
                          const val = parseFloat(e.target.value);
                          if (options.presentation === "female") {
                            updateOption("bust_cm", val);
                          } else {
                            updateOption("chest_cm", val);
                          }
                        }}
                      />
                    </div>
                    {options.presentation === "female" && (
                      <div>
                        <Label>Underbust</Label>
                        <Input
                          type="number"
                          step="0.1"
                          value={options.underbust_cm}
                          onChange={(e) => updateOption("underbust_cm", parseFloat(e.target.value))}
                        />
                      </div>
                    )}
                    <div>
                      <Label>Waist</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={options.waist_cm}
                        onChange={(e) => updateOption("waist_cm", parseFloat(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label>Hip</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={options.hip_cm}
                        onChange={(e) => updateOption("hip_cm", parseFloat(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label>Shoulder Width</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={options.shoulder_width_cm}
                        onChange={(e) => updateOption("shoulder_width_cm", parseFloat(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label>Sleeve Length</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={options.sleeve_length_cm}
                        onChange={(e) => updateOption("sleeve_length_cm", parseFloat(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label>Back Length</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={options.back_length_cm}
                        onChange={(e) => updateOption("back_length_cm", parseFloat(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label>Neck Circumference</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={options.neck_circ_cm}
                        onChange={(e) => updateOption("neck_circ_cm", parseFloat(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label>Inseam</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={options.inseam_cm}
                        onChange={(e) => updateOption("inseam_cm", parseFloat(e.target.value))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Continue with other sections... */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Body Build & Silhouette</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Build Intensity</Label>
                    <Select value={options.build_intensity} onValueChange={(v) => updateOption("build_intensity", v)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {["very_skinny", "skinny", "slim", "regular", "sturdy", "toned", "athletic", "muscular", "very_muscular", "bodybuilder", "curvy_light", "curvy_moderate", "curvy_full", "plus_1x", "plus_2x", "plus_3x", "plus_4x", "maternity_T1", "maternity_T2", "maternity_T3"].map(build => (
                          <SelectItem key={build} value={build}>{build.replace("_", " ")}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Silhouette Distribution</Label>
                    <Select value={options.silhouette_distribution} onValueChange={(v) => updateOption("silhouette_distribution", v)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {["rectangle_mild", "rectangle_strong", "hourglass_balanced", "hourglass_top", "hourglass_bottom", "pear_mild", "pear_strong", "inverted_triangle_mild", "inverted_triangle_strong", "oval_mild", "oval_full", "diamond"].map(shape => (
                          <SelectItem key={shape} value={shape}>{shape.replace("_", " ")}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* BREAST SPECIFICATIONS */}
              {options.presentation === "female" && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Breast Specifications</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Cup Size</Label>
                        <Select value={options.cup} onValueChange={(v) => updateOption("cup", v)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {["AA", "A", "B", "C", "D", "DD", "E", "F", "G", "H", "I", "J", "K"].map(cup => (
                              <SelectItem key={cup} value={cup}>{cup}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Band Size (cm)</Label>
                        <Input
                          type="number"
                          value={options.band_cm}
                          onChange={(e) => updateOption("band_cm", parseFloat(e.target.value))}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Projection</Label>
                        <Select value={options.projection} onValueChange={(v) => updateOption("projection", v)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="minimal">Minimal</SelectItem>
                            <SelectItem value="average">Average</SelectItem>
                            <SelectItem value="pronounced">Pronounced</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Ptosis Grade</Label>
                        <Select value={options.ptosis_grade} onValueChange={(v) => updateOption("ptosis_grade", v)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0_none">0 - None</SelectItem>
                            <SelectItem value="1_mild">1 - Mild</SelectItem>
                            <SelectItem value="2_moderate">2 - Moderate</SelectItem>
                            <SelectItem value="3_significant">3 - Significant</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Upper Pole Fullness</Label>
                        <Select value={options.upper_pole_fullness} onValueChange={(v) => updateOption("upper_pole_fullness", v)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="empty">Empty</SelectItem>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="full">Full</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Spacing</Label>
                        <Select value={options.spacing} onValueChange={(v) => updateOption("spacing", v)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="close">Close</SelectItem>
                            <SelectItem value="average">Average</SelectItem>
                            <SelectItem value="wide">Wide</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Shape</Label>
                        <Select value={options.shape} onValueChange={(v) => updateOption("shape", v)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="round">Round</SelectItem>
                            <SelectItem value="oval">Oval</SelectItem>
                            <SelectItem value="teardrop">Teardrop</SelectItem>
                            <SelectItem value="bell">Bell</SelectItem>
                            <SelectItem value="side_set">Side Set</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Support State</Label>
                        <Select value={options.support_state} onValueChange={(v) => updateOption("support_state", v)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="unsupported">Unsupported</SelectItem>
                            <SelectItem value="light_support">Light Support</SelectItem>
                            <SelectItem value="moderate_support">Moderate Support</SelectItem>
                            <SelectItem value="strong_support">Strong Support</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* GLUTE SPECIFICATIONS */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Glute Specifications</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Seat Size Bucket</Label>
                      <Select value={options.seat_size_bucket} onValueChange={(v) => updateOption("seat_size_bucket", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {["81–85", "86–90", "91–95", "96–100", "101–105", "106–110", "111–115", "116–120", "121–125", "126–130", "≥131"].map(bucket => (
                            <SelectItem key={bucket} value={bucket}>{bucket} cm</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Glute Projection</Label>
                      <Select value={options.glute_projection} onValueChange={(v) => updateOption("glute_projection", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="minimal">Minimal</SelectItem>
                          <SelectItem value="mild">Mild</SelectItem>
                          <SelectItem value="moderate">Moderate</SelectItem>
                          <SelectItem value="pronounced">Pronounced</SelectItem>
                          <SelectItem value="extreme">Extreme</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Seat Shape</Label>
                      <Select value={options.seat_shape} onValueChange={(v) => updateOption("seat_shape", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="flat">Flat</SelectItem>
                          <SelectItem value="round_bubble">Round Bubble</SelectItem>
                          <SelectItem value="square">Square</SelectItem>
                          <SelectItem value="heart">Heart</SelectItem>
                          <SelectItem value="v_shape">V Shape</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Hip Dips</Label>
                      <Select value={options.hip_dips} onValueChange={(v) => updateOption("hip_dips", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          <SelectItem value="mild">Mild</SelectItem>
                          <SelectItem value="moderate">Moderate</SelectItem>
                          <SelectItem value="pronounced">Pronounced</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* FACIAL FEATURES */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Facial Features</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Eye Color</Label>
                      <Select value={options.eye_color} onValueChange={(v) => updateOption("eye_color", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {["light_blue", "blue", "medium_blue", "dark_blue", "light_green", "green", "medium_green", "dark_green", "light_brown", "medium_brown", "dark_brown", "black", "hazel", "amber", "gray"].map(color => (
                            <SelectItem key={color} value={color}>{color.replace("_", " ")}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Eye Shape</Label>
                      <Select value={options.eye_shape} onValueChange={(v) => updateOption("eye_shape", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="almond">Almond</SelectItem>
                          <SelectItem value="round">Round</SelectItem>
                          <SelectItem value="hooded">Hooded</SelectItem>
                          <SelectItem value="monolid">Monolid</SelectItem>
                          <SelectItem value="upturned">Upturned</SelectItem>
                          <SelectItem value="downturned">Downturned</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Brow Style</Label>
                      <Select value={options.brow_style} onValueChange={(v) => updateOption("brow_style", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="natural">Natural</SelectItem>
                          <SelectItem value="arched">Arched</SelectItem>
                          <SelectItem value="straight">Straight</SelectItem>
                          <SelectItem value="thick">Thick</SelectItem>
                          <SelectItem value="thin">Thin</SelectItem>
                          <SelectItem value="bushy">Bushy</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Lip Fullness</Label>
                      <Select value={options.lip_fullness} onValueChange={(v) => updateOption("lip_fullness", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="very_thin">Very Thin</SelectItem>
                          <SelectItem value="thin">Thin</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="full">Full</SelectItem>
                          <SelectItem value="very_full">Very Full</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Cheekbone Definition</Label>
                      <Select value={options.cheekbone_definition} onValueChange={(v) => updateOption("cheekbone_definition", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="very_high">Very High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Jawline</Label>
                      <Select value={options.jawline} onValueChange={(v) => updateOption("jawline", v)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="soft">Soft</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="defined">Defined</SelectItem>
                          <SelectItem value="sharp">Sharp</SelectItem>
                          <SelectItem value="square">Square</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </ScrollArea>
        </div>

        {/* Prompt Panel */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                Generated Prompt
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyPrompt}
                    disabled={!generatedPrompt}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadPrompt}
                    disabled={!generatedPrompt}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={generatedPrompt}
                readOnly
                placeholder="Generated prompt will appear here..."
                className="min-h-[400px] font-mono text-sm"
              />
            </CardContent>
          </Card>

          <Button
            onClick={generatePrompt}
            disabled={isGenerating}
            className="w-full h-12"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Eye className="mr-2 h-5 w-5 animate-spin" />
                Generating Prompt...
              </>
            ) : (
              <>
                <Eye className="mr-2 h-5 w-5" />
                Generate Prompt
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}

function buildPersonModelPrompt(options: PersonModelOptions): string {
  const sections = [];

  // Profile
  sections.push(`PROFILE:
- Presentation: ${options.presentation}
- Age: ${options.age_years} years
- Skin Tone: ${options.skin_tone.replace("_", " ")}
- Undertone: ${options.undertone}
- Pose: ${options.pose.replace("_", " ")}`);

  // Measurements
  sections.push(`MEASUREMENTS (cm):
- ${options.presentation === "female" ? "Bust" : "Chest"}: ${options.presentation === "female" ? options.bust_cm : options.chest_cm}
${options.presentation === "female" ? `- Underbust: ${options.underbust_cm}` : ""}
- Waist: ${options.waist_cm}
- Hip: ${options.hip_cm}
- Shoulder Width: ${options.shoulder_width_cm}
- Sleeve Length: ${options.sleeve_length_cm}
- Back Length: ${options.back_length_cm}
- Neck Circumference: ${options.neck_circ_cm}
- Inseam: ${options.inseam_cm}
- Thigh Circumference: ${options.thigh_circ_cm}
- Calf Circumference: ${options.calf_circ_cm}
- Head Circumference: ${options.head_circ_cm}
- Hand Length: ${options.hand_length_cm}
- Weight: ${options.weight_kg} kg`);

  // Body Build & Silhouette
  sections.push(`BODY BUILD & SILHOUETTE:
- Build Intensity: ${String(options.build_intensity).replace("_", " ")}
- Silhouette Distribution: ${String(options.silhouette_distribution).replace("_", " ")}
- Limb Emphasis: ${options.limb_emphasis.length > 0 ? options.limb_emphasis.join(", ") : "none"}`);

  // Breast Spec (if female)
  if (options.presentation === "female") {
    sections.push(`BREAST SPECIFICATIONS:
- Cup Size: ${options.cup}
- Band Size: ${options.band_cm} cm
- Projection: ${options.projection}
- Ptosis Grade: ${String(options.ptosis_grade).replace("_", " ")}
- Upper Pole Fullness: ${options.upper_pole_fullness}
- Spacing: ${String(options.spacing).replace("_", " ")}
- Shape: ${options.shape}
- Support State: ${String(options.support_state).replace("_", " ")}`);
  }

  // Seat/Glute Spec
  sections.push(`SEAT/GLUTE SPECIFICATIONS:
- Seat Size Bucket: ${options.seat_size_bucket}
- Glute Projection: ${options.glute_projection}
- Seat Shape: ${String(options.seat_shape).replace("_", " ")}
- Hip Dips: ${options.hip_dips}`);

  // Facial Features
  sections.push(`FACIAL FEATURES:
- Eye Color: ${String(options.eye_color).replace("_", " ")}
- Eye Shape: ${String(options.eye_shape).replace("_", " ")}
- Brow Style: ${String(options.brow_style).replace("_", " ")}
- Lip Fullness: ${options.lip_fullness}
- Cheekbone Definition: ${options.cheekbone_definition}
- Jawline: ${options.jawline}`);

  // Hair
  sections.push(`HAIR:
- Length: ${String(options.hair_length).replace("_", " ")}
- Texture: ${options.hair_texture}
- Style: ${options.hair_style.length > 0 ? options.hair_style.join(", ") : "natural"}
- Base Color: ${String(options.hair_color_base).replace("_", " ")}
- Color Effects: ${options.hair_color_effects.length > 0 ? options.hair_color_effects.join(", ") : "none"}
- Density: ${options.hair_density}`);

  // Generation instructions
  sections.push(`GENERATE: Create a single photorealistic full-body ${options.presentation} model image in ${options.pose.replace("_", " ")} pose with sufficient padding on all sides. Ensure anatomical accuracy, natural proportions, and professional studio lighting. Use clean white background. The model should match all specifications above for measurements, build, and features.`);

  return sections.join("\n\n");
}