"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  BodyMeasurements,
  SizeInput,
  ModelIdentity,
  GenerationRequest,
  GeneratedModel,
  GenerationStatus,
  UIState,
} from "../types";
import { SizingKernel } from "../utils/sizingKernel";
import { createGeminiService } from "../services/geminiService";
import { localModelStorage } from "../services/localStorage";
import { SizeInputPanel } from "./SizeInputPanel";
import { MeasurementsPanel } from "./MeasurementsPanel";
import { IdentityPanel } from "./IdentityPanel";
import { GenerationPanel } from "./GenerationPanel";
import { ResultsGallery } from "./ResultsGallery";

export function ModelCreationModule() {
  // Core state
  const [sizeInput, setSizeInput] = useState<SizeInput>({
    system: "cm",
    label: "170 90 70 95",
  });
  const [measurements, setMeasurements] = useState<BodyMeasurements | null>(null);
  const [identity, setIdentity] = useState<ModelIdentity>({
    gender: "woman",
    age_bracket: "25-34",
    mst: 5,
    body_shape: "athletic",
    hair_summary: "shoulder-length brown hair",
  });
  const [generatedModels, setGeneratedModels] = useState<GeneratedModel[]>([]);
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus>({
    isGenerating: false,
    progress: 0,
  });

  // UI state
  const [uiState, setUIState] = useState<UIState>({
    containerWidth: 800,
    isCompact: false,
    activePanel: "input",
  });

  const containerRef = useRef<HTMLDivElement>(null);

  // ResizeObserver for responsive behavior
  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        setUIState(prev => ({
          ...prev,
          containerWidth: width,
          isCompact: width < 600,
        }));
      }
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => resizeObserver.disconnect();
  }, []);

  // Load saved models on mount
  useEffect(() => {
    const loadSavedModels = async () => {
      try {
        const models = await localModelStorage.getModels();
        setGeneratedModels(models);
      } catch (error) {
        console.error("Failed to load saved models:", error);
      }
    };

    loadSavedModels();
  }, []);

  // Convert size input to measurements
  const updateMeasurements = useCallback(() => {
    try {
      let resolvedMeasurements = SizingKernel.convertToBodyMeasurements(sizeInput);
      resolvedMeasurements = SizingKernel.adjustForGender(resolvedMeasurements, identity.gender);
      resolvedMeasurements = SizingKernel.applyBodyShapePreset(resolvedMeasurements, identity.body_shape);

      const errors = SizingKernel.validateMeasurements(resolvedMeasurements);
      if (errors.length > 0) {
        toast.error("Invalid measurements: " + errors.join(", "));
        return;
      }

      setMeasurements(resolvedMeasurements);
      setUIState(prev => ({ ...prev, activePanel: "measurements" }));
    } catch (error) {
      console.error("Failed to convert size input:", error);
      toast.error("Failed to process size input");
    }
  }, [sizeInput, identity]);

  // Generate model
  const generateModel = useCallback(async (prompt: string, angle: string) => {
    if (!measurements) {
      toast.error("Please resolve measurements first");
      return;
    }

    const request: GenerationRequest = {
      measurements,
      identity,
      angles: [angle], // Only generate the selected angle
    };

    try {
      setGenerationStatus({ isGenerating: true, progress: 0, currentAngle: angle });

      const geminiService = await createGeminiService();
      const errors = geminiService.validateRequest(request);

      if (errors.length > 0) {
        toast.error("Validation failed: " + errors.join(", "));
        setGenerationStatus({ isGenerating: false, progress: 0 });
        return;
      }

      // Create a custom model with the provided prompt
      const model = await geminiService.generateModel(request);

      // Override the generated prompt with the user-provided one for display
      model.metadata.prompt_used = prompt;

      // Save to storage
      await localModelStorage.saveModel(model);
      setGeneratedModels(prev => [model, ...prev]);

      setGenerationStatus({ isGenerating: false, progress: 100 });
      setUIState(prev => ({ ...prev, activePanel: "results" }));

      toast.success("Model generated successfully!");
    } catch (error) {
      console.error("Generation failed:", error);
      setGenerationStatus({
        isGenerating: false,
        progress: 0,
        error: error instanceof Error ? error.message : "Generation failed",
      });
      toast.error("Generation failed");
    }
  }, [measurements, identity]);

  // Panel navigation
  const goToPanel = useCallback((panel: UIState["activePanel"]) => {
    setUIState(prev => ({ ...prev, activePanel: panel }));
  }, []);

  // Layout calculation
  const getLayoutClasses = () => {
    const { containerWidth, isCompact } = uiState;

    if (isCompact) {
      return "grid grid-cols-1 gap-4";
    }

    // Two columns for wider containers
    return "grid grid-cols-2 gap-6";
  };

  return (
    <div
      ref={containerRef}
      className="w-full max-w-6xl mx-auto p-4 bg-background text-foreground min-h-screen overflow-y-auto"
    >
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground mb-2">
          Model Creation Studio
        </h1>
        <p className="text-muted-foreground">
          Generate photorealistic human models with precise measurements
        </p>
        <div className="flex items-center gap-2 mt-2">
          <Badge variant="secondary">
            Container: {uiState.containerWidth}px
          </Badge>
          <Badge variant={uiState.isCompact ? "destructive" : "default"}>
            {uiState.isCompact ? "Compact" : "Comfortable"}
          </Badge>
        </div>
      </div>

      {/* Progress indicator */}
      <div className="mb-6">
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
          <span>Progress</span>
          <span>{Math.round((Object.keys({ input: 1, measurements: 2, identity: 3, generation: 4, results: 5 }).indexOf(uiState.activePanel) + 1) / 5 * 100)}%</span>
        </div>
        <div className="w-full bg-muted rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{
              width: `${(Object.keys({ input: 1, measurements: 2, identity: 3, generation: 4, results: 5 }).indexOf(uiState.activePanel) + 1) / 5 * 100}%`
            }}
          />
        </div>
      </div>

      {/* Main content */}
      <div className={cn("mb-6", getLayoutClasses())}>
        {/* Size Input Panel */}
        <Card className={cn(
          "transition-all duration-200",
          uiState.activePanel === "input" && "ring-2 ring-primary"
        )}>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center justify-between">
              Size Input
              <Button
                variant="ghost"
                size="sm"
                onClick={() => goToPanel("input")}
                className={cn(
                  "text-xs",
                  uiState.activePanel === "input" && "text-primary"
                )}
              >
                Edit
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <SizeInputPanel
              sizeInput={sizeInput}
              onChange={setSizeInput}
              onNext={updateMeasurements}
              disabled={generationStatus.isGenerating}
            />
          </CardContent>
        </Card>

        {/* Measurements Panel */}
        <Card className={cn(
          "transition-all duration-200",
          uiState.activePanel === "measurements" && "ring-2 ring-primary"
        )}>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center justify-between">
              Resolved Measurements
              <Button
                variant="ghost"
                size="sm"
                onClick={() => goToPanel("measurements")}
                disabled={!measurements}
                className={cn(
                  "text-xs",
                  uiState.activePanel === "measurements" && "text-primary"
                )}
              >
                View
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <MeasurementsPanel
              measurements={measurements}
              onEdit={() => goToPanel("input")}
            />
          </CardContent>
        </Card>

        {/* Identity Panel */}
        <Card className={cn(
          "transition-all duration-200",
          uiState.activePanel === "identity" && "ring-2 ring-primary"
        )}>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center justify-between">
              Model Identity
              <Button
                variant="ghost"
                size="sm"
                onClick={() => goToPanel("identity")}
                className={cn(
                  "text-xs",
                  uiState.activePanel === "identity" && "text-primary"
                )}
              >
                Configure
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <IdentityPanel
              identity={identity}
              onChange={setIdentity}
              disabled={generationStatus.isGenerating}
            />
          </CardContent>
        </Card>

        {/* Generation Panel */}
        <Card className={cn(
          "transition-all duration-200",
          uiState.activePanel === "generation" && "ring-2 ring-primary"
        )}>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center justify-between">
              Generate Model
              <Button
                variant="ghost"
                size="sm"
                onClick={() => goToPanel("generation")}
                disabled={!measurements}
                className={cn(
                  "text-xs",
                  uiState.activePanel === "generation" && "text-primary"
                )}
              >
                Generate
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <GenerationPanel
              measurements={measurements}
              identity={identity}
              onGenerate={generateModel}
              status={generationStatus}
              disabled={!measurements}
            />
          </CardContent>
        </Card>
      </div>

      {/* Results Gallery */}
      {generatedModels.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Generated Models
              <Button
                variant="ghost"
                size="sm"
                onClick={() => goToPanel("results")}
                className={cn(
                  "text-xs",
                  uiState.activePanel === "results" && "text-primary"
                )}
              >
                View Gallery
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResultsGallery
              models={generatedModels}
              onDelete={async (id) => {
                await localModelStorage.deleteModel(id);
                setGeneratedModels(prev => prev.filter(m => m.id !== id));
                toast.success("Model deleted");
              }}
            />
          </CardContent>
        </Card>
      )}

      {/* Footer */}
      <div className="mt-8 pt-4 border-t border-border">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>Model Creation Module v1.0</span>
          <div className="flex items-center gap-4">
            <span>{generatedModels.length} models generated</span>
            <span>AI-generated content</span>
          </div>
        </div>
      </div>
    </div>
  );
}