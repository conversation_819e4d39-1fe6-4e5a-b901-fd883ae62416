"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { BodyMeasurements } from "../types";

interface MeasurementsPanelProps {
  measurements: BodyMeasurements | null;
  onEdit: () => void;
}

const MEASUREMENT_LABELS = {
  stature: "Height",
  chest_circumference: "Chest",
  waist_circumference: "Waist",
  hip_circumference: "Hips",
  inseam_length: "Inseam",
  sleeve_length_crown_to_wrist: "Sleeve",
  neck_circumference: "Neck",
  shoulder_width: "Shoulders",
  arm_length: "Arm Length",
  thigh_circumference: "Thighs",
  calf_circumference: "Calves",
} as const;

export function MeasurementsPanel({ measurements, onEdit }: MeasurementsPanelProps) {
  if (!measurements) {
    return (
      <div className="text-center py-8">
        <div className="text-muted-foreground mb-4">
          <div className="text-4xl mb-2">📏</div>
          <div className="text-lg font-medium">No measurements resolved</div>
          <div className="text-sm">Configure size input first</div>
        </div>
        <Button variant="outline" onClick={onEdit}>
          Go to Size Input
        </Button>
      </div>
    );
  }

  const formatMeasurement = (key: keyof BodyMeasurements): string => {
    const value = measurements[key];
    if (key.includes("length") || key.includes("width") || key.includes("height")) {
      return `${value.toFixed(1)} cm`;
    }
    return `${value.toFixed(1)} cm`;
  };

  const getMeasurementStatus = (key: keyof BodyMeasurements): "normal" | "adjusted" => {
    // In a real implementation, this would check if the measurement was overridden
    return "normal";
  };

  return (
    <div className="space-y-4">
      {/* Summary */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <Card className="bg-primary/5 border-primary/20">
          <CardContent className="p-3">
            <div className="text-2xl font-bold text-primary">
              {measurements.stature.toFixed(0)}cm
            </div>
            <div className="text-xs text-muted-foreground">Height</div>
          </CardContent>
        </Card>

        <Card className="bg-secondary/50">
          <CardContent className="p-3">
            <div className="text-2xl font-bold">
              {measurements.chest_circumference.toFixed(0)}cm
            </div>
            <div className="text-xs text-muted-foreground">Chest</div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Measurements */}
      <div className="space-y-3">
        <h4 className="font-medium text-sm">Detailed Measurements</h4>

        <div className="grid grid-cols-1 gap-2">
          {Object.entries(MEASUREMENT_LABELS).map(([key, label]) => {
            const status = getMeasurementStatus(key as keyof BodyMeasurements);
            return (
              <div
                key={key}
                className="flex items-center justify-between py-2 px-3 rounded-md bg-muted/30"
              >
                <span className="text-sm font-medium">{label}</span>
                <div className="flex items-center gap-2">
                  <span className="font-mono text-sm">
                    {formatMeasurement(key as keyof BodyMeasurements)}
                  </span>
                  {status === "adjusted" && (
                    <Badge variant="secondary" className="text-xs">
                      Adjusted
                    </Badge>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Body Proportions */}
      <Card className="bg-muted/50">
        <CardContent className="pt-4">
          <h4 className="font-medium text-sm mb-3">Body Proportions</h4>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-muted-foreground">Waist-to-Hip Ratio</div>
              <div className="font-mono">
                {(measurements.waist_circumference / measurements.hip_circumference).toFixed(2)}
              </div>
            </div>

            <div>
              <div className="text-muted-foreground">Chest-to-Waist Ratio</div>
              <div className="font-mono">
                {(measurements.chest_circumference / measurements.waist_circumference).toFixed(2)}
              </div>
            </div>

            <div>
              <div className="text-muted-foreground">Shoulder-to-Chest Ratio</div>
              <div className="font-mono">
                {(measurements.shoulder_width / measurements.chest_circumference).toFixed(2)}
              </div>
            </div>

            <div>
              <div className="text-muted-foreground">Leg-to-Torso Ratio</div>
              <div className="font-mono">
                {(measurements.inseam_length / (measurements.stature - measurements.inseam_length)).toFixed(2)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex gap-2 pt-2">
        <Button variant="outline" onClick={onEdit} className="flex-1">
          Edit Size Input
        </Button>
        <Button className="flex-1">
          Use These Measurements
        </Button>
      </div>
    </div>
  );
}