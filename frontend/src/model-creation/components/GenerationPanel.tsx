"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, CheckCircle, Loader2, Eye, Image as ImageIcon } from "lucide-react";
import { BodyMeasurements, ModelIdentity, GenerationStatus } from "../types";

interface GenerationPanelProps {
  measurements: BodyMeasurements | null;
  identity: ModelIdentity;
  onGenerate: (prompt: string, angle: string) => void;
  status: GenerationStatus;
  disabled?: boolean;
}

const ANGLES = [
  { key: "front", label: "Front", description: "Full frontal view" },
  { key: "45-left", label: "45° Left", description: "Three-quarter left view" },
  { key: "side", label: "Side", description: "Profile view" },
  { key: "45-right", label: "45° Right", description: "Three-quarter right view" },
  { key: "back", label: "Back", description: "Rear view" },
];

export function GenerationPanel({
  measurements,
  identity,
  onGenerate,
  status,
  disabled
}: GenerationPanelProps) {
  const [generatedPrompt, setGeneratedPrompt] = useState<string>("");
  const [selectedAngle, setSelectedAngle] = useState<string>("front");

  const canGenerate = measurements && !status.isGenerating && !disabled;

  const buildPrompt = () => {
    if (!measurements) return "";

    // System message for consistent, true-to-size generation
    const systemMessage = `You are a professional fashion model generator. Create photorealistic human models that are TRUE TO SIZE with ≤1-2% tolerance. Use clean studio lighting with neutral 3-point setup. Ensure anatomical accuracy and natural proportions.`;

    // Body measurements block
    const measurementsBlock = `
BODY MEASUREMENTS (cm):
- Height: ${measurements.stature}
- Chest: ${measurements.chest_circumference}
- Waist: ${measurements.waist_circumference}
- Hips: ${measurements.hip_circumference}
- Inseam: ${measurements.inseam_length}
- Sleeve: ${measurements.sleeve_length_crown_to_wrist}
- Neck: ${measurements.neck_circumference}
- Shoulders: ${measurements.shoulder_width}
- Arms: ${measurements.arm_length}
- Thighs: ${measurements.thigh_circumference}
- Calves: ${measurements.calf_circumference}
`;

    // Identity summary
    const identitySummary = `
MODEL IDENTITY:
- Gender: ${identity.gender}
- Age: ${identity.age_bracket}
- Body Type: ${identity.body_shape} (MST: ${identity.mst}/10)
- Hair: ${identity.hair_summary}
`;

    // Generation instructions
    const instructions = `
GENERATE: Clean studio shot, photorealistic, true-to-size human model.
- Background: Plain white or light gray studio backdrop
- Lighting: Soft, even, professional photography setup
- Pose: Natural standing pose, relaxed but professional
- Clothing: Form-fitting base layer (leotard/tank top/shorts) to show body shape
- Quality: High resolution, sharp details, natural skin texture
- Angle: ${ANGLES.find(a => a.key === selectedAngle)?.label} view

IMPORTANT: Ensure measurements are accurate to within 1-2%. No distortions or exaggerations.`;

    return systemMessage + measurementsBlock + identitySummary + instructions;
  };

  const handleGeneratePrompt = () => {
    const prompt = buildPrompt();
    setGeneratedPrompt(prompt);
  };

  const handleGenerateImage = () => {
    if (generatedPrompt) {
      onGenerate(generatedPrompt, selectedAngle);
    }
  };

  const getStatusIcon = () => {
    if (status.isGenerating) {
      return <Loader2 className="h-5 w-5 animate-spin text-primary" />;
    }
    if (status.error) {
      return <AlertCircle className="h-5 w-5 text-destructive" />;
    }
    if (status.progress === 100) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    return null;
  };

  const getStatusMessage = () => {
    if (status.isGenerating) {
      return status.currentAngle
        ? `Generating ${status.currentAngle} view...`
        : "Initializing generation...";
    }
    if (status.error) {
      return `Error: ${status.error}`;
    }
    if (status.progress === 100) {
      return "Generation completed successfully!";
    }
    return "Ready to generate model";
  };

  return (
    <div className="space-y-6">
      {/* Generation Status */}
      <Card className="bg-muted/50">
        <CardContent className="pt-4">
          <div className="flex items-center gap-3 mb-3">
            {getStatusIcon()}
            <div className="flex-1">
              <div className="font-medium text-sm">Generation Status</div>
              <div className="text-sm text-muted-foreground">
                {getStatusMessage()}
              </div>
            </div>
          </div>

          {status.isGenerating && (
            <div className="space-y-2">
              <Progress value={status.progress} className="w-full" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Progress</span>
                <span>{status.progress}%</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Generation Preview */}
      {measurements && (
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Generation Preview</h4>

          <div className="grid grid-cols-1 gap-3">
            {/* Model Summary */}
            <Card className="bg-primary/5 border-primary/20">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-sm">Model Configuration</span>
                  <Badge variant="secondary">Ready</Badge>
                </div>
                <div className="text-sm space-y-1">
                  <div><strong>Gender:</strong> {identity.gender}</div>
                  <div><strong>Age:</strong> {identity.age_bracket}</div>
                  <div><strong>Height:</strong> {measurements.stature.toFixed(0)}cm</div>
                  <div><strong>Body Shape:</strong> {identity.body_shape}</div>
                </div>
              </CardContent>
            </Card>

            {/* Angle Selection */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-medium text-sm">Select Angle</span>
                  <Badge variant="outline">Single angle generation</Badge>
                </div>
                <div className="grid grid-cols-1 gap-2">
                  {ANGLES.map((angle) => (
                    <div
                      key={angle.key}
                      className={`flex items-center justify-between py-2 px-3 rounded-md cursor-pointer transition-colors ${
                        selectedAngle === angle.key
                          ? "bg-primary/10 border border-primary/20"
                          : "bg-muted/30 hover:bg-muted/50"
                      }`}
                      onClick={() => setSelectedAngle(angle.key)}
                    >
                      <div>
                        <div className="font-medium text-sm">{angle.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {angle.description}
                        </div>
                      </div>
                      {selectedAngle === angle.key && (
                        <Badge variant="default" className="text-xs">
                          Selected
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Generated Prompt Display */}
      {generatedPrompt && (
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between mb-3">
              <span className="font-medium text-sm">Generated Prompt</span>
              <Badge variant="secondary">Ready for generation</Badge>
            </div>
            <Textarea
              value={generatedPrompt}
              readOnly
              className="min-h-[200px] text-sm font-mono"
              placeholder="Generated prompt will appear here..."
            />
          </CardContent>
        </Card>
      )}

      {/* Generation Settings */}
      <Card className="bg-muted/50">
        <CardContent className="pt-4">
          <h4 className="font-medium text-sm mb-3">Generation Settings</h4>

          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span>Model:</span>
              <span className="font-mono">gemini-2.5-flash-image-preview</span>
            </div>

            <div className="flex justify-between">
              <span>Quality:</span>
              <span>High (Studio)</span>
            </div>

            <div className="flex justify-between">
              <span>Output:</span>
              <span>1 angle × PNG</span>
            </div>

            <div className="flex justify-between">
              <span>Processing:</span>
              <span>True-to-size (±1-2%)</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Safety Notice */}
      <Card className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
        <CardContent className="pt-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
            <div className="text-sm">
              <div className="font-medium text-amber-800 dark:text-amber-200 mb-1">
                Safety & Ethics
              </div>
              <div className="text-amber-700 dark:text-amber-300">
                Generated models are AI-created content. No real persons are depicted.
                Content is filtered for appropriateness.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="pt-2 space-y-3">
        <div className="flex gap-3">
          <Button
            onClick={handleGeneratePrompt}
            disabled={!canGenerate}
            variant="outline"
            className="flex-1 h-12 text-base font-medium"
          >
            <Eye className="mr-2 h-5 w-5" />
            Generate Prompt
          </Button>

          <Button
            onClick={handleGenerateImage}
            disabled={!canGenerate || !generatedPrompt}
            className="flex-1 h-12 text-base font-medium"
          >
            {status.isGenerating ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <ImageIcon className="mr-2 h-5 w-5" />
                Generate Image
              </>
            )}
          </Button>
        </div>

        {!measurements && (
          <p className="text-sm text-muted-foreground text-center">
            Configure measurements first to enable generation
          </p>
        )}

        {measurements && !generatedPrompt && (
          <p className="text-sm text-muted-foreground text-center">
            Generate a prompt first, then create the image
          </p>
        )}
      </div>
    </div>
  );
}