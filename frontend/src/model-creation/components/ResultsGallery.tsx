"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Download, Trash2, Eye, Calendar, User } from "lucide-react";
import { GeneratedModel } from "../types";

interface ResultsGalleryProps {
  models: GeneratedModel[];
  onDelete: (id: string) => void;
}

const ANGLE_LABELS = {
  front: "Front",
  "45-left": "45° Left",
  side: "Side Profile",
  "45-right": "45° Right",
  back: "Back",
};

export function ResultsGallery({ models, onDelete }: ResultsGalleryProps) {
  const [selectedModel, setSelectedModel] = useState<GeneratedModel | null>(null);

  const handleDownload = (model: GeneratedModel, angle: string) => {
    const imageUrl = model.angles[angle];
    if (!imageUrl) return;

    // Create download link
    const link = document.createElement("a");
    link.href = imageUrl;
    link.download = `model_${model.id}_${angle}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDownloadAll = (model: GeneratedModel) => {
    Object.entries(model.angles).forEach(([angle, url]) => {
      if (url) {
        setTimeout(() => handleDownload(model, angle), 100);
      }
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (models.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground mb-4">
          <div className="text-4xl mb-2">🖼️</div>
          <div className="text-lg font-medium">No models generated yet</div>
          <div className="text-sm">Generate your first model to see results here</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Generated Models</h3>
        <Badge variant="secondary">{models.length} models</Badge>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {models.map((model) => (
          <Card key={model.id} className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-base flex items-center gap-2">
                    <User className="h-4 w-4" />
                    {model.identity.gender} • {model.identity.age_bracket}
                  </CardTitle>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(model.generatedAt)}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {model.identity.body_shape}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      MST: {model.identity.mst}/10
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadAll(model)}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    All
                  </Button>

                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedModel(model)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Model Details</DialogTitle>
                      </DialogHeader>
                      <ModelDetailView model={model} onDownload={handleDownload} />
                    </DialogContent>
                  </Dialog>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDelete(model.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              {/* Thumbnail Grid */}
              <div className="grid grid-cols-5 gap-2 mb-4">
                {Object.entries(ANGLE_LABELS).map(([angle, label]) => {
                  const imageUrl = model.angles[angle];
                  return (
                    <div key={angle} className="space-y-2">
                      <div className="aspect-square bg-muted rounded-md overflow-hidden">
                        {imageUrl ? (
                          <img
                            src={imageUrl}
                            alt={`${label} view`}
                            className="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
                            onClick={() => handleDownload(model, angle)}
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
                            No image
                          </div>
                        )}
                      </div>
                      <div className="text-center">
                        <div className="text-xs font-medium">{label}</div>
                        {imageUrl && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownload(model, angle)}
                            className="h-6 px-2 text-xs"
                          >
                            <Download className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Measurements Summary */}
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground">Height</div>
                  <div className="font-mono">{model.measurements.stature.toFixed(0)}cm</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Chest</div>
                  <div className="font-mono">{model.measurements.chest_circumference.toFixed(0)}cm</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Waist</div>
                  <div className="font-mono">{model.measurements.waist_circumference.toFixed(0)}cm</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Hips</div>
                  <div className="font-mono">{model.measurements.hip_circumference.toFixed(0)}cm</div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

interface ModelDetailViewProps {
  model: GeneratedModel;
  onDownload: (model: GeneratedModel, angle: string) => void;
}

function ModelDetailView({ model, onDownload }: ModelDetailViewProps) {
  return (
    <div className="space-y-6">
      {/* Model Info */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">Identity</h4>
          <div className="text-sm space-y-1">
            <div><strong>Gender:</strong> {model.identity.gender}</div>
            <div><strong>Age:</strong> {model.identity.age_bracket}</div>
            <div><strong>Body Shape:</strong> {model.identity.body_shape}</div>
            <div><strong>MST:</strong> {model.identity.mst}/10</div>
            <div><strong>Hair:</strong> {model.identity.hair_summary}</div>
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-2">Measurements</h4>
          <div className="text-sm space-y-1 font-mono">
            <div>Height: {model.measurements.stature.toFixed(1)}cm</div>
            <div>Chest: {model.measurements.chest_circumference.toFixed(1)}cm</div>
            <div>Waist: {model.measurements.waist_circumference.toFixed(1)}cm</div>
            <div>Hips: {model.measurements.hip_circumference.toFixed(1)}cm</div>
          </div>
        </div>
      </div>

      {/* Full-size Images */}
      <div className="grid grid-cols-1 gap-4">
        {Object.entries(ANGLE_LABELS).map(([angle, label]) => {
          const imageUrl = model.angles[angle];
          return (
            <div key={angle} className="space-y-2">
              <div className="flex items-center justify-between">
                <h5 className="font-medium">{label}</h5>
                {imageUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDownload(model, angle)}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                )}
              </div>
              <div className="bg-muted rounded-lg overflow-hidden">
                {imageUrl ? (
                  <img
                    src={imageUrl}
                    alt={`${label} view`}
                    className="w-full max-h-96 object-contain"
                  />
                ) : (
                  <div className="w-full h-48 flex items-center justify-center text-muted-foreground">
                    No image available
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Metadata */}
      <div className="text-xs text-muted-foreground border-t pt-4">
        <div><strong>Model ID:</strong> {model.id}</div>
        <div><strong>Generated:</strong> {new Date(model.generatedAt).toLocaleString()}</div>
        <div><strong>AI Model:</strong> {model.metadata.model_version}</div>
        <div><strong>Content:</strong> {model.metadata.ai_generated ? "AI Generated" : "Unknown"}</div>
      </div>
    </div>
  );
}