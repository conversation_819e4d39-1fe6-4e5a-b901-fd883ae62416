"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { SizeInput, SizingSystem } from "../types";

interface SizeInputPanelProps {
  sizeInput: SizeInput;
  onChange: (input: SizeInput) => void;
  onNext: () => void;
  disabled?: boolean;
}

const SIZING_SYSTEMS: { value: SizingSystem; label: string; example: string }[] = [
  { value: "cm", label: "Direct CM", example: "170 90 70 95" },
  { value: "en13402", label: "EN 13402 (EU)", example: "38C" },
  { value: "astm_misses", label: "ASTM Misses", example: "8" },
  { value: "astm_petite", label: "ASTM Petite", example: "4P" },
  { value: "astm_plus", label: "ASTM Plus", example: "16W" },
  { value: "astm_men", label: "ASTM Men", example: "L" },
  { value: "jeans_wl", label: "Jeans W/L", example: "W32L34" },
  { value: "neck_sleeve", label: "Neck × Sleeve", example: "16x35" },
];

export function SizeInputPanel({ sizeInput, onChange, onNext, disabled }: SizeInputPanelProps) {
  const handleSystemChange = (system: SizingSystem) => {
    onChange({
      ...sizeInput,
      system,
      label: getDefaultLabel(system),
    });
  };

  const handleLabelChange = (label: string) => {
    onChange({
      ...sizeInput,
      label,
    });
  };

  const getDefaultLabel = (system: SizingSystem): string => {
    const systemData = SIZING_SYSTEMS.find(s => s.value === system);
    return systemData?.example || "";
  };

  const getSystemDescription = (system: SizingSystem): string => {
    switch (system) {
      case "cm":
        return "Direct measurements in centimeters: height chest waist hips";
      case "en13402":
        return "European sizing with numeric size and cup (e.g., 38C, 40D)";
      case "astm_misses":
        return "US women's standard sizes (0-16)";
      case "astm_petite":
        return "US women's petite sizes with shorter inseam";
      case "astm_plus":
        return "US women's plus sizes (14W-24W)";
      case "astm_men":
        return "US men's standard sizes (S, M, L, XL, XXL)";
      case "jeans_wl":
        return "Jeans sizing: waist and length in inches (W32L34)";
      case "neck_sleeve":
        return "Shirt sizing: neck and sleeve in inches (16x35)";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-4">
      {/* Sizing System Selection */}
      <div className="space-y-2">
        <Label htmlFor="sizing-system">Sizing System</Label>
        <Select
          value={sizeInput.system}
          onValueChange={handleSystemChange}
          disabled={disabled}
        >
          <SelectTrigger id="sizing-system">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {SIZING_SYSTEMS.map((system) => (
              <SelectItem key={system.value} value={system.value}>
                <div className="flex items-center justify-between w-full">
                  <span>{system.label}</span>
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {system.example}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-sm text-muted-foreground">
          {getSystemDescription(sizeInput.system)}
        </p>
      </div>

      {/* Size Label Input */}
      <div className="space-y-2">
        <Label htmlFor="size-label">Size Label</Label>
        <Input
          id="size-label"
          value={sizeInput.label}
          onChange={(e) => handleLabelChange(e.target.value)}
          placeholder={getDefaultLabel(sizeInput.system)}
          disabled={disabled}
          className="font-mono"
        />
        <p className="text-xs text-muted-foreground">
          Enter the size according to the selected system
        </p>
      </div>

      {/* Overrides Section */}
      <Card className="bg-muted/50">
        <CardContent className="pt-4">
          <div className="space-y-3">
            <Label className="text-sm font-medium">Manual Overrides (Optional)</Label>
            <p className="text-xs text-muted-foreground">
              Override specific measurements in centimeters
            </p>

            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor="height-override" className="text-xs">Height (cm)</Label>
                <Input
                  id="height-override"
                  type="number"
                  placeholder="170"
                  value={sizeInput.overrides?.stature || ""}
                  onChange={(e) => {
                    const value = e.target.value ? Number(e.target.value) : undefined;
                    onChange({
                      ...sizeInput,
                      overrides: {
                        ...sizeInput.overrides,
                        stature: value,
                      },
                    });
                  }}
                  disabled={disabled}
                  className="h-8 text-xs"
                />
              </div>

              <div className="space-y-1">
                <Label htmlFor="chest-override" className="text-xs">Chest (cm)</Label>
                <Input
                  id="chest-override"
                  type="number"
                  placeholder="90"
                  value={sizeInput.overrides?.chest_circumference || ""}
                  onChange={(e) => {
                    const value = e.target.value ? Number(e.target.value) : undefined;
                    onChange({
                      ...sizeInput,
                      overrides: {
                        ...sizeInput.overrides,
                        chest_circumference: value,
                      },
                    });
                  }}
                  disabled={disabled}
                  className="h-8 text-xs"
                />
              </div>

              <div className="space-y-1">
                <Label htmlFor="waist-override" className="text-xs">Waist (cm)</Label>
                <Input
                  id="waist-override"
                  type="number"
                  placeholder="70"
                  value={sizeInput.overrides?.waist_circumference || ""}
                  onChange={(e) => {
                    const value = e.target.value ? Number(e.target.value) : undefined;
                    onChange({
                      ...sizeInput,
                      overrides: {
                        ...sizeInput.overrides,
                        waist_circumference: value,
                      },
                    });
                  }}
                  disabled={disabled}
                  className="h-8 text-xs"
                />
              </div>

              <div className="space-y-1">
                <Label htmlFor="hips-override" className="text-xs">Hips (cm)</Label>
                <Input
                  id="hips-override"
                  type="number"
                  placeholder="95"
                  value={sizeInput.overrides?.hip_circumference || ""}
                  onChange={(e) => {
                    const value = e.target.value ? Number(e.target.value) : undefined;
                    onChange({
                      ...sizeInput,
                      overrides: {
                        ...sizeInput.overrides,
                        hip_circumference: value,
                      },
                    });
                  }}
                  disabled={disabled}
                  className="h-8 text-xs"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Button */}
      <div className="pt-2">
        <Button
          onClick={onNext}
          disabled={disabled || !sizeInput.label.trim()}
          className="w-full"
        >
          Resolve Measurements
        </Button>
      </div>
    </div>
  );
}