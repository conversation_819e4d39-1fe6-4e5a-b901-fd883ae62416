import { GeneratedModel, ModelStorage } from "../types";

export class LocalModelStorage implements ModelStorage {
  private readonly MODELS_KEY = "model_creation_models";
  private readonly ASSETS_DIR = "/model-creation/assets";

  async saveModel(model: GeneratedModel): Promise<string> {
    const models = await this.getModels();
    models.push(model);

    // Keep only last 50 models to prevent storage bloat
    if (models.length > 50) {
      models.splice(0, models.length - 50);
    }

    localStorage.setItem(this.MODELS_KEY, JSON.stringify(models));
    return model.id;
  }

  async getModels(): Promise<GeneratedModel[]> {
    try {
      const stored = localStorage.getItem(this.MODELS_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error("Failed to load models from storage:", error);
      return [];
    }
  }

  async deleteModel(id: string): Promise<void> {
    const models = await this.getModels();
    const filtered = models.filter(model => model.id !== id);
    localStorage.setItem(this.MODELS_KEY, JSON.stringify(filtered));
  }

  async saveAsset(filename: string, data: Blob): Promise<string> {
    // For now, return a placeholder URL
    // In a real implementation, this would save to a local file system or cloud storage
    const url = `${this.ASSETS_DIR}/${filename}`;

    // Store blob data as base64 in localStorage (temporary solution)
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader.result as string;
      localStorage.setItem(`asset_${filename}`, base64);
    };
    reader.readAsDataURL(data);

    return url;
  }

  async getAsset(filename: string): Promise<string | null> {
    return localStorage.getItem(`asset_${filename}`);
  }

  // Utility methods
  async clearAllData(): Promise<void> {
    localStorage.removeItem(this.MODELS_KEY);

    // Clear all asset keys
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith("asset_")) {
        localStorage.removeItem(key);
      }
    });
  }

  async getStorageStats(): Promise<{ models: number; assets: number; size: string }> {
    const models = await this.getModels();
    const assetKeys = Object.keys(localStorage).filter(key => key.startsWith("asset_"));

    // Estimate size (rough calculation)
    let totalSize = 0;
    assetKeys.forEach(key => {
      const data = localStorage.getItem(key);
      if (data) {
        totalSize += data.length;
      }
    });

    const sizeInMB = (totalSize / (1024 * 1024)).toFixed(2);

    return {
      models: models.length,
      assets: assetKeys.length,
      size: `${sizeInMB} MB`,
    };
  }
}

// Export singleton instance
export const localModelStorage = new LocalModelStorage();