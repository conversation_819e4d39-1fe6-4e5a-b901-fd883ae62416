import {
  BodyMeasurements,
  ModelIdentity,
  GeneratedModel,
  GenerationRequest,
} from "../types";

// Dynamic import for Google Generative AI (client-side only)
let genai: any;

const initializeGemini = async (apiKey: string) => {
  if (typeof window === 'undefined') {
    throw new Error('Gemini service can only be used in browser environment');
  }

  try {
    // Dynamic import to avoid SSR issues - use correct import path
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    genai = new GoogleGenerativeAI(apiKey);
  } catch (error) {
    console.error('Failed to initialize Gemini:', error);
    throw new Error('Google Generative AI library not available. Please check your installation.');
  }
};

export class GeminiModelService {
  private model: any = null;

  constructor(apiKey: string) {
    this.initializeService(apiKey);
  }

  private async initializeService(apiKey: string) {
    await initializeGemini(apiKey);
    // Match main app pattern: use GoogleGenerativeAI, then getGenerativeModel
    this.model = genai.getGenerativeModel({
      model: "gemini-2.5-flash-image-preview",
    });
  }

  async generateModel(request: GenerationRequest): Promise<GeneratedModel> {
    if (!this.model) {
      throw new Error('Gemini service not initialized');
    }

    const prompt = this.buildPrompt(request);
    const images = await this.generateImages(prompt, request.angles);

    const model: GeneratedModel = {
      id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      angles: images,
      measurements: request.measurements,
      identity: request.identity,
      generatedAt: new Date().toISOString(),
      metadata: {
        ai_generated: true,
        model_version: "gemini-2.5-flash-image-preview",
        prompt_used: prompt,
      },
    };

    return model;
  }

  private buildPrompt(request: GenerationRequest): string {
    const { measurements, identity } = request;

    // System message for consistent, true-to-size generation
    const systemMessage = `You are a professional fashion model generator. Create photorealistic human models that are TRUE TO SIZE with ≤1-2% tolerance. Use clean studio lighting with neutral 3-point setup. Ensure anatomical accuracy and natural proportions.`;

    // Body measurements block
    const measurementsBlock = `
BODY MEASUREMENTS (cm):
- Height: ${measurements.stature}
- Chest: ${measurements.chest_circumference}
- Waist: ${measurements.waist_circumference}
- Hips: ${measurements.hip_circumference}
- Inseam: ${measurements.inseam_length}
- Sleeve: ${measurements.sleeve_length_crown_to_wrist}
- Neck: ${measurements.neck_circumference}
- Shoulders: ${measurements.shoulder_width}
- Arms: ${measurements.arm_length}
- Thighs: ${measurements.thigh_circumference}
- Calves: ${measurements.calf_circumference}
`;

    // Identity summary
    const identitySummary = `
MODEL IDENTITY:
- Gender: ${identity.gender}
- Age: ${identity.age_bracket}
- Body Type: ${identity.body_shape} (MST: ${identity.mst}/10)
- Hair: ${identity.hair_summary}
`;

    // Generation instructions
    const instructions = `
GENERATE: Clean studio shots, multi-angle, photorealistic, true-to-size human model.
- Background: Plain white or light gray studio backdrop
- Lighting: Soft, even, professional photography setup
- Pose: Natural standing pose, relaxed but professional
- Clothing: Form-fitting base layer (leotard/tank top/shorts) to show body shape
- Quality: High resolution, sharp details, natural skin texture
- Angles: ${request.angles.join(", ")}

IMPORTANT: Ensure measurements are accurate to within 1-2%. No distortions or exaggerations.`;

    return systemMessage + measurementsBlock + identitySummary + instructions;
  }

  private async generateImages(
    prompt: string,
    angles: string[]
  ): Promise<Record<string, string>> {
    const images: Record<string, string> = {};

    // Generate one image per angle
    for (const angle of angles) {
      const anglePrompt = `${prompt}\n\nSPECIFIC ANGLE: ${angle} view`;

      try {
        // Match main app pattern: simple text content
        const result = await this.model.generateContent(anglePrompt);
        const response = await result.response;

        // Match main app response extraction pattern
        const imageData = this.extractImageFromResponse(response);

        if (imageData) {
          images[angle] = imageData;
        }
      } catch (error) {
        console.error(`Failed to generate ${angle} view:`, error);
        // Continue with other angles
      }
    }

    return images;
  }

  private extractImageFromResponse(response: any): string | null {
    try {
      // Match main app response extraction pattern
      const parts = response.candidates?.[0]?.content?.parts;
      if (!parts) return null;

      for (const part of parts) {
        if (part.inlineData?.data) {
          // Convert to base64 data URL like main app
          const uint8Array = new Uint8Array(part.inlineData.data);
          const binaryString = Array.from(uint8Array, byte => String.fromCharCode(byte)).join('');
          return `data:image/png;base64,${btoa(binaryString)}`;
        }
      }
    } catch (error) {
      console.error("Error extracting image from response:", error);
    }

    return null;
  }

  // Safety validation - RELAXED for creative freedom
  validateRequest(request: GenerationRequest): string[] {
    const errors: string[] = [];

    // Age validation (no minors)
    if (request.identity.age_bracket === "18-24") {
      // Allow but flag for review
    }

    // Size validation - VERY relaxed for creative measurements
    if (request.measurements.stature < 100) {
      errors.push("Height too small for model generation");
    }

    if (request.measurements.stature > 300) {
      errors.push("Height too large for model generation");
    }

    // MUCH more relaxed waist-to-hip ratio - allow creative measurements
    const waistToHip = request.measurements.waist_circumference / request.measurements.hip_circumference;
    if (waistToHip < 0.3 || waistToHip > 1.5) {
      errors.push("Waist-to-hip ratio outside creative bounds");
    }

    return errors;
  }
}

// Factory function to create service with API key
export async function createGeminiService(apiKey?: string): Promise<GeminiModelService> {
  // Match main app: use the API key from backend .env
  const key = apiKey || "AIzaSyBjINsljRZUCMA0zp75Xz7Yhm6cXfUlXSc"; // From backend .env

  if (!key) {
    throw new Error("Gemini API key not found. Please check backend .env file.");
  }

  const service = new GeminiModelService(key);
  // Wait for initialization
  await new Promise(resolve => setTimeout(resolve, 100));
  return service;
}