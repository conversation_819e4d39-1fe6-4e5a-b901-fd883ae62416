import { BodyMeasurements, SizeInput, SizingSystem } from "../types";

// Size mappings and conversion functions
const SIZE_MAPPINGS = {
  // EN 13402 European sizing (numeric/letter)
  en13402: {
    // [numeric_size]: { chest, waist, hip } in cm
    "32A": { chest: 76, waist: 58, hip: 82 },
    "32B": { chest: 78, waist: 58, hip: 82 },
    "32C": { chest: 80, waist: 58, hip: 82 },
    "34A": { chest: 80, waist: 62, hip: 86 },
    "34B": { chest: 82, waist: 62, hip: 86 },
    "34C": { chest: 84, waist: 62, hip: 86 },
    "34D": { chest: 86, waist: 62, hip: 86 },
    "36A": { chest: 84, waist: 66, hip: 90 },
    "36B": { chest: 86, waist: 66, hip: 90 },
    "36C": { chest: 88, waist: 66, hip: 90 },
    "36D": { chest: 90, waist: 66, hip: 90 },
    "38A": { chest: 88, waist: 70, hip: 94 },
    "38B": { chest: 90, waist: 70, hip: 94 },
    "38C": { chest: 92, waist: 70, hip: 94 },
    "38D": { chest: 94, waist: 70, hip: 94 },
    "40A": { chest: 92, waist: 74, hip: 98 },
    "40B": { chest: 94, waist: 74, hip: 98 },
    "40C": { chest: 96, waist: 74, hip: 98 },
    "40D": { chest: 98, waist: 74, hip: 98 },
  },

  // ASTM Misses sizes (US women's)
  astm_misses: {
    "0": { chest: 76, waist: 56, hip: 81 },
    "2": { chest: 79, waist: 58, hip: 84 },
    "4": { chest: 81, waist: 61, hip: 86 },
    "6": { chest: 84, waist: 64, hip: 89 },
    "8": { chest: 86, waist: 66, hip: 91 },
    "10": { chest: 89, waist: 69, hip: 94 },
    "12": { chest: 91, waist: 71, hip: 97 },
    "14": { chest: 94, waist: 74, hip: 99 },
    "16": { chest: 98, waist: 78, hip: 103 },
  },

  // ASTM Petite (shorter inseam)
  astm_petite: {
    "0P": { chest: 76, waist: 56, hip: 81, inseam: 71 },
    "2P": { chest: 79, waist: 58, hip: 84, inseam: 71 },
    "4P": { chest: 81, waist: 61, hip: 86, inseam: 71 },
    "6P": { chest: 84, waist: 64, hip: 89, inseam: 71 },
    "8P": { chest: 86, waist: 66, hip: 91, inseam: 71 },
  },

  // ASTM Plus sizes
  astm_plus: {
    "14W": { chest: 102, waist: 81, hip: 107 },
    "16W": { chest: 106, waist: 85, hip: 111 },
    "18W": { chest: 110, waist: 89, hip: 115 },
    "20W": { chest: 114, waist: 93, hip: 119 },
    "22W": { chest: 118, waist: 97, hip: 123 },
  },

  // ASTM Men's sizes
  astm_men: {
    "S": { chest: 86, waist: 71, hip: 89 },
    "M": { chest: 91, waist: 76, hip: 94 },
    "L": { chest: 97, waist: 81, hip: 99 },
    "XL": { chest: 102, waist: 86, hip: 104 },
    "XXL": { chest: 107, waist: 91, hip: 109 },
  },

  // Jeans W/L (waist × length in inches)
  jeans_wl: {
    // Convert inches to cm, assume standard proportions
    "W28L30": { waist: 71, inseam: 76 },
    "W30L30": { waist: 76, inseam: 76 },
    "W30L32": { waist: 76, inseam: 81 },
    "W32L30": { waist: 81, inseam: 76 },
    "W32L32": { waist: 81, inseam: 81 },
    "W32L34": { waist: 81, inseam: 86 },
    "W34L30": { waist: 86, inseam: 76 },
    "W34L32": { waist: 86, inseam: 81 },
    "W34L34": { waist: 86, inseam: 86 },
    "W34L36": { waist: 86, inseam: 91 },
    "W36L32": { waist: 91, inseam: 81 },
    "W36L34": { waist: 91, inseam: 86 },
    "W38L32": { waist: 97, inseam: 81 },
    "W38L34": { waist: 97, inseam: 86 },
  },

  // Neck × Sleeve (inches)
  neck_sleeve: {
    "14x32": { neck: 36, sleeve: 81 },
    "14.5x32": { neck: 37, sleeve: 81 },
    "15x32": { neck: 38, sleeve: 81 },
    "15x33": { neck: 38, sleeve: 84 },
    "15.5x33": { neck: 39, sleeve: 84 },
    "15.5x34": { neck: 39, sleeve: 86 },
    "16x34": { neck: 41, sleeve: 86 },
    "16x35": { neck: 41, sleeve: 89 },
    "16.5x35": { neck: 42, sleeve: 89 },
    "17x35": { neck: 43, sleeve: 89 },
    "17x36": { neck: 43, sleeve: 91 },
    "17.5x36": { neck: 44, sleeve: 91 },
  },
};

// Body shape presets with proportional adjustments
const BODY_SHAPE_PRESETS = {
  straight: { chest: 1.0, waist: 1.0, hip: 1.0, shoulder: 1.0 },
  athletic: { chest: 1.05, waist: 0.95, hip: 0.98, shoulder: 1.02 },
  curve: { chest: 1.02, waist: 0.96, hip: 1.04, shoulder: 0.98 },
  slim_thick: { chest: 0.98, waist: 0.94, hip: 1.06, shoulder: 0.96 },
};

// Gender-specific base proportions
const GENDER_BASES = {
  woman: {
    stature: 165, // cm
    chest_circumference: 88,
    waist_circumference: 68,
    hip_circumference: 93,
    inseam_length: 76,
    sleeve_length_crown_to_wrist: 76,
    neck_circumference: 33,
    shoulder_width: 38,
    arm_length: 58,
    thigh_circumference: 54,
    calf_circumference: 34,
  },
  man: {
    stature: 175,
    chest_circumference: 96,
    waist_circumference: 81,
    hip_circumference: 96,
    inseam_length: 81,
    sleeve_length_crown_to_wrist: 84,
    neck_circumference: 39,
    shoulder_width: 44,
    arm_length: 62,
    thigh_circumference: 58,
    calf_circumference: 36,
  },
};

export class SizingKernel {
  static convertToBodyMeasurements(input: SizeInput): BodyMeasurements {
    const base = GENDER_BASES.woman; // Default, will be adjusted by caller
    let measurements: Partial<BodyMeasurements> = { ...base };

    switch (input.system) {
      case "cm":
        // Direct CM input - parse the label as JSON or space-separated values
        measurements = this.parseDirectCM(input.label);
        break;

      case "en13402":
        const enData = SIZE_MAPPINGS.en13402[input.label.toUpperCase()];
        if (enData) {
          measurements.chest_circumference = enData.chest;
          measurements.waist_circumference = enData.waist;
          measurements.hip_circumference = enData.hip;
        }
        break;

      case "astm_misses":
        const missesData = SIZE_MAPPINGS.astm_misses[input.label];
        if (missesData) {
          measurements.chest_circumference = missesData.chest;
          measurements.waist_circumference = missesData.waist;
          measurements.hip_circumference = missesData.hip;
        }
        break;

      case "astm_petite":
        const petiteData = SIZE_MAPPINGS.astm_petite[input.label];
        if (petiteData) {
          measurements.chest_circumference = petiteData.chest;
          measurements.waist_circumference = petiteData.waist;
          measurements.hip_circumference = petiteData.hip;
          measurements.inseam_length = petiteData.inseam;
        }
        break;

      case "astm_plus":
        const plusData = SIZE_MAPPINGS.astm_plus[input.label];
        if (plusData) {
          measurements.chest_circumference = plusData.chest;
          measurements.waist_circumference = plusData.waist;
          measurements.hip_circumference = plusData.hip;
        }
        break;

      case "astm_men":
        const menData = SIZE_MAPPINGS.astm_men[input.label.toUpperCase()];
        if (menData) {
          measurements.chest_circumference = menData.chest;
          measurements.waist_circumference = menData.waist;
          measurements.hip_circumference = menData.hip;
        }
        break;

      case "jeans_wl":
        const jeansData = SIZE_MAPPINGS.jeans_wl[input.label.toUpperCase()];
        if (jeansData) {
          measurements.waist_circumference = jeansData.waist;
          measurements.inseam_length = jeansData.inseam;
        }
        break;

      case "neck_sleeve":
        const neckSleeveData = SIZE_MAPPINGS.neck_sleeve[input.label];
        if (neckSleeveData) {
          measurements.neck_circumference = neckSleeveData.neck;
          measurements.sleeve_length_crown_to_wrist = neckSleeveData.sleeve;
        }
        break;
    }

    // Apply manual overrides
    if (input.overrides) {
      measurements = { ...measurements, ...input.overrides };
    }

    return measurements as BodyMeasurements;
  }

  static applyBodyShapePreset(
    measurements: BodyMeasurements,
    bodyShape: string
  ): BodyMeasurements {
    const preset = BODY_SHAPE_PRESETS[bodyShape as keyof typeof BODY_SHAPE_PRESETS];
    if (!preset) return measurements;

    return {
      ...measurements,
      chest_circumference: measurements.chest_circumference * preset.chest,
      waist_circumference: measurements.waist_circumference * preset.waist,
      hip_circumference: measurements.hip_circumference * preset.hip,
      shoulder_width: measurements.shoulder_width * preset.shoulder,
    };
  }

  static adjustForGender(
    measurements: BodyMeasurements,
    gender: "woman" | "man"
  ): BodyMeasurements {
    const base = GENDER_BASES[gender];
    const ratio = measurements.stature / base.stature;

    // Scale all measurements proportionally based on height difference
    return {
      stature: measurements.stature,
      chest_circumference: measurements.chest_circumference * ratio,
      waist_circumference: measurements.waist_circumference * ratio,
      hip_circumference: measurements.hip_circumference * ratio,
      inseam_length: measurements.inseam_length * ratio,
      sleeve_length_crown_to_wrist: measurements.sleeve_length_crown_to_wrist * ratio,
      neck_circumference: measurements.neck_circumference * ratio,
      shoulder_width: measurements.shoulder_width * ratio,
      arm_length: measurements.arm_length * ratio,
      thigh_circumference: measurements.thigh_circumference * ratio,
      calf_circumference: measurements.calf_circumference * ratio,
    };
  }

  private static parseDirectCM(label: string): Partial<BodyMeasurements> {
    // Try to parse as JSON first
    try {
      return JSON.parse(label);
    } catch {
      // Fallback: parse space-separated values
      const values = label.split(/\s+/).map(Number);
      if (values.length >= 3) {
        return {
          stature: values[0] || 170,
          chest_circumference: values[1] || 90,
          waist_circumference: values[2] || 70,
          hip_circumference: values[3] || 95,
        };
      }
    }

    // Default fallback
    return GENDER_BASES.woman;
  }

  static validateMeasurements(measurements: BodyMeasurements): string[] {
    const errors: string[] = [];

    if (measurements.stature < 120 || measurements.stature > 220) {
      errors.push("Height must be between 120-220 cm");
    }

    if (measurements.chest_circumference < 60 || measurements.chest_circumference > 150) {
      errors.push("Chest circumference must be between 60-150 cm");
    }

    if (measurements.waist_circumference < 50 || measurements.waist_circumference > 130) {
      errors.push("Waist circumference must be between 50-130 cm");
    }

    return errors;
  }
}