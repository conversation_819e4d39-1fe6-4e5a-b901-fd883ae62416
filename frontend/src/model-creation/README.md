# Model Creation Module

A standalone, isolated module for generating photorealistic human models using Gemini AI. This module is designed to live inside constrained UI containers (like the "Models" tab in media-studio) and provides fast, self-contained model generation capabilities.

## Features

- **Isolated Design**: No dependencies on main app, fully self-contained
- **Responsive UI**: Container-based responsiveness (420px to 1200px+)
- **Multi-Input Sizing**: Supports CM, EN 13402, ASTM, Jeans W/L, Neck×Sleeve
- **Gemini Integration**: Uses gemini-2.5-flash-image-preview for generation
- **Local Storage**: Temporary persistence with simple storage interface
- **Safety Features**: No minors, no public figures, AI-generated metadata

## Directory Structure

```
model-creation/
├── components/          # React components
├── services/           # Business logic and API clients
├── types/             # TypeScript type definitions
├── utils/             # Utility functions
├── assets/            # Generated model assets
├── .reports/          # Compatibility reports
└── README.md          # This file
```

## Quick Start

1. **Access the module**: Navigate to `/model-creation-test`
2. **Input sizing**: Choose system (CM, EN 13402, ASTM, etc.) and enter measurements
3. **Configure identity**: Select gender, age bracket, MST, body shape
4. **Generate**: Choose angles and optional reference images
5. **Download**: View and download generated multi-angle models

## Sizing Systems Supported

- **Direct CM**: Direct centimeter measurements
- **EN 13402**: European numeric/letter sizing (e.g., 38C, 40D)
- **ASTM Misses/Petite/Plus**: US standard sizes
- **ASTM Mature Men**: Men's sizing standards
- **Jeans W/L**: Waist/Length in inches (e.g., W34 L38)
- **Neck×Sleeve**: Neck and sleeve measurements in inches

## Technical Details

### Responsive Design
- **Narrow (< 600px)**: Single column layout
- **Wide (≥ 600px)**: Two column layout
- **Container-based**: Adapts to parent container width, not viewport
- **ResizeObserver**: Dynamic layout adjustments

### Gemini Integration
- **Model**: gemini-2.5-flash-image-preview
- **Prompt Structure**: System message + user payload with measurements
- **Angles**: front, 45° L/R, side, back
- **Studio Quality**: Clean lighting, neutral 3-point setup

### Storage
- **Local**: SQLite or JSON files under `/model-creation`
- **Assets**: Binary outputs under `/model-creation/assets`
- **Interface**: Simple storage abstraction for future GCS/S3 adapters

## Compatibility

This module mirrors styling and patterns from `/media-studio`:
- Tailwind CSS with custom design tokens
- Inter font family
- Green primary color (#22c55e)
- 0.5rem border radius
- ShadCN UI components

## Safety & Ethics

- **No Minors**: Age validation prevents underage model generation
- **No Public Figures**: Prevents lookalike generation of real people
- **AI Disclosure**: All assets marked as "ai-generated" in metadata
- **Content Filtering**: Built-in safety checks

## Future Extensions

- **Database**: Switch to Postgres for persistence
- **Cloud Storage**: Add GCS/S3 adapters
- **Advanced Features**: Pose variations, clothing options
- **Batch Processing**: Multiple model generation

## Development Notes

- **Isolation**: No imports from outside `/model-creation`
- **Styling**: Copy design tokens from `/media-studio` (read-only)
- **API**: Direct Gemini client usage (no backend proxy)
- **Testing**: Manual testing with provided JSON samples

## Sample Usage

```json
{
  "market_model": "global-fast-fashion, studio-first, catalog-basics",
  "input": {
    "system": "jeans_wl",
    "label": "W34 L38",
    "overrides": { "stature": 193, "sleeve_length_crown_to_wrist": 92 }
  },
  "identity": {
    "gender": "man",
    "age_bracket": "25-34",
    "mst": 6,
    "body_shape": "athletic"
  },
  "angles": ["front", "45-left", "side", "45-right", "back"]
}
```

## Run Steps

1. Ensure Gemini API key is configured
2. Navigate to `/model-creation-test`
3. Input measurements using any supported sizing system
4. Configure model identity and appearance
5. Generate multi-angle models
6. Download results from gallery

## Width/Responsiveness Notes

- **Minimum**: 420px (single column, compact mode)
- **Optimal**: 600px+ (two columns, comfortable mode)
- **Maximum**: 1200px+ (expands gracefully)
- **No scrolling**: Horizontal scrolling prevented
- **Adaptive**: Grid density adjusts based on container width
- **Density Toggle**: "Compact / Comfortable" mode switch