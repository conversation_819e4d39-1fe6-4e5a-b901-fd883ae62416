#!/usr/bin/env python3
"""
Simple script to generate fashion images using Gemini API directly.
Follows the exact pattern from the user's example.
"""

import os
import base64
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv(Path(__file__).parent.parent / ".env")

from google import genai
from google.genai import types
from PIL import Image
from io import BytesIO


def main():
    """Generate fashion image using Gemini API directly."""

    # Get API key
    api_key = os.environ.get("BANANA_API_KEY")
    if not api_key:
        print("❌ BANANA_API_KEY not found in environment variables")
        return

    # Initialize Gemini client
    client = genai.Client(api_key=api_key)

    # Get all images from scripts/images directory
    images_dir = Path("./scripts/images")
    if not images_dir.exists():
        print(f"❌ Images directory not found: {images_dir}")
        return

    # Collect all image files
    image_extensions = {'.jpg', '.jpeg', '.png', '.avif', '.webp'}
    image_files = []

    for image_file in images_dir.iterdir():
        if image_file.is_file() and image_file.suffix.lower() in image_extensions:
            image_files.append(image_file)

    if not image_files:
        print("❌ No image files found in scripts/images")
        return

    # Sort images by filename
    image_files.sort(key=lambda x: x.name)

    print(f"📸 Found {len(image_files)} images (sorted by filename):")
    for img_file in image_files:
        print(f"   - {img_file.name}")

    # Load images as PIL Images (following user's example)
    pil_images = []
    for image_file in image_files:
        try:
            img = Image.open(image_file)
            pil_images.append(img)
            print(f"✅ Loaded: {image_file.name}")
        except Exception as e:
            print(f"⚠️  Failed to load {image_file.name}: {e}")

    if not pil_images:
        print("❌ No images could be loaded")
        return

    # Use the user's exact prompt
    text_input = """
        Make the woman wear the outfit items from first image, and scene from second image. 
        Replace her current outfit as far as needed. 
        DO NOT CHANGE THE WOMAN's features.. 
        The face from third image needs to match EXACTLY. 
        Put her in a fitting daring pose in the background with the props. 
        Ensure the lighting is perfect and top studio quality.
    """

    print("🚀 Generating fashion image...")
    print(f"📝 Using prompt: {text_input[:100]}...")

    try:
        # Generate image using user's exact pattern
        print(f"📡 Making API call with {len(pil_images)} images and text prompt...")
        response = client.models.generate_content(
            model="gemini-2.5-flash-image-preview",
            contents=pil_images + [text_input],  # Images first, then text
        )
        print("📡 API call completed")

        # Debug: Print response structure
        print(f"🔍 Response candidates: {len(response.candidates) if response.candidates else 0}")
        if response.candidates:
            candidate = response.candidates[0]
            print(f"🔍 Candidate content: {candidate.content}")
            print(f"🔍 Candidate finish reason: {getattr(candidate, 'finish_reason', 'N/A')}")

            if candidate.content:
                print(f"🔍 Candidate content parts: {len(candidate.content.parts) if candidate.content.parts else 0}")

                if candidate.content.parts:
                    for i, part in enumerate(candidate.content.parts):
                        print(f"🔍 Part {i}: {type(part)}")
                        if hasattr(part, 'inline_data') and part.inline_data:
                            print(f"   📸 Found inline_data in part {i}")
                        elif hasattr(part, 'text') and part.text:
                            print(f"   📝 Found text in part {i}: {part.text}.")
            else:
                print("❌ Candidate content is None")

        # Extract image data (following user's example) - with safety check
        if response.candidates and response.candidates[0].content and response.candidates[0].content.parts:
            image_parts = [
                part.inline_data.data
                for part in response.candidates[0].content.parts
                if part.inline_data
            ]
        else:
            image_parts = []
            print("❌ No valid content parts found in response")

        if image_parts:
            # Save the generated image
            image = Image.open(BytesIO(image_parts[0]))

            # Create output directory
            output_dir = Path("./scripts/outputs")
            output_dir.mkdir(exist_ok=True)

            output_file = output_dir / "fashion_ecommerce_shot.png"
            image.save(output_file)

            print(f"✅ Image saved to: {output_file}")
            print(f"📏 Image size: {image.size}")
            print(f"🎨 Image mode: {image.mode}")

            # Show image info
            print("\n📊 Generation Summary:")
            print(f"   - Input images: {len(pil_images)}")
            print(f"   - Output file: {output_file}")
            print(f"   - Image dimensions: {image.size[0]}x{image.size[1]}")

        else:
            print("❌ No image data found in response")
            print("💡 This might be because:")
            print("   - The model doesn't support image generation with multiple reference images")
            print("   - The API key doesn't have access to image generation")
            print("   - The prompt needs to be adjusted")

    except Exception as e:
        print(f"❌ Generation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()