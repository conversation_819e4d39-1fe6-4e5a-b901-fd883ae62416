import { api } from "./api";

// Import MediaItem type from media-viewer component
export interface MediaItem {
  type:
    | "image"
    | "video"
    | "featured_image"
    | "featured_video"
    | "detected_image"
    | "detected_video";
  id: string;
  src: string;
  alt: string;
  path?: string;
  key?: string;
  data?: any;
}

export interface Metafield {
  id?: number;
  namespace: string;
  key: string;
  value: string;
  value_type: string;
  description?: string;
  owner_id?: number;
  created_at?: string;
  updated_at?: string;
  owner_resource?: string;
}

export interface ProductVariant {
  id: number;
  external_id: string;
  product_id: number;
  title?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  compare_at_price?: number;
  cost?: number;
  weight?: number;
  weight_unit: string;
  quantity: number;
  inventory_policy?: string;
  inventory_item_id?: string;
  option1?: string;
  option2?: string;
  option3?: string;
  taxable: boolean;
  requires_shipping: boolean;
  fulfillment_service?: string;
  available_for_sale: boolean;
  full_json?: string;
  metafields?: Metafield[];
  created_at: string;
  updated_at: string;
  source_updated_at?: string;
}

export interface ProductImage {
  id: number;
  external_id: string;
  product_id: number;
  variant_id?: number;
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  position: number;
  full_json?: string;
  metafields?: Metafield[];
  created_at: string;
  updated_at: string;
  source_updated_at?: string;
}

export interface Product {
  id: number;
  external_id: string;
  title: string;
  handle?: string;
  vendor?: string;
  product_type?: string;
  status: string;
  published: boolean;
  description?: string;
  tags?: string; // JSON string
  options?: string; // JSON string
  seo?: string; // JSON string
  metafields?: Metafield[]; // Parsed metafields
  collections?: string; // JSON string
  full_json?: string; // Complete raw data from platform
  featured_media?: string; // JSON: Featured media from Shopify (can be image or video)
  store_id: number;
  created_at: string;
  updated_at: string;
  published_at?: string;
  source_updated_at?: string;

  // Relationships
  variants?: ProductVariant[];
  images?: ProductImage[];

  // Computed fields for list view
  variant_count?: number;
  image_count?: number; // total assets (may include videos)

  // Precomputed parsed fields for UI performance
  parsed_tags?: string[];
  parsed_collections?: { id: string; name: string; color?: string }[];

  // Additional precomputed UI helpers (optional)
  searchable_text?: string; // lower-cased concatenation of common searchable fields
  metafields_text?: string; // lower-cased concat of metafield key/value
  image_count_images?: number; // number of image-type assets only
  primary_thumbnail?: string; // url for primary thumbnail (first image asset or fallback)

  // Assets (media items)
  assets?: MediaItem[];
}

export interface SyncStatus {
  store_id: number;
  sync_in_progress: boolean;
  active_job?: {
    id: number;
    status: string;
    started_at?: string;
    entity_type: string;
  };
  lock_available: boolean;
}

export interface SyncResult {
  message: string;
  task_id: string;
  store_id: number;
}

export interface PaginatedProductsResponse {
  items: Product[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Helper function to parse metafields JSON string
const parseMetafields = (metafieldsRaw?: string): Metafield[] | undefined => {
  if (!metafieldsRaw) return undefined;

  try {
    const parsed = JSON.parse(metafieldsRaw);

    // Handle nested namespace/key structure from database
    if (parsed && typeof parsed === "object" && !Array.isArray(parsed)) {
      const metafields: Metafield[] = [];

      // Iterate through namespaces (custom, global, etc.)
      Object.entries(parsed).forEach(
        ([namespace, namespaceData]: [string, any]) => {
          if (namespaceData && typeof namespaceData === "object") {
            // Iterate through keys within each namespace
            Object.entries(namespaceData).forEach(
              ([key, fieldData]: [string, any]) => {
                if (fieldData && typeof fieldData === "object") {
                  metafields.push({
                    namespace,
                    key,
                    value: fieldData.value || "",
                    value_type: fieldData.type || "string",
                    updated_at: fieldData.updated_at,
                    description: fieldData.description,
                  });
                }
              }
            );
          }
        }
      );

      return metafields.length > 0 ? metafields : undefined;
    }

    // Handle legacy array format
    if (Array.isArray(parsed)) {
      return parsed;
    }

    // Handle single object format
    if (parsed && typeof parsed === "object") {
      return [parsed];
    }

    return undefined;
  } catch (error) {
    console.warn(
      "Failed to parse metafields:",
      error,
      "Raw data:",
      metafieldsRaw
    );
    return undefined;
  }
};

// Helper function to process product data and parse metafields
const processProductData = (product: any): Product => {
  return {
    ...product,
    metafields: parseMetafields(product.metafields),
    variants: product.variants?.map((variant: any) => ({
      ...variant,
      metafields: parseMetafields(variant.metafields),
    })),
    images: product.images?.map((image: any) => ({
      ...image,
      metafields: parseMetafields(image.metafields),
    })),
  };
};

export const productService = {
  async getProducts(
    page: number = 1,
    limit: number = 50,
    search?: string,
    options?: { signal?: AbortSignal }
  ): Promise<PaginatedProductsResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (search && search.trim()) {
      params.append("search", search.trim());
    }

    const response = await api.get(`/api/products/?${params.toString()}`, {
      signal: options?.signal,
    });

    // Process the response data to parse metafields
    const processedData = {
      ...response.data,
      items: response.data.items?.map(processProductData) || [],
    };

    return processedData;
  },

  async getProductsOptimized(
    page: number = 1,
    limit: number = 50,
    params?: {
      search?: string;
      vendors?: string[];
      tags?: string[];
      status?: string[];
      types?: string[];
      collections?: string[];
      media_sources?: ("original" | "generated")[];
      media_types?: ("image" | "video")[];
      min_media?: number | null;
      sort_field?:
        | "product_title"
        | "created"
        | "updated"
        | "inventory"
        | "product_type"
        | "vendor";
      sort_dir?: "asc" | "desc";
    },
    options?: { signal?: AbortSignal }
  ): Promise<PaginatedProductsResponse> {
    const qs = new URLSearchParams({
      page: String(page),
      limit: String(limit),
    });

    if (params?.search?.trim()) qs.set("search", params.search.trim());
    if (params?.vendors?.length) qs.set("vendors", params.vendors.join(","));
    if (params?.tags?.length) qs.set("tags", params.tags.join(","));
    if (params?.status?.length) qs.set("status", params.status.join(","));
    if (params?.types?.length) qs.set("types", params.types.join(","));
    if (params?.collections?.length)
      qs.set("collections", params.collections.join(","));
    if (params?.media_sources?.length)
      qs.set("media_sources", params.media_sources.join(","));
    if (params?.media_types?.length)
      qs.set("media_types", params.media_types.join(","));
    if (params?.min_media != null)
      qs.set("min_media", String(params.min_media));
    if (params?.sort_field) qs.set("sort_field", params.sort_field);
    if (params?.sort_dir) qs.set("sort_dir", params.sort_dir);

    const response = await api.get(`/api/products/optimized?${qs.toString()}`, {
      signal: options?.signal,
    });

    const processedData = {
      ...response.data,
      items: response.data.items?.map(processProductData) || [],
    };

    return processedData;
  },

  async getProduct(id: number): Promise<Product> {
    const response = await api.get(`/api/products/${id}`);
    return processProductData(response.data);
  },

  async getSyncStatus(
    storeId: number
  ): Promise<{ store_id: number; sync_status: SyncStatus }> {
    const response = await api.get(
      `/api/stores/${storeId}/sync-progress/products`
    );
    return response.data;
  },

  async getProductFacets(options?: { signal?: AbortSignal }): Promise<{
    vendors: string[];
    types: string[];
    status: string[];
    tags: string[];
    collections: { id: string; name: string }[];
  }> {
    const response = await api.get(`/api/products/facets`, {
      signal: options?.signal,
    });
    return response.data;
  },
};
