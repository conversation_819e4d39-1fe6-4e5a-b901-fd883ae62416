import { api } from "./api";
import type { Asset } from "./mediaService";

export type MediaLibraryCollection = "gallery" | "models" | "outfits" | "scenes";

interface RawMediaLibraryAsset {
  id: number;
  external_id: string;
  collection: string;
  type: string;
  file_uri: string;
  preview_uri?: string;
  original_filename?: string;
  content_type?: string;
  size_bytes?: number;
  metadata?: Record<string, any> | null;
  created_at: string;
}

interface MediaLibraryListResponse {
  items: RawMediaLibraryAsset[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

const toAsset = (raw: RawMediaLibraryAsset): Asset => {
  const url = raw.preview_uri || raw.file_uri;
  const filename = raw.original_filename || `upload-${raw.id}`;

  return {
    id: String(raw.id),
    productId: "",
    url,
    type: (raw.type as Asset["type"]) || "image",
    filename,
    displayName: filename,
    fileUrl: raw.file_uri,
    previewUrl: raw.preview_uri || raw.file_uri,
    prompt: raw.metadata?.prompt,
    sourceType: "ai_generated",
    generatedAt: raw.created_at,
  };
};

class MediaLibraryService {
  async listAssets(params: {
    collection?: MediaLibraryCollection;
    page?: number;
    limit?: number;
  }): Promise<{
    items: Asset[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const response = await api.get<MediaLibraryListResponse>("/api/media/library", {
      params: {
        collection: params.collection,
        page: params.page,
        limit: params.limit,
      },
    });

    return {
      items: response.data.items.map(toAsset),
      total: response.data.total,
      page: response.data.page,
      totalPages: response.data.total_pages,
    };
  }

  async uploadAsset(collection: MediaLibraryCollection, file: File): Promise<Asset> {
    const formData = new FormData();
    formData.append("collection", collection);
    formData.append("file", file);

    const response = await api.post<RawMediaLibraryAsset>(
      "/api/media/library/upload",
      formData,
      {
        headers: { "Content-Type": "multipart/form-data" },
      }
    );

    return toAsset(response.data);
  }
}

export const mediaLibraryService = new MediaLibraryService();
