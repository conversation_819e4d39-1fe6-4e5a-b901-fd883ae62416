/**
 * Video Generation Service - ProductVideo API client
 */

import { api } from "./api";

// Additional types for the new components
export interface VideoGenerationRequest {
  productIds: string[];
  templateId: string;
  aspectRatio: string;
  ctaText: string;
}

// Types for ProductVideo API
export interface GenerateVideoRequest {
  shop_id: string;
  product_ids: string[];
  template_id?: string;
  aspect_ratio?: string;
  locale?: string;
}

export interface VideoVariant {
  variant_id: string;
  variant_name: string;
  status: string;
  video_url?: string;
  thumbnail_url?: string;
  duration?: number;
}

export interface VideoJob {
  job_id: string;
  status: string;
  progress: number;
  variants: VideoVariant[];
}

export interface PushToShopifyRequest {
  shop_id: string;
  product_id: string;
  variant_id: string;
  publish_targets?: string[];
  publish_options?: {
    alt_text?: string;
    position?: number;
  };
}

export interface Template {
  id: string;
  name: string;
  description?: string;
  preview_url?: string;
  category?: string;
}

export interface AnalyticsMetrics {
  views: number;
  plays: number;
  completion_rate: number;
  avg_watch_time: number;
  ctr: number;
  conversions: number;
  conversion_lift?: number;
}

interface GalleryItemData {
  id: string;
  variantName: string;
  productId: string;
  productTitle: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration: number;
  createdAt?: string;
  status: "ready" | "generating" | "failed";
  isFavorite: boolean;
  tags: string[];
  metrics: {
    views: number;
    plays: number;
    completionRate: number;
  };
}

interface GalleryResponse {
  items: GalleryItemData[];
  total: number;
  page: number;
  pageSize: number;
  tags: string[];
}

const parseJsonIfNeeded = (value: unknown): any => {
  if (!value) return undefined;
  if (typeof value === "string") {
    try {
      return JSON.parse(value);
    } catch (err) {
      console.warn("Failed to parse settings JSON", err);
      return undefined;
    }
  }
  return value;
};

const extractTagsFromValue = (value: unknown): string[] => {
  if (!value) return [];
  if (Array.isArray(value)) {
    return value
      .map((tag) => (typeof tag === "string" ? tag.trim() : ""))
      .filter(Boolean);
  }
  if (typeof value === "string") {
    return value
      .split(",")
      .map((tag) => tag.trim())
      .filter(Boolean);
  }
  return [];
};

const buildGalleryItem = (asset: any): { item: GalleryItemData; tags: string[] } => {
  const settings = parseJsonIfNeeded(asset?.settings) ?? {};
  const rawTags = new Set<string>();

  extractTagsFromValue(asset?.tags).forEach((tag) => rawTags.add(tag));
  extractTagsFromValue(settings?.tags).forEach((tag) => rawTags.add(tag));
  extractTagsFromValue(settings?.metadata?.tags).forEach((tag) => rawTags.add(tag));

  const tags = Array.from(rawTags);

  const status = (settings?.status as string) ?? "ready";

  const metricsSource = settings?.metrics ?? {};
  const metrics = {
    views: Number(metricsSource.views ?? metricsSource.totalViews ?? 0) || 0,
    plays: Number(metricsSource.plays ?? metricsSource.totalPlays ?? 0) || 0,
    completionRate:
      Number(
        metricsSource.completionRate ??
          metricsSource.completion_rate ??
          metricsSource.avg_completion_rate
      ) || 0,
  };

  const durationCandidate =
    settings?.duration ??
    settings?.video?.duration ??
    settings?.metadata?.duration ??
    settings?.analytics?.duration;

  const duration = Number(durationCandidate) || 0;

  const variantName =
    settings?.variant_name ??
    settings?.variantName ??
    (typeof asset?.prompt === "string" && asset.prompt.length > 0
      ? asset.prompt
      : `Variant ${asset?.id ?? ""}`);

  const productTitle =
    settings?.product_title ??
    settings?.productTitle ??
    settings?.product?.title ??
    settings?.metadata?.productTitle ??
    (variantName && typeof variantName === "string"
      ? variantName
      : `Product ${asset?.product_id ?? ""}`);

  const galleryItem: GalleryItemData = {
    id: String(asset?.id ?? ""),
    variantName,
    productId: asset?.product_id ? String(asset.product_id) : "",
    productTitle,
    videoUrl: asset?.file_uri ?? asset?.preview_uri ?? "",
    thumbnailUrl: asset?.preview_uri ?? asset?.file_uri ?? undefined,
    duration,
    createdAt: asset?.created_at ?? settings?.created_at,
    status: status === "generating" || status === "failed" ? (status as GalleryItemData["status"]) : "ready",
    isFavorite: Boolean(settings?.isFavorite ?? settings?.favorite),
    tags,
    metrics,
  };

  return { item: galleryItem, tags };
};

export interface ProductAnalytics {
  product_id: string;
  variant_id?: string;
  metrics: AnalyticsMetrics;
  period: {
    from: string;
    to: string;
  };
}

/**
 * Video Generation API Service
 */
export class VideoService {
  /**
   * Get product analytics
   */
  static async getProductAnalytics(
    productId: string,
    fromDate?: string,
    toDate?: string,
    variantId?: string
  ): Promise<ProductAnalytics> {
    const params = new URLSearchParams();
    if (fromDate) params.append("from_date", fromDate);
    if (toDate) params.append("to_date", toDate);
    if (variantId) params.append("variant_id", variantId);

    const response = await api.get(
      `/api/analytics/product/${productId}?${params}`
    );
    return response.data;
  }

  /**
   * Get dashboard metrics
   */
  static async getDashboardMetrics(days: number = 30) {
    const response = await api.get(`/api/analytics/dashboard?days=${days}`);
    return response.data;
  }

  /**
   * Generate videos with new interface
   */
  static async generateVideos(request: VideoGenerationRequest) {
    const response = await api.post("/api/video-generation/generate", request);
    return response.data;
  }

  /**
   * Get video variants
   */
  static async getVariants() {
    const response = await api.get("/api/video-generation/variants");
    return response.data;
  }

  /**
   * Get variants by IDs
   */
  static async getVariantsByIds(variantIds: string[]) {
    const response = await api.post("/api/video-generation/variants/by-ids", {
      variantIds,
    });
    return response.data;
  }

  /**
   * Toggle favorite status
   */
  static async toggleFavorite(variantId: string) {
    const response = await api.post(
      `/api/video-generation/variants/${variantId}/favorite`
    );
    return response.data;
  }

  /**
   * Regenerate variant
   */
  static async regenerateVariant(variantId: string) {
    const response = await api.post(
      `/api/video-generation/variants/${variantId}/regenerate`
    );
    return response.data;
  }

  /**
   * Track video event
   */
  static async trackEvent(event: {
    variantId: string;
    eventType: string;
    timestamp: string;
    sessionId: string;
  }) {
    const response = await api.post("/api/analytics/events/ingest", {
      event_type: event.eventType,
      video_variant_id: event.variantId,
      session_id: event.sessionId,
      timestamp: event.timestamp,
      dedup_token: `${event.sessionId}-${event.variantId}-${event.eventType}-${Date.now()}`,
    });
    return response.data;
  }

  /**
   * Push to Shopify with new interface
   */
  static async pushToShopify(params: {
    variantIds: string[];
    altTexts: Record<string, string>;
    mediaPositions: Record<string, number>;
    replaceExisting: boolean;
  }) {
    const response = await api.post(
      "/api/video-generation/push-to-shopify",
      params
    );
    return response.data;
  }

  /**
   * Get push job status
   */
  static async getPushJobStatus(jobId: string) {
    const response = await api.get(
      `/api/video-generation/push-jobs/${jobId}/status`
    );
    return response.data;
  }

  /**
   * Get gallery items
   */
  static async getGallery(params: {
    search?: string;
    sortBy?: string;
    status?: string;
    tags?: string[];
    page?: number;
    limit?: number;
  }): Promise<GalleryResponse> {
    const page = params.page ?? 1;
    const pageSize = params.limit ?? 24;
    const fetchLimit = Math.max(page * pageSize, 200);

    const response = await api.get("/api/media/assets", {
      params: {
        page: 1,
        per_page: fetchLimit,
      },
    });

    const rawAssets = response.data?.assets ?? [];

    const mapped = rawAssets
      .map((asset: any) => buildGalleryItem(asset))
      .filter(({ item }) => Boolean(item.videoUrl));

    const allTagsSet = new Set<string>();
    mapped.forEach(({ tags }) => tags.forEach((tag) => allTagsSet.add(tag)));

    let filteredItems = mapped.map(({ item }) => item);

    if (params.search) {
      const query = params.search.toLowerCase();
      filteredItems = filteredItems.filter(
        (item) =>
          item.variantName.toLowerCase().includes(query) ||
          item.productTitle.toLowerCase().includes(query)
      );
    }

    if (params.status && params.status.trim().length > 0) {
      const statusFilter = params.status.toLowerCase();
      filteredItems = filteredItems.filter(
        (item) => item.status.toLowerCase() === statusFilter
      );
    }

    if (params.tags && params.tags.length > 0) {
      const tagSet = new Set(
        params.tags.map((tag) => tag.toLowerCase().trim()).filter(Boolean)
      );

      filteredItems = filteredItems.filter((item) =>
        item.tags.some((tag) => tagSet.has(tag.toLowerCase()))
      );
    }

    const sortBy = params.sortBy ?? "created_desc";
    const itemsForSorting = [...filteredItems];

    const getCreatedAt = (item: GalleryItemData) =>
      item.createdAt ? new Date(item.createdAt).getTime() : 0;

    switch (sortBy) {
      case "created_asc":
        itemsForSorting.sort((a, b) => getCreatedAt(a) - getCreatedAt(b));
        break;
      case "name_asc":
        itemsForSorting.sort((a, b) =>
          a.variantName.localeCompare(b.variantName)
        );
        break;
      case "name_desc":
        itemsForSorting.sort((a, b) =>
          b.variantName.localeCompare(a.variantName)
        );
        break;
      case "views_desc":
        itemsForSorting.sort((a, b) => b.metrics.views - a.metrics.views);
        break;
      case "plays_desc":
        itemsForSorting.sort((a, b) => b.metrics.plays - a.metrics.plays);
        break;
      case "created_desc":
      default:
        itemsForSorting.sort((a, b) => getCreatedAt(b) - getCreatedAt(a));
        break;
    }

    const total = itemsForSorting.length;
    const startIndex = (page - 1) * pageSize;
    const paginatedItems = itemsForSorting.slice(startIndex, startIndex + pageSize);

    return {
      items: paginatedItems,
      total,
      page,
      pageSize,
      tags: Array.from(allTagsSet),
    };
  }

  /**
   * Get gallery tags
   */
  static async getGalleryTags() {
    const gallery = await this.getGallery({ page: 1, limit: 200 });
    return {
      tags: gallery.tags,
    };
  }

  /**
   * Create bulk download
   */
  static async createBulkDownload(variantIds: string[]) {
    const response = await api.post("/api/video-generation/bulk-download", {
      variantIds,
    });
    return response.data.downloadUrl;
  }

  /**
   * Bulk delete variants
   */
  static async bulkDelete(variantIds: string[]) {
    const response = await api.delete("/api/video-generation/variants/bulk", {
      data: { variantIds },
    });
    return response.data;
  }

  /**
   * Get analytics dashboard data
   */
  static async getAnalytics(params: { timeRange: string; productId?: string }) {
    const response = await api.get("/api/analytics/dashboard", { params });
    return response.data;
  }

  /**
   * Get analytics products
   */
  static async getAnalyticsProducts() {
    const response = await api.get("/api/analytics/products");
    return response.data;
  }
}

export default VideoService;
