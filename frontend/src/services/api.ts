import axios from "axios";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8123";

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  if (typeof window !== "undefined") {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log("API request with auth token:", {
        url: config.url,
        hasToken: !!token,
      });
    } else {
      console.warn("API request without auth token:", { url: config.url });
    }
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Make dev-time errors clearer (especially when backend is offline)
    const isNetworkError = !error.response;
    const logPayload = isNetworkError
      ? {
          message: error.message,
          code: error.code,
          url: error.config?.url,
          baseURL: error.config?.baseURL,
        }
      : {
          status: error.response?.status,
          statusText: error.response?.statusText,
          url: error.config?.url,
          data: error.response?.data,
        };

    (isNetworkError ? console.warn : console.error)(
      isNetworkError ? "API Network Error" : "API Error",
      logPayload
    );

    if (error.response?.status === 401) {
      if (typeof window !== "undefined") {
        localStorage.removeItem("token");
        localStorage.removeItem("token_expiry");
        window.location.href = "/login";
      }
    } else if (error.response?.status === 403) {
      console.error("403 Forbidden - Check authentication and permissions");
    }
    return Promise.reject(error);
  }
);
