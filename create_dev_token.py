#!/usr/bin/env python3
"""
Create a development JWT token for testing the frontend.
"""

import jwt
from datetime import datetime, timedelta

# Backend configuration (from backend/src/core/config.py)
SECRET_KEY = "your-secret-key-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 43200  # 30 days

def create_dev_token():
    """Create a development JWT token."""
    # Token payload
    payload = {
        "sub": "2",  # User ID
        "email": "<EMAIL>",
        "exp": datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    }
    
    # Create token
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALG<PERSON>ITHM)
    return token

if __name__ == "__main__":
    token = create_dev_token()
    print("Development JWT Token:")
    print(token)
    print("\nTo use this token:")
    print("1. Open browser developer tools")
    print("2. Go to Application/Storage > Local Storage > http://localhost:3000")
    print("3. Add key 'token' with the above value")
    print("4. Refresh the page")
