# Makefile for E-commerce Project

# Use bash for all commands
SHELL := /bin/bash

.PHONY: help setup start stop up down rebuild logs sync test e2e shell clean dump restore

help:
	@echo "Usage: make [target]"
	@echo ""
	@echo "Targets:"
	@echo "  help        Show this help message."
	@echo "  setup       Set up the development environment."
	@echo "  start       Start all services using start script."
	@echo "  stop        Stop all services using stop script."
	@echo "  up          Start all services in the background."
	@echo "  down        Stop all services."
	@echo "  rebuild     Stop, remove containers, and rebuild all services."
	@echo "  logs        Follow logs of all services. Use 'make logs s=<service>' for a specific service."
	@echo "  sync        Install/update backend dependencies, including dev extras."
	@echo "  test        Sync environment and run backend Python tests locally."
	@echo "  e2e         Sync environment and run e2e tests locally."
	@echo "  shell       Get a shell into a running service. Use 'make shell s=<service>' (e.g., api, db)."
	@echo "  clean       Remove temporary files like __pycache__ and .pytest_cache."
	@echo "  dump        Dump the database to data/ directory"
	@echo "  restore     Restore the database. Usage: make restore DUMP_FILE=<path_to_sql_dump_file>"

setup:
	@echo "Setting up development environment..."
	./scripts/setup.sh

start:
	@echo "Starting services..."
	./scripts/start.sh

stop:
	@echo "Stopping services..."
	./scripts/stop.sh

up:
	@echo "Starting all services..."
	docker compose up -d

down:
	@echo "Stopping all services..."
	docker compose down

rebuild:
	@echo "Rebuilding all services..."
	docker compose down
	docker compose build --no-cache
	docker compose up -d

logs:
	@echo "Following logs... (Press Ctrl+C to stop)"
	docker compose logs -f $(s)

sync:
	@echo "Installing/syncing backend dev environment..."
	(cd backend && uv pip install -e ".[dev]")

test: sync
	@echo "==================================Installing dependencies==================================="
	(cd backend && uv pip install -e ".[dev]")
	@echo "======================Dropping and Creating all tables in test database====================="
	(cd backend && TESTING=1 uv run python tests/setup_test_env.py)
	@echo "======================Running backend unit tests parallelly with uv========================="
	(cd backend && TESTING=1 uv run python -m pytest tests/unittest --tb=short -n auto --timeout=300)
	@echo "======================Running backend e2e tests locally with uv============================"
	(cd backend && TESTING=1 uv run python -m pytest tests/e2e -n 1 --tb=short)
	@echo "======================Backend tests completed=============================================="

# Example: make shell s=api
shell:
	@echo "Opening shell in service: $(s)..."
	docker compose exec $(s) bash

clean:
	@echo "Cleaning up temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +


dump:
	@echo "Dumping database..."
	./scripts/dump_db.sh

restore:
	@echo "Restoring database from $(DUMP_FILE)..."
	./scripts/restore_db.sh $(DUMP_FILE)
