# E-commerce Platform

A comprehensive SaaS platform for AI-powered product video generation and e-commerce automation. Integrates with Shopify for product synchronization, generates professional media content using AI, and publishes content back to e-commerce platforms.

## 🚀 Quick Start

### 🎯 One-Command Startup (Recommended)

```bash
./start.sh
```

This script will:

- ✅ Stop any conflicting processes on ports 3000, 8123, etc.
- 🧹 Clean up existing Docker containers
- 🐳 Start the full Docker Compose stack with restored database
- 📋 Display all service URLs

### 🛑 Clean Shutdown

```bash
./stop.sh
```

### Prerequisites

- Docker and Docker Compose
- Python 3.11+
- PostgreSQL 15+
- Redis 7+

### Manual Setup (Alternative)

#### 1. <PERSON>lone and Setup

```bash
git clone <repository-url>
cd e-commerce
cp .env.example .env
# Edit .env with your configuration
```

#### 2. Start Services

```bash
# Start all services
docker-compose up -d

# Check service health
docker-compose ps
```

### 3. Access the Application

- **Web Dashboard**: http://localhost:3000
- **API Documentation**: http://localhost:8123/docs
- **Airbyte UI**: http://localhost:8080
- **Grafana**: http://localhost:3001

## 📋 Services

| Service        | Port | Purpose                                         |
| -------------- | ---- | ----------------------------------------------- |
| **API Server** | 8123 | REST API with authentication and business logic |
| **Worker**     | 9091 | Background task processing (Celery)             |
| **Airbyte**    | 8080 | Data synchronization and ETL                    |
| **PostgreSQL** | 5432 | Primary database                                |
| **Redis**      | 6379 | Caching and task queuing                        |
| **Frontend**   | 3000 | Web dashboard (Next.js)                         |
| **Grafana**    | 3001 | Monitoring dashboards                           |

## 🔧 Configuration

### Environment Variables

Create a `.env` file with:

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/ecommerce_db

# Redis
REDIS_URL=redis://localhost:6379

# Airbyte
AIRBYTE_API_URL=http://localhost:8001
AIRBYTE_USERNAME=airbyte
AIRBYTE_PASSWORD=password

# Shopify (for development)
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
SHOPIFY_WEBHOOK_SECRET=your_webhook_secret

# AI Providers
GOOGLE_VEO_API_KEY=your_veo_key
BANANA_API_KEY=your_banana_key
```

## 📚 Documentation

Detailed documentation is available in the `docs/` folder:

- **[Backend Architecture Documentation](docs/index.html)** - A comprehensive guide to the backend system architecture.
- **[Monitoring Architecture](docs/monitoring-architecture.md)** - Comprehensive monitoring setup
- **[Sync Flow Documentation](docs/sync-flow-documentation.md)** - Complete system architecture and flows

## 🛠️ Development

### Running Tests

```bash
# Unit tests
cd backend && python -m pytest tests/ -v

# Integration tests
docker-compose -f docker-compose.test.yml up -d
cd backend && python -m pytest tests/integration/ -v
```

### Code Quality

```bash
# Linting
cd backend && flake8 src/

# Type checking
cd backend && mypy src/
```

## 🔒 Security

- JWT-based authentication
- HMAC webhook validation
- Rate limiting and DDoS protection
- Encrypted sensitive data storage
- Environment-based secrets management

## 📊 Monitoring

Access monitoring dashboards:

- **Grafana**: http://localhost:3001 (admin/admin)
- **API Metrics**: http://localhost:8123/metrics
- **Worker Metrics**: http://localhost:9091/metrics

## 🆘 Troubleshooting

### Common Issues

1. **Services won't start**

   ```bash
   docker-compose logs <service-name>
   ```

2. **Database connection issues**

   ```bash
   docker-compose exec postgres psql -U user -d ecommerce_db
   ```

3. **Airbyte setup problems**
   - Check Airbyte logs: `docker-compose logs airbyte`
   - Verify configuration in Airbyte UI

### Health Checks

```bash
# API health
curl http://localhost:8123/health

# Worker health
curl http://localhost:9091/health

# Database connectivity
docker-compose exec postgres pg_isready
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Run the test suite
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

- **Issues**: Create an issue in the repository
- **Documentation**: Check the `docs/` folder
- **Logs**: Use `docker-compose logs` for debugging
