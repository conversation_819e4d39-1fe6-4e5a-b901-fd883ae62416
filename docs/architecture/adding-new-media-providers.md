# Adding New Media Providers

This guide explains how to add new media providers to the system following the KISS (Keep It Simple, Stupid) principle. The process has been streamlined to require minimal code changes and configuration.

## Overview

The media provider system supports three types of providers:
- **Text Providers**: Generate text content (descriptions, marketing copy, etc.)
- **Image Providers**: Generate product images and visual content
- **Video Providers**: Generate product videos and promotional content

## Quick Start Checklist

To add a new provider, you need to:

1. ✅ Create a provider configuration file
2. ✅ Implement the provider class
3. ✅ Register the provider in the manager
4. ✅ Test the provider

That's it! No complex mappings, translations, or custom configurations needed.

## Step 1: Create Provider Configuration

Create a JSON configuration file in `backend/src/modules/media/providers/configs/`:

### Example: `my_provider.json`

```json
{
  "type": "text",
  "api_key": "${MY_PROVIDER_API_KEY}",
  "timeout": 120,
  "model": "my-model-v1",
  "config_schema": {
    "temperature": {
      "type": "number",
      "min": 0,
      "max": 2,
      "default": 1.0,
      "description": "Controls randomness in generation"
    },
    "max_tokens": {
      "type": "integer",
      "min": 1,
      "max": 4000,
      "default": 1000,
      "description": "Maximum number of tokens to generate"
    },
    "style": {
      "type": "string",
      "enum": ["casual", "formal", "creative"],
      "default": "casual",
      "description": "Writing style for content generation"
    }
  }
}
```

### Configuration Schema Rules

- **Use official API parameters only** - No custom mappings
- **Include type, min/max, default, description** for each parameter
- **Use environment variables** for API keys: `${PROVIDER_API_KEY}`
- **Set reasonable timeouts** based on provider response times
- **Follow JSON schema standards** for parameter definitions

## Step 2: Implement Provider Class

Create your provider class in the appropriate directory:
- Text: `backend/src/modules/media/providers/text/`
- Image: `backend/src/modules/media/providers/image/`
- Video: `backend/src/modules/media/providers/video/`

### Example: Text Provider

```python
"""
My Provider for text generation.
"""

import logging
from typing import Dict, List, Optional, Any

from ..base import TextProvider
from ..config import ProviderConfig
from ...schemas import ProviderMediaRequest, ProviderMediaResult

logger = logging.getLogger(__name__)


class MyProvider(TextProvider):
    """My provider for text generation."""

    def __init__(self):
        self.client = None
        self.config: Optional[ProviderConfig] = None
        self.model = None

    @property
    def provider_name(self) -> str:
        return "my_provider"

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the provider with configuration."""
        try:
            self.config = config
            self.model = config.model
            
            # Initialize your API client here
            # self.client = MyProviderClient(api_key=config.api_key)
            
            logger.info(f"Successfully initialized {self.provider_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider_name}: {e}")
            return False

    async def generate_content(self, request: ProviderMediaRequest) -> List[ProviderMediaResult]:
        """Generate text content using the provider."""
        try:
            # Get configuration parameters from request
            provider_config = getattr(request, 'custom_config', {}) or {}
            
            # Extract parameters with defaults from config schema
            temperature = provider_config.get('temperature', 1.0)
            max_tokens = provider_config.get('max_tokens', 1000)
            style = provider_config.get('style', 'casual')
            
            # Generate content using your provider's API
            # response = await self.client.generate(
            #     prompt=request.prompt,
            #     temperature=temperature,
            #     max_tokens=max_tokens,
            #     style=style
            # )
            
            # Return results
            return [
                ProviderMediaResult(
                    content="Generated text content here",
                    media_type="text",
                    provider_name=self.provider_name,
                    metadata={
                        "model": self.model,
                        "temperature": temperature,
                        "max_tokens": max_tokens,
                        "style": style
                    }
                )
            ]
            
        except Exception as e:
            logger.exception(f"Error generating content with {self.provider_name}: {e}")
            raise
```

## Step 3: Register Provider in Manager

Add your provider to the manager in `backend/src/modules/media/providers/manager.py`:

### 1. Import your provider class:

```python
from .text.my_provider import MyProvider  # Add this import
```

### 2. Add to provider class mapping:

```python
TEXT_PROVIDER_CLASSES = {
    "gemini": GeminiProvider,
    "my_provider": MyProvider,  # Add this line
    "example_text": ExampleTextProvider,
}
```

That's it! The system will automatically:
- Load your configuration
- Initialize your provider
- Make it available via the API
- Handle configuration overrides

## Step 4: Test Your Provider

### 1. Add environment variable:

```bash
export MY_PROVIDER_API_KEY="your-api-key-here"
```

### 2. Test configuration loading:

```python
# Test script
import asyncio
from modules.media.providers.manager import get_all_providers

async def test_provider():
    providers = await get_all_providers()
    if 'my_provider' in providers:
        print("✅ Provider loaded successfully!")
    else:
        print("❌ Provider not found")

asyncio.run(test_provider())
```

### 3. Test content generation:

Use the media API endpoints to test your provider:

```bash
curl -X POST "http://localhost:8000/api/media/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Generate a product description",
    "media_type": "text",
    "provider_name": "my_provider",
    "custom_config": {
      "temperature": 0.8,
      "max_tokens": 500,
      "style": "creative"
    }
  }'
```

## Configuration Best Practices

### 1. Use Official API Parameters
- Always use the exact parameter names from the provider's official API
- Don't create custom mappings or translations
- Include all available parameters that make sense for e-commerce

### 2. Provide Good Defaults
- Set defaults that work well for e-commerce use cases
- Use conservative values for safety (e.g., lower temperature for consistency)
- Include helpful descriptions for each parameter

### 3. Handle Errors Gracefully
- Always wrap API calls in try-catch blocks
- Log errors with sufficient detail for debugging
- Return meaningful error messages to users

### 4. Follow Naming Conventions
- Provider names: lowercase with underscores (e.g., `my_provider`)
- Class names: PascalCase with "Provider" suffix (e.g., `MyProvider`)
- File names: match provider names (e.g., `my_provider.py`)

## Environment Variables

Add your provider's environment variables to:
- `.env.example` (with placeholder values)
- Your local `.env` file (with real values)
- Production environment configuration

Example:
```bash
# My Provider Configuration
MY_PROVIDER_API_KEY=your-api-key-here
MY_PROVIDER_BASE_URL=https://api.myprovider.com/v1
```

## Troubleshooting

### Provider Not Loading
1. Check that the configuration file exists and is valid JSON
2. Verify the provider class is imported in `manager.py`
3. Ensure the provider name matches between config file and class mapping

### Configuration Not Working
1. Verify parameter names match the provider's official API
2. Check that default values are within valid ranges
3. Ensure environment variables are set correctly

### API Errors
1. Check API key is valid and has necessary permissions
2. Verify network connectivity to provider's API
3. Review provider's API documentation for rate limits and requirements

## Next Steps

After adding your provider:
1. Update the main `providers_config.json` if needed
2. Add unit tests for your provider
3. Update API documentation
4. Consider adding provider-specific features or optimizations

For more information, see:
- [Media Module Documentation](../modules/media-module.md)
- [System Architecture](system-architecture.md)
- [API Documentation](http://localhost:8000/docs)
