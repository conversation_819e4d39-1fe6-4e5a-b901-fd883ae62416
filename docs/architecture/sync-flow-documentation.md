# ProductVideo Platform - Complete Architecture Documentation

## Overview

This document describes the comprehensive architecture of the ProductVideo platform, a SaaS solution that automatically generates professional product videos for e-commerce stores using AI. The platform integrates with Shopify for product synchronization, generates multi-modal media content (video/image/voice), and publishes content back to e-commerce platforms.

## System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Web Dashboard]
        B[API Clients]
        C[Shopify Webhooks]
    end

    subgraph "API Layer"
        D[FastAPI Server]
        E[Authentication & Authorization]
        F[Rate Limiting & Middleware]
    end

    subgraph "Processing Layer"
        G[Celery Workers]
        H[Task Queue - Redis]
        I[Background Processors]
    end

    subgraph "Data Layer"
        J[(PostgreSQL)]
        K[(Redis Cache)]
        L[Object Storage - S3/R2]
    end

    subgraph "External Services"
        M[Airbyte ETL]
        N[AI Providers]
        O[Shopify API]
    end

    A --> D
    B --> D
    C --> D
    D --> G
    D --> J
    G --> H
    G --> I
    I --> J
    I --> K
    I --> L
    I --> M
    I --> N
    I --> O
```

## Core Components

### API Layer Components

- **FastAPI Server** (`backend/src/servers/api/`): REST API endpoints with automatic OpenAPI docs
- **Authentication** (`backend/src/modules/auth/`): JWT-based auth with role-based access
- **Rate Limiting**: Built-in rate limiting and request throttling
- **Middleware**: Request logging, CORS, error handling

### Processing Layer Components

- **Celery Workers** (`backend/src/servers/worker/`): Distributed task processing
- **Task Queue**: Redis-based job queuing with priorities
- **Background Processors**:
  - `AirbyteSyncProcessor`: Processes Airbyte raw data → production tables
  - `MediaGenerationProcessor`: AI media generation (video/image/voice)
  - `MediaPushProcessor`: Platform media upload with retry logic
  - `AnalyticsProcessor`: Event processing and analytics aggregation

### Data Layer Components

- **PostgreSQL**: Primary database with async SQLAlchemy
- **Redis**: Caching, sessions, task queuing, distributed locks
- **Object Storage**: S3/CloudFlare R2 for media file storage
- **Airbyte**: ETL pipeline for Shopify data synchronization

### External Integrations

- **Shopify API**: OAuth, webhooks, GraphQL API for product/media management
- **AI Providers**: Google Veo 3 (video), Banana (images), Mock (testing)
- **Airbyte OSS**: Modern open-source data integration platform (v1.0.0)
  - Dedicated databases for Airbyte metadata and Temporal workflows
  - Full Temporal integration for reliable workflow orchestration
  - Production-ready setup following official best practices
  - Web UI at http://localhost:8080, API at http://localhost:8001
- **Monitoring**: Prometheus metrics, structured logging

## Complete Data Flow Architecture

```mermaid
graph TB
    subgraph "Entry Points"
        A1[Web Dashboard UI]
        A2[REST API Calls]
        A3[Shopify Webhooks]
    end

    subgraph "API Processing"
        B1[FastAPI Routes]
        B2[Authentication]
        B3[Input Validation]
        B4[Business Logic]
    end

    subgraph "Background Processing"
        C1[Celery Task Queue]
        C2[Worker Processes]
        C3[Retry Logic]
        C4[Error Handling]
    end

    subgraph "Data Processing"
        D1[Airbyte ETL]
        D2[Raw Tables]
        D3[Data Transformation]
        D4[Production Tables]
    end

    subgraph "AI Processing"
        E1[Media Generation]
        E2[Content Storage]
        E3[Platform Upload]
        E4[Analytics]
    end

    subgraph "External Services"
        F1[Shopify API]
        F2[AI Providers]
        F3[Object Storage]
        F4[Monitoring]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> E1
    E1 --> E2
    E2 --> E3
    E3 --> F1
    E4 --> F4
    C2 --> F2
    E2 --> F3
```

## Complete Flow Documentation

### 1. Store Onboarding & Setup Flow

**API Trigger**: `POST /api/stores` (Store creation endpoint)

```mermaid
graph TD
    A[POST /api/stores] --> B[Validate store data]
    B --> C[Create Store record in DB]
    C --> D[Setup Shopify OAuth]
    D --> E[Auto-create Airbyte infrastructure]
    E --> F[Create Shopify source connection]
    F --> G[Create PostgreSQL destination]
    G --> H[Setup sync catalog & connection]
    H --> I[Update store with Airbyte IDs]
    I --> J[Return store configuration]
    J --> K[Setup webhook subscriptions]
```

**Key Components:**

- `stores/router.py`: API endpoint for store creation
- `stores/service.py`: Business logic for store setup
- `airbyte_service.py`: Automated Airbyte resource creation
- Database models: Store, Airbyte connection metadata

### 2. Product Data Synchronization Flow

#### **Automatic Sync (Webhook-Driven)**

**API Trigger**: Shopify webhook → `POST /api/webhooks/shopify/products/*`

```mermaid
graph TD
    A[Shopify webhook received] --> B[Validate webhook signature]
    B --> C[Store webhook in WebhookEvent model]
    C --> D[Enqueue process_webhook_event task]
    D --> E[AirbyteSyncProcessor.process_business_logic]
    E --> F{Should trigger sync?}
    F -->|Yes| G[Enqueue trigger_airbyte_sync task]
    F -->|No| H[Skip sync - cooldown active]
    G --> I[Airbyte sync job started]
    I --> J[Monitor job completion]
    J --> K[Enqueue consumer_upsert task]
    K --> L[AirbyteSyncProcessor.process_airbyte_products]
    L --> M[Transform & upsert to production tables]
    M --> N[Update sync checkpoints]
    N --> O[Trigger business logic]
    O --> P[Video generation for new products]
```

#### **Manual Sync**

**API Trigger**: `POST /api/sync/{store_id}` (Manual sync endpoint)

```mermaid
graph TD
    A[POST /api/sync/{store_id}] --> B[Validate user permissions]
    B --> C[Check sync cooldown & locks]
    C --> D[Enqueue trigger_airbyte_sync task]
    D --> E[Airbyte sync job execution]
    E --> F[Monitor job progress]
    F --> G[Process raw data to production]
    G --> H[Return sync results & metrics]
```

### 2. Automatic Sync Flow (Webhook-Driven)

```mermaid
graph TD
    A[Shopify webhook received] --> B[Store webhook in WebhookEvent model]
    B --> C[Worker processes webhook]
    C --> D{Should trigger sync?}
    D -->|Yes| E[Trigger Airbyte sync job]
    D -->|No| F[Skip sync]
    E --> G[Monitor Airbyte job status]
    G --> H[Job completed?]
    H -->|No| G
    H -->|Yes| I[Process raw data to production]
    I --> J[Update sync status]
```

**Key Components:**

- `sync/models.py`: WebhookEvent, SyncJob models
- `worker/tasks.py`: process_webhook_event, trigger_airbyte_sync, monitor_airbyte_job
- `airbyte_service.py`: Sync triggering and status monitoring

### 3. AI Media Generation Flow

**API Trigger**: `POST /api/media/generate` (Media generation endpoint)

```mermaid
graph TD
    A[POST /api/media/generate] --> B[Validate user permissions]
    B --> C[Check product ownership & limits]
    C --> D[Create MediaJob in database]
    D --> E[Enqueue generate_media task]
    E --> F[MediaGenerationProcessor.process]
    F --> G[Select AI provider by media type]
    G --> H{Video Generation}
    H --> I[Veo3 API call with retry logic]
    H --> J{Image Generation}
    J --> K[Banana API call with retry logic]
    H --> L{Voice Generation}
    L --> M[TTS synthesis with retry logic]
    I --> N[Process & transcode video]
    K --> O[Upload image to storage]
    M --> P[Upload voice to storage]
    N --> Q[Update MediaVariant with URLs]
    O --> Q
    P --> Q
    Q --> R[Update job status to COMPLETED]
    R --> S[Return generation results]
```

**Key Components:**

- `media/router.py`: API endpoints for media generation
- `MediaGenerationProcessor`: Handles AI provider selection and retry logic
- `video_transcoding_service.py`: Video processing and HLS generation
- `storage_service.py`: S3/CloudFlare R2 file uploads

### 4. Media Publishing Flow

**API Trigger**: `POST /api/media/push` (Media publishing endpoint)

```mermaid
graph TD
    A[POST /api/media/push] --> B[Validate variant ownership]
    B --> C[Check publishing permissions]
    C --> D[Enqueue push_to_shopify task]
    D --> E[MediaPushProcessor.process]
    E --> F[Get platform-specific service]
    F --> G{Shopify Publishing}
    G --> H[GraphQL productCreateMedia mutation]
    G --> I[Handle rate limiting & retries]
    G --> J[Update variant with media_id]
    J --> K[Update push status to COMPLETED]
    K --> L[Return publishing results]
    I --> M[Retry with exponential backoff]
    M --> G
```

**Key Components:**

- `media/router.py`: Publishing API endpoints
- `MediaPushProcessor`: Platform-specific publishing with retry logic
- `shopify/media_service.py`: Shopify GraphQL API integration
- Rate limiting and error recovery mechanisms

### 5. Analytics Processing Flow

**API Trigger**: Internal events → `analytics/event_service.py`

```mermaid
graph TD
    A[User action triggers event] --> B[analytics_event_service.ingest_event]
    B --> C[Validate event data]
    C --> D[Enqueue process_analytics_event task]
    D --> E[AnalyticsProcessor.process]
    E --> F[Batch processing (up to 50 events)]
    F --> G[Event validation & transformation]
    G --> H[Database upsert with conflict resolution]
    H --> I[Update analytics aggregates]
    I --> J[Return processing results]
    J --> K[Prometheus metrics update]
```

**Key Components:**

- `analytics/event_service.py`: Event ingestion and validation
- `AnalyticsProcessor`: Batch processing with retry logic
- Database models for analytics data storage
- Prometheus metrics integration

### 4. Data Processing Flow

```mermaid
graph TD
    A[Raw data in Airbyte tables] --> B[consumer_upsert task]
    B --> C[AirbyteSyncProcessor.process_airbyte_*]
    C --> D[Extract and transform Shopify data]
    D --> E[Validate data integrity]
    E --> F[Upsert to production tables]
    F --> G[Update sync checkpoints]
    G --> H[Log processing metrics]
    H --> I[Trigger business logic]
    I --> J[Video generation for new products]
```

**Key Components:**

- `airbyte_sync_processor.py`: Processes raw Airbyte data into production tables
- `products/models.py`: Product and variant data models
- `sync/models.py`: SyncCheckpoint for progress tracking
- Business logic integration for video generation and analytics

### 5. Syncer System Architecture

#### **Overview**

The system uses a "Syncer" architecture to transform raw data from Airbyte into the production database tables. Each syncer is a class responsible for a specific data entity (e.g., products, variants). This approach provides a clean, maintainable, and extensible way to manage the data transformation logic.

```mermaid
graph TD
    subgraph "Airbyte Raw Tables"
        A1[products] --> M1[ProductsSyncer]
        A2[product_variants] --> M2[ProductVariantsSyncer]
        A3[product_images] --> M3[ProductImageSyncer]
        A4[inventory_levels] --> M4[InventoryLevelSyncer]
    end

    subgraph "Syncer Classes"
        M1 --> P1[Product Table]
        M2 --> P2[ProductVariant Table]
        M3 --> P3[ProductImage Table]
        M4 --> P4[InventoryLevel Table]
    end

    subgraph "Production Database"
        P1
        P2
        P3
        P4
    end

    M1 --> R1[Resolve Relationships]
    M2 --> R2[Link to Products]
    M3 --> R3[Link to Products]
    M4 --> R4[Link via inventory_item_id]
```

#### **Syncer Architecture Benefits**

- **✅ Single Responsibility**: Each syncer handles one entity type.
- **✅ Extensible**: Easy to add new entity types and syncers.
- **✅ Testable**: Isolated transformation logic for unit testing.

#### **Syncer Directory Structure**

```
backend/src/modules/sync/platforms/shopify/syncers/
├── __init__.py
├── products_syncer.py
├── product_variants_syncer.py
├── product_images_syncer.py
└── inventory_levels_syncer.py
```

#### **ProductsSyncer Example**

```python
class ProductsSyncer(BaseSyncer):
    """
    Syncer for Shopify products.
    """

    def __init__(self, airbyte_engine=None, platform_config=None):
        super().__init__(airbyte_engine, platform_config)
        self.entity_type = 'products'

    async def sync(self, db: AsyncSession, store_id: int) -> Dict[str, Any]:
        """
        Sync products for a specific store with comprehensive statistics tracking.
        """
        # ... (implementation details) ...

    def get_select_query(self, store, last_sync_time: datetime, offset: int, batch_size: int) -> str:
        """Generate the SELECT query for fetching products from Airbyte."""
        return """
        SELECT
            id::text as external_id,
            title as title,
            body_html as description,
            -- ... other fields
        FROM products
        WHERE _airbyte_extracted_at > :last_sync_time
          AND shop_url = :shop_url
        ORDER BY id::text
        LIMIT :batch_size OFFSET :offset
        """

    def get_insert_query(self) -> str:
        """Generate the INSERT query for bulk inserting products."""
        return """
        INSERT INTO products (...)
        VALUES %s
        ON CONFLICT (external_id) DO UPDATE SET ...
        """
```

#### **Database Schema Evolution**

The mapper system supports the evolution from a problematic single-table approach to a proper relational structure:

**Before (Problematic):**
```sql
-- Single table with JSON fields (data misrepresentation)
products (
    id VARCHAR PRIMARY KEY,
    store_id UUID,
    title VARCHAR,
    variants JSONB,  -- ❌ Misrepresented as JSON
    images JSONB,    -- ❌ Misrepresented as JSON
    inventory JSONB, -- ❌ Misrepresented as JSON
    -- ... other fields
)
```

**After (Proper Relational):**
```sql
-- Proper relational structure
products (
    id VARCHAR PRIMARY KEY,
    store_id UUID,
    title VARCHAR,
    description TEXT,
    tags JSONB,
    options JSONB,
    -- ... core product fields
)

product_variants (
    id VARCHAR PRIMARY KEY,
    product_id INTEGER REFERENCES products(id),  -- ✅ Proper relationship
    title VARCHAR,
    sku VARCHAR,
    price DECIMAL,
    quantity INTEGER,
    -- ... all variant-specific fields
)

product_images (
    id VARCHAR PRIMARY KEY,
    product_id INTEGER REFERENCES products(id),  -- ✅ Proper relationship
    variant_id INTEGER REFERENCES product_variants(id),  -- Optional
    src VARCHAR,
    alt VARCHAR,
    width INTEGER,
    height INTEGER,
    -- ... image metadata
)

inventory_levels (
    id VARCHAR PRIMARY KEY,
    inventory_item_id VARCHAR,  -- Links to variant
    location_id VARCHAR,
    available INTEGER,
    -- ... inventory data
)
```

#### **Key Advantages of the New Architecture**

1. **✅ Data Integrity**: Proper foreign key relationships prevent orphaned records
2. **✅ Query Performance**: Efficient joins and indexing on individual tables
3. **✅ Data Consistency**: No misrepresentation of complex data as JSON
4. **✅ Scalability**: Separate tables allow for better partitioning and optimization
5. **✅ Maintainability**: Clear separation of concerns between different entity types
6. **✅ Extensibility**: Easy to add new entity types without schema changes
7. **✅ Analytics**: Better support for complex queries and reporting
8. **✅ Validation**: Strong typing and constraints at the database level

#### **Migration Strategy**

The mapper system supports seamless migration from the old approach:

1. **Backward Compatibility**: Mappers can handle both old and new data formats
2. **Gradual Migration**: Can migrate one entity type at a time
3. **Data Validation**: Comprehensive validation ensures data integrity during migration
4. **Rollback Support**: Easy to rollback if issues are discovered

This mapper architecture provides a solid foundation for reliable, scalable, and maintainable data processing in the ProductVideo platform.

## Component Interactions

### Airbyte Service Integration

- **Setup**: Called during store creation to establish ETL pipeline
- **Triggering**: Manual and automatic sync initiation
- **Monitoring**: Job status polling and completion detection
- **Configuration**: Uses JSON templates for source/destination setup

### Scalable Multi-Tenant Architecture

- **Shared Tables**: All stores use the same table names (e.g., `raw.products`, `raw.product_variants`)
- **Store Isolation**: `store_id` column in all tables for data separation
- **Compound Primary Keys**: `store_id` + `id` for proper deduplication
- **Benefits**: Reduced table count, better performance, easier maintenance

**Before (Not Scalable)**:

- Store 1: `shopify_1_products`, `shopify_1_product_variants`
- Store 2: `shopify_2_products`, `shopify_2_product_variants`
- **Problem**: 1000 stores × 4 tables = 4000+ tables

**After (Scalable)**:

- All stores: `raw.products`, `raw.product_variants` with `store_id` column
- **Benefit**: 4 shared tables total, regardless of store count

### Worker Server & Task Processing

#### **Celery Task Architecture**

```mermaid
graph TD
    subgraph "Task Producers"
        A1[API Endpoints]
        A2[Webhook Handlers]
        A3[Scheduled Tasks]
    end

    subgraph "Task Queue (Redis)"
        B1[High Priority Queue]
        B2[Default Priority Queue]
        B3[Low Priority Queue]
    end

    subgraph "Task Consumers"
        C1[Worker Pool 1]
        C2[Worker Pool 2]
        C3[Worker Pool N]
    end

    subgraph "Task Processors"
        D1[AirbyteSyncProcessor]
        D2[MediaGenerationProcessor]
        D3[MediaPushProcessor]
        D4[AnalyticsProcessor]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C1 --> D4
```

#### **Task Definitions**

```python
# Sync Tasks
process_webhook_event     # Webhook processing pipeline
trigger_airbyte_sync      # Airbyte job orchestration
consumer_upsert           # Raw data processing
monitor_airbyte_job       # Job status monitoring

# Media Tasks
generate_media           # AI content generation
push_to_shopify          # Platform publishing
process_analytics_event  # Analytics processing

# Utility Tasks
cleanup_old_data         # Data maintenance
health_check            # System monitoring
```

#### **Processor Responsibilities**

- **AirbyteSyncProcessor**:

  - Processes raw Airbyte data into production tables
  - Handles Shopify product/variant transformations
  - Manages sync checkpoints and deduplication
  - Triggers business logic for new products

- **MediaGenerationProcessor**:

  - Orchestrates AI provider selection (Veo3/Banana/TTS)
  - Handles retry logic and error recovery
  - Manages video transcoding and storage uploads
  - Updates generation job status and metrics

- **MediaPushProcessor**:

  - Manages platform-specific publishing (Shopify)
  - Handles rate limiting and API quotas
  - Provides retry logic for failed uploads
  - Updates publishing status and audit trails

- **AnalyticsProcessor**:
  - Processes user interaction events
  - Handles batch processing for performance
  - Manages analytics data aggregation
  - Provides real-time metrics and insights

### Database Models & Schema

#### **Core Business Models**

```sql
-- User Management
users (
    id UUID PRIMARY KEY,
    email VARCHAR UNIQUE,
    hashed_password VARCHAR,
    is_active BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Multi-tenant Stores
stores (
    id UUID PRIMARY KEY,
    owner_id UUID REFERENCES users(id),
    shop_domain VARCHAR UNIQUE,
    admin_access_token VARCHAR, -- Encrypted
    platform VARCHAR, -- shopify, woocommerce, etc.
    is_active BOOLEAN,
    -- Airbyte integration fields
    airbyte_source_id VARCHAR,
    airbyte_destination_id VARCHAR,
    airbyte_connection_id VARCHAR,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Product Catalog
products (
    id VARCHAR PRIMARY KEY, -- Shopify product ID
    store_id UUID REFERENCES stores(id),
    title VARCHAR,
    description TEXT,
    handle VARCHAR,
    product_type VARCHAR,
    vendor VARCHAR,
    status VARCHAR,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    synced_at TIMESTAMP
)

-- Product Variants
product_variants (
    id VARCHAR PRIMARY KEY, -- Shopify variant ID
    store_id UUID REFERENCES stores(id),
    product_id VARCHAR REFERENCES products(id),
    title VARCHAR,
    sku VARCHAR,
    price DECIMAL,
    compare_at_price DECIMAL,
    inventory_quantity INTEGER,
    weight DECIMAL,
    weight_unit VARCHAR,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### **Media Generation Models**

```sql
-- Media Generation Jobs
media_jobs (
    id SERIAL PRIMARY KEY,
    tenant_id UUID REFERENCES users(id),
    product_id VARCHAR,
    status VARCHAR, -- pending, processing, completed, failed
    media_type VARCHAR, -- video, image, voice
    script TEXT,
    custom_config JSONB,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Generated Media Variants
media_variants (
    id SERIAL PRIMARY KEY,
    job_id INTEGER REFERENCES media_jobs(id),
    tenant_id UUID REFERENCES users(id),
    product_id VARCHAR,
    variant_name VARCHAR,
    status VARCHAR, -- generating, ready, failed
    -- Media URLs
    video_url VARCHAR,
    image_url VARCHAR,
    voice_url VARCHAR,
    thumbnail_url VARCHAR,
    -- Processing metadata
    provider_media_id VARCHAR,
    generation_params JSONB,
    -- Publishing status
    push_status VARCHAR, -- pending, pushing, completed, failed
    media_id VARCHAR, -- Platform media ID
    pushed_at TIMESTAMP,
    push_error_message TEXT,
    -- User interaction
    is_favorite BOOLEAN DEFAULT FALSE,
    user_rating INTEGER,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### **Sync & Processing Models**

```sql
-- Webhook Events
webhook_events (
    id SERIAL PRIMARY KEY,
    store_id UUID REFERENCES stores(id),
    topic VARCHAR,
    payload JSONB,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP,
    created_at TIMESTAMP
)

-- Airbyte Sync Jobs
sync_jobs (
    id SERIAL PRIMARY KEY,
    store_id UUID REFERENCES stores(id),
    airbyte_job_id VARCHAR,
    status VARCHAR, -- running, succeeded, failed
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    records_synced INTEGER,
    error_message TEXT,
    created_at TIMESTAMP
)

-- Sync Checkpoints
sync_checkpoints (
    id SERIAL PRIMARY KEY,
    store_id UUID REFERENCES stores(id),
    entity_type VARCHAR, -- products, variants, orders
    last_synced_id VARCHAR,
    last_synced_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE(store_id, entity_type)
)

-- Raw Airbyte Staging Tables
raw_products (
    _airbyte_ab_id VARCHAR PRIMARY KEY,
    _airbyte_emitted_at TIMESTAMP,
    _airbyte_normalized_at TIMESTAMP,
    store_id UUID,
    id VARCHAR,
    title VARCHAR,
    -- ... other Shopify fields
)

raw_product_variants (
    _airbyte_ab_id VARCHAR PRIMARY KEY,
    _airbyte_emitted_at TIMESTAMP,
    _airbyte_normalized_at TIMESTAMP,
    store_id UUID,
    id VARCHAR,
    product_id VARCHAR,
    -- ... other Shopify fields
)
```

#### **Analytics Models**

```sql
-- Analytics Events
analytics_events (
    id SERIAL PRIMARY KEY,
    tenant_id UUID REFERENCES users(id),
    event_type VARCHAR,
    event_data JSONB,
    user_id UUID,
    session_id VARCHAR,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP
)

-- Analytics Aggregates
analytics_aggregates (
    id SERIAL PRIMARY KEY,
    tenant_id UUID REFERENCES users(id),
    metric_name VARCHAR,
    metric_value DECIMAL,
    dimensions JSONB,
    date DATE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE(tenant_id, metric_name, date, dimensions)
)
```

## Key Features

### Concurrency Control

- Per-store Redis locks prevent overlapping sync operations
- Queue-based task processing ensures orderly execution

### Error Handling

- Exponential backoff for failed operations
- Dead letter queues for unrecoverable failures
- Comprehensive logging and metrics

### Scalability

- Horizontal scaling via Celery workers
- Database connection pooling
- Async operations for high throughput

### Monitoring

- Sync job status tracking
- Performance metrics collection
- Real-time progress updates

## Configuration

### Environment Variables

```bash
AIRBYTE_API_URL=https://airbyte.your-domain.com
AIRBYTE_API_KEY=your_airbyte_api_key
AIRBYTE_WORKSPACE_ID=your_workspace_id
REDIS_URL=redis://localhost:6379
DATABASE_URL=postgresql://user:pass@localhost:5432/db
```

### Worker Configuration

- Task routing defined in `worker/config.json`
- Prefetch and concurrency settings
- Queue prioritization

## Complete API Endpoint Reference

### Authentication & User Management (`/auth`)

```http
POST   /register                    # User registration
POST   /login                       # User login
POST   /logout                      # User logout
POST   /refresh                     # Token refresh
GET    /me                          # Get current user
POST   /forgot-password             # Password reset request
POST   /reset-password              # Reset password with token
POST   /verify-email                # Verify email with token
POST   /resend-verification         # Resend verification email
# Plus OAuth endpoints under /oauth
```

### Billing & Tenants (`/billing`)

```http
POST   /tenants                     # Create a new tenant
GET    /tenants/{tenant_id}         # Get tenant details
PUT    /tenants/{tenant_id}         # Update a tenant
GET    /tenants                     # List tenants
POST   /subscriptions               # Create a new subscription
POST   /usage                       # Record metered usage
GET    /tenants/{tenant_id}/dashboard # Get billing dashboard data
POST   /webhooks/stripe             # Handle Stripe webhooks
GET    /tenants/{tenant_id}/credits/balance # Get credit balance
POST   /tenants/{tenant_id}/credits/purchase # Purchase credits
```

### Store Management (`/stores`)

```http
POST   /                            # Create a new store
GET    /                            # List user's stores
GET    /{store_id}                  # Get store details
PUT    /{store_id}                  # Update store settings
DELETE /{store_id}                  # Delete a store
POST   /{store_id}/connect          # Connect or re-connect a store
POST   /{store_id}/disconnect       # Disconnect a store
POST   /{store_id}/test-connection  # Test connection to a store
POST   /test-connection             # Test connection with new credentials
```

### Product & Sync Management (`/products`, `/sync`)

```http
GET    /products                    # List products with pagination and search
GET    /products/{product_id}       # Get product details
POST   /stores/{store_id}/sync/products # Trigger product sync for a store
GET    /stores/{store_id}/sync-progress # Get real-time sync progress
GET    /stores/{store_id}/sync-checkpoints # Get sync checkpoints
GET    /sync/airbyte/platforms      # Get list of supported Airbyte platforms
```

### Media Generation (`/media`)

```http
POST   /generate                    # Generate media for products
GET    /jobs                        # List generation jobs
GET    /jobs/{job_id}               # Get job status & variants
POST   /jobs/{job_id}/cancel        # Cancel a job
POST   /jobs/{job_id}/retry         # Retry a failed job
POST   /push                        # Push variant to a platform
POST   /variants/{variant_id}/regenerate # Regenerate a specific variant
```

### Asset Management (`/assets`)

```http
GET    /                            # Get all assets with pagination and search
POST   /reassign                    # Reassign an asset to a new product
```

### Templates (`/templates`)

```http
GET    /                            # Get available templates
GET    /{template_id}               # Get a specific template
GET    /categories/list             # List all template categories
GET    /recommendations/get         # Get recommended templates
GET    /{template_id}/customization # Get customization options for a template
```

### Analytics (`/analytics`)

```http
POST   /events/ingest               # Ingest a single analytics event
POST   /events/batch                # Ingest a batch of analytics events
GET    /dashboard                   # Get dashboard metrics
GET    /media-analytics             # Get detailed media analytics
GET    /conversion-funnel           # Get conversion funnel analysis
```

### Customer Management (`/customers`)

```http
GET    /                            # Get all customers for user's stores
POST   /                            # Create a new customer
GET    /{customer_id}               # Get a specific customer
PUT    /{customer_id}               # Update a customer
DELETE /{customer_id}               # Delete a customer
```

### Web Scraper (`/scraper`)

```http
GET    /stats                       # Get scraper statistics
GET    /documents                   # Get scraped documents
GET    /jobs                        # Get active scraping jobs
POST   /validate                    # Validate a URL for scraping
POST   /scrape                      # Start scraping a URL
```

## Best Practices

### Development

- Use mock data for testing sync flows
- Implement comprehensive logging
- Test webhook processing thoroughly

### Production

- Monitor queue depths and worker health
- Set up alerts for failed syncs
- Regular cleanup of old sync data

### Performance

- Batch processing for large datasets
- Connection pooling for database operations
- Caching for frequently accessed data

## Troubleshooting

### Common Issues & Troubleshooting

#### **Sync & Data Processing Issues**

- **Airbyte connection failures**: Check API credentials, workspace configuration, and network connectivity
- **Webhook signature validation**: Verify Shopify webhook secrets and HMAC signatures
- **Database deadlocks**: Monitor concurrent operations and implement proper locking strategies
- **Sync checkpoint corruption**: Reset checkpoints and trigger full resync if needed
- **Raw data processing failures**: Check Airbyte schema changes and update transformation logic

#### **Media Generation Issues**

- **AI provider rate limits**: Implement exponential backoff and monitor API quotas
- **Video transcoding failures**: Check FFmpeg installation and storage permissions
- **Storage upload failures**: Verify S3/CloudFlare credentials and bucket permissions
- **Provider fallback not working**: Check provider configurations and API keys
- **Generation timeout**: Increase Celery task timeouts for large media files

#### **Media Publishing Issues**

- **Shopify API rate limits**: Implement intelligent retry logic with proper backoff
- **GraphQL mutation failures**: Check product permissions and media upload limits
- **Platform authentication**: Verify OAuth tokens and refresh expired credentials
- **Media deduplication**: Implement proper duplicate detection and handling
- **Cross-platform publishing**: Ensure platform-specific API implementations

#### **Analytics Processing Issues**

- **Event ingestion failures**: Validate event schemas and implement proper error handling
- **Batch processing timeouts**: Adjust batch sizes and processing timeouts
- **Metrics aggregation errors**: Check database constraints and aggregation logic
- **Real-time dashboard delays**: Optimize query performance and caching strategies

#### **Worker & Queue Issues**

- **Celery task timeouts**: Adjust task timeouts based on operation complexity
- **Queue backlog**: Monitor Redis queue depths and scale worker pools
- **Memory exhaustion**: Implement proper resource limits and garbage collection
- **Database connection pools**: Monitor connection usage and implement proper pooling
- **Redis connectivity**: Check Redis cluster health and failover configurations

#### **Performance Issues**

- **Slow media generation**: Optimize AI provider selection and caching
- **High latency APIs**: Implement response caching and database query optimization
- **Storage bottlenecks**: Use CDN distribution and optimize file serving
- **Database query performance**: Add proper indexing and query optimization
- **Memory leaks**: Monitor application memory usage and implement proper cleanup

### Debugging

- Enable detailed logging in worker tasks
- Use Airbyte UI for ETL pipeline inspection
- Monitor Redis for queue status
- Check database for sync job states

## Future Enhancements

- Multi-platform support beyond Shopify
- Advanced sync scheduling
- Real-time sync status WebSocket updates
- Enhanced error recovery mechanisms
- Sync performance analytics

## Migration Notes

### From Per-Store Tables to Shared Tables

If upgrading from the old per-store table approach:

1. **Backup existing data** from prefixed tables
2. **Run migration script** to copy data to shared tables with `store_id`
3. **Update Airbyte connections** to use new configuration
4. **Test sync operations** with sample stores
5. **Monitor performance** and adjust indexing as needed

### Database Indexes for Shared Tables

```sql
-- Recommended indexes for shared tables
CREATE INDEX idx_products_store_id ON raw.products(store_id);
CREATE INDEX idx_products_store_updated ON raw.products(store_id, updated_at);
CREATE INDEX idx_product_variants_store_id ON raw.product_variants(store_id);
CREATE INDEX idx_product_variants_store_product ON raw.product_variants(store_id, product_id);
```

### Performance Considerations

- **Partitioning**: Consider table partitioning by `store_id` for very large datasets
- **Archiving**: Implement data archiving for old sync records
- **Caching**: Cache frequently accessed product data by store

---

## 📋 **Complete Architecture Summary**

### **🎯 System Overview**

The ProductVideo platform is a comprehensive SaaS solution that provides AI-powered video generation for e-commerce stores. The system integrates seamlessly with Shopify for product synchronization, generates multi-modal content (video/image/voice), and publishes content back to e-commerce platforms with full automation.

### **🏗️ Architecture Highlights**

#### **1. Multi-Layer Architecture**

- **Client Layer**: Web dashboard, API clients, Shopify webhooks
- **API Layer**: FastAPI server with authentication, rate limiting, middleware
- **Processing Layer**: Celery workers with Redis queuing and background processors
- **Data Layer**: PostgreSQL, Redis cache, S3/CloudFlare R2 storage
- **External Services**: Airbyte ETL, AI providers, Shopify API

#### **2. Complete Flow Coverage**

- ✅ **Store Onboarding**: Automated Airbyte setup and OAuth configuration
- ✅ **Data Synchronization**: Webhook-driven and manual sync with Airbyte ETL
- ✅ **AI Media Generation**: Multi-provider support with retry logic and fallback
- ✅ **Content Publishing**: Platform-specific publishing with rate limiting
- ✅ **Analytics Processing**: Event tracking and real-time metrics

#### **3. Enterprise-Grade Features**

- ✅ **Scalability**: Horizontal scaling via Celery workers and database partitioning
- ✅ **Reliability**: Comprehensive retry logic, error recovery, and monitoring
- ✅ **Observability**: Prometheus metrics, structured logging, performance tracking
- ✅ **Security**: JWT authentication, encrypted tokens, rate limiting
- ✅ **Multi-tenancy**: Isolated stores with shared infrastructure

### **🔧 Technical Implementation**

#### **Background Processing**

- **4 Specialized Processors**: AirbyteSync, MediaGeneration, MediaPush, Analytics
- **Task Queue Management**: Redis-based queuing with priority levels
- **Error Handling**: Exponential backoff, dead letter queues, status tracking
- **Monitoring**: Real-time metrics and performance dashboards

#### **Data Architecture**

- **Shared Multi-tenant Tables**: Store isolation via `store_id` columns
- **Raw Data Processing**: Airbyte staging tables with transformation pipelines
- **Production Data**: Normalized tables with proper relationships
- **Analytics Storage**: Event tracking with aggregation capabilities

#### **API Design**

- **RESTful Endpoints**: 30+ endpoints covering all platform features
- **Authentication**: JWT-based with role-based access control
- **Rate Limiting**: Built-in protection against API abuse
- **Documentation**: Auto-generated OpenAPI/Swagger documentation

### **🚀 Key Differentiators**

#### **AI Integration**

- **Multi-Provider Support**: Google Veo 3, Banana, TTS with automatic fallback
- **Content Generation**: Video, image, and voice generation in one platform
- **Quality Assurance**: Retry logic and error recovery for reliable generation

#### **E-Commerce Integration**

- **Shopify Native**: OAuth, webhooks, GraphQL API integration
- **Auto-Publishing**: Seamless content publishing to product galleries
- **Real-time Sync**: Webhook-driven updates with manual sync fallback

#### **Developer Experience**

- **Comprehensive Documentation**: Complete API reference and architecture docs
- **Monitoring Tools**: Built-in metrics and logging for operational visibility
- **Extensible Design**: Plugin architecture for additional platforms and providers

### **📊 Business Impact**

#### **For E-Commerce Stores**

- **Automated Content Creation**: AI-generated videos without technical expertise
- **Multi-Channel Publishing**: Content automatically published to Shopify
- **Performance Analytics**: Real-time insights into content performance
- **Scalable Solution**: Handles stores of any size with consistent performance

#### **For the Platform**

- **SaaS Revenue Model**: Subscription-based with usage tracking
- **Operational Excellence**: Automated workflows with minimal manual intervention
- **Scalable Infrastructure**: Cloud-native design supporting rapid growth
- **Enterprise Features**: Multi-tenancy, monitoring, security, compliance

### **🔮 Future Roadmap**

- **Multi-Platform Support**: WooCommerce, BigCommerce, Magento integration
- **Advanced AI Features**: Custom model training, style transfer, personalization
- **Real-time Collaboration**: Team collaboration and approval workflows
- **Advanced Analytics**: Predictive analytics and recommendation engine
- **Mobile Applications**: iOS/Android apps for content management
- **API Marketplace**: Third-party integrations and white-label solutions

---

## 📚 **Documentation Index**

### **Core Documentation**

- ✅ **Architecture Overview**: Complete system design and component interactions
- ✅ **API Reference**: Comprehensive endpoint documentation with examples
- ✅ **Data Models**: Database schema and relationships
- ✅ **Flow Diagrams**: Visual representation of all system flows

### **Operational Documentation**

- ✅ **Setup & Configuration**: Environment variables and deployment guides
- ✅ **Troubleshooting**: Common issues and debugging procedures
- ✅ **Best Practices**: Development and production guidelines
- ✅ **Migration Guide**: Upgrading from legacy systems

### **Developer Documentation**

- ✅ **Component Reference**: Detailed processor and service documentation
- ✅ **Integration Guides**: Third-party service integration procedures
- ✅ **Monitoring Setup**: Metrics and alerting configuration
- ✅ **Testing Strategies**: Unit, integration, and end-to-end testing

This documentation provides a complete reference for understanding, deploying, and maintaining the ProductVideo platform. The architecture is designed for scale, reliability, and maintainability while providing a seamless experience for both developers and end-users.
