# Media Module Overview

## System Architecture

The Media Module is the core component of the ProductVideo platform, providing comprehensive AI-powered media generation capabilities for e-commerce stores. This document provides a complete understanding of the media generation system.

## Core Components

### 1. Context Engine (`context_engine.py`)

**Purpose**: Analyzes product information and generates comprehensive context for media generation.

**Key Features:**

- Product analysis and categorization
- Market intelligence and competitive analysis
- Brand integration and audience targeting
- Comprehensive product context generation

**Main Classes:**

- `ProductContext`: Product information container
- `BrandContext`: Brand guidelines and preferences
- `EcommerceContextEngine`: Main analysis engine

### 2. Prompt Engine (`prompt_engine.py`)

**Purpose**: Generates professional-grade prompts for different media types and platforms.

**Key Features:**

- Template-based prompt generation
- Platform-specific optimization
- Brand voice integration
- Quality enhancement through structured prompts

**Main Classes:**

- `PromptContext`: Context for prompt generation
- `GeneratedPrompt`: Generated prompt with metadata
- `PromptEngine`: Main prompt generation engine

### 3. Provider System (`providers/`)

**Purpose**: Multi-provider architecture for AI content generation with automatic fallback.

**Supported Providers:**

- **Banana AI**: Professional product photography and lifestyle images
- **Google Gemini**: Text generation for descriptions and marketing copy
- **Google Veo 3**: Video generation for product showcases
- **Mock Provider**: Testing and development

**Key Features:**

- Automatic fallback between providers
- Rate limiting and quota management
- Cost optimization and performance monitoring

### 4. Content Safety (`content_safety.py`)

**Purpose**: Ensures generated content meets legal and brand safety standards.

**Key Features:**

- Brand protection and trademark filtering
- Legal compliance validation
- Category-specific safety rules
- Content filtering and moderation

### 5. A/B Testing (`ab_testing.py`)

**Purpose**: Tests different strategies and measures performance.

**Key Features:**

- Feature flag management
- Experiment tracking and analysis
- Performance measurement
- User segmentation and targeting

## Data Models

### Core Business Models

```sql
-- Media Generation Jobs
CREATE TABLE media_jobs (
    id BIGINT PRIMARY KEY,
    external_id UUID UNIQUE NOT NULL,
    user_id BIGINT NOT NULL REFERENCES users(id),
    product_id BIGINT NOT NULL,
    status VARCHAR NOT NULL, -- pending, processing, completed, failed, cancelled
    media_type VARCHAR NOT NULL, -- 'video', 'image', 'text'
    provider VARCHAR NOT NULL,
    resolved_provider VARCHAR,
    template_id VARCHAR,
    custom_config JSON,
    full_payload JSON,
    mode VARCHAR,
    model VARCHAR,
    settings JSON,
    items JSON,
    shop_id BIGINT,
    product_ids JSON,
    celery_task_id VARCHAR,
    progress_percentage FLOAT DEFAULT 0.0,
    error_message TEXT,
    idempotency_key VARCHAR(32),
    product_version VARCHAR(16),
    needs_manual_review BOOLEAN DEFAULT FALSE,
    qa_metadata JSON,
    language VARCHAR(10) DEFAULT 'en',
    fallback_language VARCHAR(10) DEFAULT 'en',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Generated Media Variants
CREATE TABLE media_variants (
    id BIGINT PRIMARY KEY,
    external_id UUID UNIQUE NOT NULL,
    job_id BIGINT NOT NULL REFERENCES media_jobs(id),
    user_id BIGINT NOT NULL REFERENCES users(id),
    variant_name VARCHAR NOT NULL,
    status VARCHAR NOT NULL, -- GENERATING, COMPLETED, FAILED, PROCESSING, CANCELLED
    duration_seconds FLOAT,
    resolution VARCHAR,
    file_size_bytes INTEGER,
    provider VARCHAR,
    video_url VARCHAR,
    image_url VARCHAR,
    thumbnail_url VARCHAR,
    provider_media_id VARCHAR,
    provider_metadata JSON,
    is_favorite BOOLEAN DEFAULT FALSE,
    user_rating INTEGER,
    push_status VARCHAR DEFAULT 'pending', -- pending, pushing, completed, failed
    media_id VARCHAR,
    pushed_at TIMESTAMP WITH TIME ZONE,
    push_error_message TEXT,
    alt_text TEXT,
    captions TEXT,
    text_content TEXT,
    quality_score FLOAT,
    needs_manual_review BOOLEAN DEFAULT FALSE,
    qa_metadata JSON,
    brand_safety_checked BOOLEAN DEFAULT FALSE,
    copyright_validated BOOLEAN DEFAULT FALSE,
    content_flags JSON,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);
```

## Processing Flow

### Media Generation Pipeline

```mermaid
graph TD
    A[API Request] --> B[Input Validation]
    B --> C[Create MediaJob]
    C --> D[Enqueue Celery Task]
    D --> E[Context Analysis]
    E --> F[Prompt Generation]
    F --> G[Provider Selection]
    G --> H{Provider Available?}
    H -->|Yes| I[Generate Content]
    H -->|No| J[Try Fallback Provider]
    J --> I
    I --> K[Content Safety Check]
    K --> L{Quality OK?}
    L -->|Yes| M[Upload to Storage]
    L -->|No| N[Flag for Review]
    M --> O[Update Database]
    O --> P[Return Results]
    N --> O
```

### Background Processing

#### Celery Task Architecture

```python
@celery_app.task(name='media.generate_media', bind=True, base=LoggedTask, max_retries=3)
def generate_media(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Complete media generation workflow with comprehensive error handling
    """
```

## API Endpoints

### Generate Media

`POST /api/media/generate`

**Request Body (`MediaGenerateRequest`):**

```json
{
  "mode": "image",
  "media_type": "image",
  "model": "banana",
  "settings": {
    "size": "1024x1024",
    "guidance": 7.5,
    "steps": 25,
    "aspect_ratio": "1:1"
  },
  "items": [
    {
      "product_id": 12345,
      "prompt": "A professional photo of a product",
      "product_context": {
        "title": "Premium Leather Sneakers",
        "description": "High-quality leather sneakers with modern design"
      }
    }
  ],
  "shop_id": 987,
  "shop_branding": {
    "shop_name": "Modern Footwear Co.",
    "brand_voice": "professional"
  }
}
```

**Response (`MediaGenerateResponse`):**

```json
{
  "jobs": [
    {
      "product_id": 12345,
      "job_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "status": "pending",
      "celery_task_id": "f0e9d8c7-b6a5-4321-fedc-ba9876543210"
    }
  ]
}
```

### Get Job Status

`GET /api/media/jobs/{job_id}/status`

**Response (`MediaJobStatusResponse`):**

```json
{
  "job_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "status": "completed",
  "progress": 100,
  "variants": [
    {
      "variant_id": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
      "variant_name": "square_variant_1",
      "status": "COMPLETED",
      "image_url": "https://cdn.example.com/media/var_123456789.jpg",
      "thumbnail_url": "https://cdn.example.com/thumbnails/var_123456789.jpg",
      "duration_seconds": null
    }
  ]
}
```

### Other Endpoints

- **`GET /api/media/jobs`**: List all media generation jobs for the user.
- **`POST /api/media/push`**: Push a generated media variant to a connected store.
- **`POST /api/media/jobs/{job_id}/cancel`**: Cancel a running job.
- **`POST /api/media/jobs/{job_id}/retry`**: Retry a failed job.
- **`POST /api/media/variants/{variant_id}/regenerate`**: Regenerate a specific variant.

## Template System

### Template Structure

The system uses comprehensive JSON templates for different media types:

```json
{
  "image_templates": {
    "product_photography": {
      "base_template": "Professional product photography of {product_title}",
      "style_modifiers": {
        "minimalist": "clean white background, minimal composition, studio lighting",
        "luxury": "premium setting, sophisticated lighting, high-end presentation",
        "lifestyle": "natural environment, lifestyle context, authentic setting"
      },
      "category_specific": {
        "fashion_apparel": {
          "setup": "fashion photography setup with professional lighting",
          "lighting": "soft box lighting with key and fill lights",
          "background": "seamless white backdrop",
          "composition": "model wearing the item or flat lay styling"
        }
      },
      "quality_modifiers": [
        "8K resolution",
        "sharp focus",
        "professional lighting",
        "commercial quality"
      ]
    }
  }
}
```

### Template Categories

- **Product Photography**: Studio-quality product shots
- **Lifestyle Photography**: Contextual, real-world scenarios
- **Video Showcase**: Product demonstration videos
- **Social Media**: Platform-optimized content
- **Marketing Copy**: Product descriptions and captions

## Quality Assurance

### Content Safety Checks

```python
async def check_content_safety(
    self,
    content: str,
    content_type: str = "text",
    product_category: Optional[str] = None
) -> Tuple[bool, List[ContentFlag], Dict[str, Any]]:
    """
    Comprehensive content safety validation
    """
```

### Quality Scoring

- **AI Quality Assessment**: Automatic quality scoring (0-100)
- **Manual Review Queue**: Human oversight for critical content
- **Content Flagging**: Automatic detection of issues requiring review
- **Performance Metrics**: Success rates, user satisfaction, generation times

## Configuration

### Provider Configuration

```python
PROVIDER_CONFIGS = {
    "banana": {
        "api_key": "your_banana_api_key",
        "base_url": "https://api.banana.dev",
        "timeout": 300,
        "max_retries": 3,
        "models": ["stable-diffusion-xl", "stable-diffusion-2"]
    },
    "gemini": {
        "api_key": "your_gemini_api_key",
        "model": "gemini-pro",
        "timeout": 60,
        "max_retries": 2
    },
    "veo3": {
        "api_key": "your_veo3_api_key",
        "base_url": "https://generativelanguage.googleapis.com/v1beta",
        "timeout": 600,
        "max_retries": 3
    }
}
```

### Storage Configuration

```bash
STORAGE_PROVIDER=s3
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-media-bucket
```

## Performance & Scalability

### Benchmarks

- **Image Generation**: 30-60 seconds per variant
- **Video Generation**: 2-5 minutes per 30-second video
- **Text Generation**: 5-15 seconds per content piece
- **Storage Upload**: 10-30 seconds depending on file size

### Scalability Features

- **Horizontal Scaling**: Multiple Celery workers
- **Concurrent Processing**: 100+ simultaneous generation jobs
- **Queue Management**: Redis-based task queuing with priorities
- **Database Optimization**: Connection pooling and query optimization
- **CDN Distribution**: Global content delivery

## Error Handling & Recovery

### Retry Logic

```python
@celery_app.task(name='media.generate_media', bind=True, base=LoggedTask, max_retries=3)
def generate_media(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    try:
        # Main generation logic
        pass
    except Exception as e:
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries * 60  # Exponential backoff
            raise self.retry(countdown=countdown, exc=e)
        raise
```

### Error Types

- **Provider Errors**: API failures, rate limits, authentication issues
- **Storage Errors**: Upload failures, permission issues, quota exceeded
- **Validation Errors**: Content safety violations, quality issues
- **System Errors**: Database failures, network issues, resource exhaustion

## Monitoring & Analytics

### Key Metrics

```python
# Success rates
media_duration_seconds{media_type="video", status="success"}
media_push_duration_seconds{platform="shopify", status="success"}

# Performance tracking
histogram_quantile(0.95, rate(media_duration_seconds_bucket[5m]))

# Queue health
celery_active_tasks{queue="media-generation"}
celery_queue_length{queue="media-push"}
```

### Monitoring Features

- **Generation Success Rate**: Percentage of successful generations
- **Average Generation Time**: Time to complete media generation
- **Provider Performance**: Success rates by provider
- **Quality Scores**: Average quality scores over time
- **User Satisfaction**: Ratings and feedback metrics

## Security & Compliance

### Content Safety

- **Brand Protection**: Automatic filtering of competitor mentions
- **Legal Compliance**: Trademark and copyright validation
- **Category-Specific Rules**: Different standards for different product types
- **Manual Review**: Human oversight for sensitive content

### Data Protection

- **Encryption**: Secure storage of API keys and sensitive data
- **Access Control**: Role-based permissions and authentication
- **Audit Logging**: Comprehensive logging of all operations
- **Data Retention**: Configurable retention policies

## Best Practices

### Development

- **Template Maintenance**: Regularly update and test prompt templates
- **Provider Monitoring**: Monitor provider performance and costs
- **Quality Assurance**: Implement comprehensive testing for new features
- **Documentation**: Keep API documentation current and comprehensive

### Production

- **Monitoring**: Set up comprehensive monitoring and alerting
- **Backup Providers**: Always have fallback providers configured
- **Rate Limiting**: Implement appropriate rate limits and quotas
- **Cost Optimization**: Monitor and optimize provider usage costs

## Troubleshooting

### Common Issues

#### Provider Issues

- **API Rate Limits**: Implement exponential backoff and request queuing
- **Authentication Failures**: Verify API keys and permissions
- **Service Outages**: Monitor provider status and implement fallbacks

#### Quality Issues

- **Poor Generation Quality**: Review and update prompt templates
- **Inconsistent Results**: Implement quality scoring and filtering
- **Content Safety Violations**: Update safety filters and rules

#### Performance Issues

- **Slow Generation Times**: Optimize provider selection and caching
- **High Error Rates**: Implement better error handling and retries
- **Resource Exhaustion**: Monitor system resources and scale appropriately

## Future Enhancements

### Planned Features

- **Advanced AI Models**: Integration with newer, more capable AI models
- **Custom Training**: Allow users to train custom models
- **Real-time Collaboration**: Multi-user editing and approval workflows
- **Advanced Analytics**: Predictive analytics and recommendation engine
- **Mobile Optimization**: Native mobile apps for content management

### Architecture Improvements

- **Microservices**: Break down into smaller, more focused services
- **Event-Driven Architecture**: Implement event-driven processing
- **Global Distribution**: Multi-region deployment for better performance
- **Advanced Caching**: Implement more sophisticated caching strategies

---

This documentation provides a comprehensive overview of the Media Module architecture, covering all aspects from core components to operational procedures. The system is designed to be scalable, reliable, and maintainable while providing professional-grade media generation capabilities.
