# Backend System Architecture

This document provides a comprehensive overview of the backend architecture for the E-commerce Platform.

## 1. Core Philosophy

The backend is designed with a modular, service-oriented architecture. Key principles include:

- **Separation of Concerns**: Each module has a distinct responsibility (e.g., `auth`, `billing`, `media`).
- **Scalability**: The system is built to scale horizontally using a distributed task queue (Celery) and a stateless API design.
- **Extensibility**: A plugin-based system allows for easy integration with new e-commerce platforms and AI providers.
- **Observability**: Structured logging, Prometheus metrics, and health checks are integrated throughout the system.
- **Asynchronous Operations**: The entire stack is asynchronous, using `asyncio` and `asyncpg` for high performance and concurrency.

## 2. High-Level Architecture

The backend consists of three main logical layers:

1. **API Layer**: Handles incoming HTTP requests, authentication, and validation.
2. **Service Layer**: Contains the core business logic for each module.
3. **Data & Processing Layer**: Manages data storage, background tasks, and communication with external services.

```mermaid
graph TD
    subgraph "External World"
        A[Users via Frontend]
        B[Shopify Webhooks]
        C[API Clients]
    end

    subgraph "Backend System"
        subgraph "API Layer (FastAPI)"
            D[API Server]
            E[Authentication]
            F[Routers]
        end

        subgraph "Service & Processing Layer"
            G[Core Services]
            H[Module Services]
            I[Celery Workers]
            J[Task Queue: Redis]
        end

        subgraph "Data & Integration Layer"
            K[Database: PostgreSQL]
            L[AI Providers]
            M[Shopify API]
            N[Airbyte ETL]
            O[Object Storage]
        end
    end

    A --> D
    B --> D
    C --> D
    D -- Authenticates via --> E
    D -- Dispatches to --> F
    F -- Uses --> H
    H -- Uses --> G
    H -- Enqueues tasks --> J
    I -- Consumes tasks from --> J
    I -- Executes Tasks, which use --> H
    H -- Interacts with --> K
    H -- Calls --> L
    H -- Calls --> M
    H -- Manages --> N
    H -- Uses --> O
```

## 3. Directory Structure & Module Overview

The `backend/src` directory is organized into several key packages:

- **`core/`**: Cross-cutting concerns like database configuration, settings management, base classes for services and schemas, and core middleware.
- **`modules/`**: The heart of the application, where each sub-directory represents a business domain (e.g., `products`, `media`, `billing`).
- **`plugins/`**: Contains platform-specific implementations (e.g., `shopify`), allowing for easy extension to other platforms like WooCommerce.
- **`servers/`**: Entry points for running the application.
  - `api/`: The main FastAPI application server.
  - `worker/`: The Celery worker and task definitions.

### Module Breakdown

| Module              | Description                                                         | Key Responsibilities                                                                    |
| :------------------ | :------------------------------------------------------------------ | :-------------------------------------------------------------------------------------- |
| **`analytics`**     | Handles event tracking, A/B testing, and performance metrics.       | Ingesting analytics events, providing dashboard data, managing A/B tests.               |
| **`assets`**        | Manages media assets (images, videos) associated with products.     | Listing assets, reassigning assets to different products.                               |
| **`auth`**          | User authentication, authorization, and session management.         | User registration, login (password & OAuth), JWT handling, password reset.              |
| **`billing`**       | Manages subscriptions, payments, and usage quotas via Stripe.       | Tenant creation, subscription management, credit system, usage tracking.                |
| **`compliance`**    | Ensures content safety and legal compliance.                        | Filtering for competitor brands, checking for trademark/copyright issues.               |
| **`customers`**     | Manages customer data synced from e-commerce platforms.             | Storing customer information and addresses.                                             |
| **`media`**         | Core of the AI content generation.                                  | Orchestrates media generation jobs, manages AI providers, and handles media processing. |
| **`notifications`** | Handles sending emails and other notifications.                     | Sending password reset emails, and other user notifications.                            |
| **`orders`**        | Manages order data synced from e-commerce platforms.                | Storing order and line item information.                                                |
| **`processing`**    | Handles media processing tasks like transcoding.                    | Video transcoding to MP4/HLS, thumbnail generation.                                     |
| **`products`**      | Manages product data synced from e-commerce platforms.              | Storing product, variant, and image information.                                        |
| **`queue`**         | Manages the distributed task queue using Celery and Redis.          | Enqueuing and monitoring background tasks for media generation, sync, etc.              |
| **`scraper`**       | Scrapes data from e-commerce websites.                              | Manages scraping jobs and stores scraped product/collection data.                       |
| **`storage`**       | Provides an abstraction layer for object storage.                   | Uploading and managing media files on services like S3 or local storage.                |
| **`stores`**        | Manages connected e-commerce stores.                                | Store creation, connection testing, and managing sync configurations.                   |
| **`sync`**          | Handles data synchronization with e-commerce platforms via Airbyte. | Manages Airbyte connections, triggers syncs, and processes incoming data.               |
| **`templates`**     | Manages media generation templates.                                 | Provides different styles and formats for generated media.                              |

## 4. Key Architectural Patterns

### a. Service-Oriented Modularity

Each module in `src/modules` is self-contained with its own `models.py`, `schemas.py`, `service.py`, and `router.py`. This makes the codebase easy to navigate, maintain, and test.

- **`router.py`**: Defines the API endpoints for the module.
- **`service.py`**: Contains the core business logic.
- **`models.py`**: Defines the SQLAlchemy database models.
- **`schemas.py`**: Defines the Pydantic data validation schemas.

### b. Asynchronous Task Processing

Heavy or long-running tasks are offloaded to background workers using **Celery**.

- **API Server**: Quickly accepts requests and enqueues a task in Redis.
- **Celery Worker**: Picks up the task from the Redis queue and executes the logic.

This ensures the API remains responsive and can handle a high volume of requests. Key async tasks include:

- Media generation (`media_tasks.py`)
- Data synchronization (`sync_tasks.py`)
- Webhook processing (`webhook_tasks.py`)

### c. Data Synchronization with Airbyte

Data is synced from Shopify into the application's database using **Airbyte**.

1. **Setup**: When a store is connected, the backend automatically configures an Airbyte source (for Shopify) and a destination (PostgreSQL).
2. **Sync Trigger**: Syncs are triggered manually via the API or automatically via Shopify webhooks.
3. **ETL Process**: Airbyte extracts data from Shopify and loads it into a raw format in a dedicated `airbyte` database.
4. **Transformation**: A `consumer` process (part of the `sync` module's Celery tasks) reads the raw data, transforms it, and upserts it into the production database tables (`products`, `orders`, etc.).

This provides a robust and reliable ETL pipeline that is decoupled from the main application logic.

### d. Pluggable Architecture

- **AI Providers**: The `media/providers` system allows for different AI generation services (like Banana, Veo3, Gemini) to be used interchangeably. A `ProviderManager` selects the appropriate provider based on the request and configured fallbacks.
- **Platform Plugins**: The `plugins` directory is designed to hold platform-specific logic. Currently, it contains the `shopify` plugin, which handles Shopify-specific API interactions. This makes it straightforward to add support for other platforms like WooCommerce or BigCommerce in the future.

## 5. Database Schema Overview

The database is designed with a multi-tenant architecture, where each `User` has one or more `Tenant`s, and each `Tenant` can have multiple `Store`s.

- **`users`**: Stores user account information.
- **`tenants`**: Represents a workspace or organization, linking a user to their resources.
- **`stores`**: Represents a connected e-commerce store (e.g., a Shopify shop).
- **`products`, `product_variants`, `product_images`**: Mirrored data from the connected stores.
- **`media_jobs`, `media_variants`**: Track the status and results of AI media generation.
- **`subscriptions`, `billing_usage`, `credit_transactions`**: Manage billing and subscription data.
- **`webhook_events`, `sync_jobs`, `sync_checkpoints`**: Support the data synchronization process.

All primary models use a UUID `external_id` field to expose to the outside world, while using integer `id` for internal relationships.

For a complete and detailed reference of the database structure, please see the [Database Schema Documentation](../database/database-schema.md).

## 6. Observability

- **Logging**: Centralized, structured JSON logging is implemented across the application. A `RequestContextMiddleware` adds a unique request ID to all logs for easy tracing.
- **Metrics**: A dedicated `/metrics` endpoint exposes Prometheus metrics for key operations (API requests, task queue length, sync durations, etc.).
- **Health Checks**: A `/health` endpoint provides a simple way to check the health of the API server and its database connection.

This architecture provides a solid foundation for a scalable, maintainable, and feature-rich e-commerce integration platform.
