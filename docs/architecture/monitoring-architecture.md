# Monitoring & Observability Architecture

## Overview

This document describes the comprehensive monitoring and observability system implemented for the E-commerce Platform. The system provides real-time insights into application performance, queue health, sync operations, and business metrics using Prometheus, Grafana, and custom instrumentation.

## Architecture

### Components

```mermaid
graph TD
    A["<b>Application</b><br/>• API Server<br/>• Workers<br/>• Background Tasks"] -- Emits Metrics --> B;
    B["<b>Prometheus</b><br/>• Metrics Collection<br/>• Time Series Storage"] -- Queried by --> C;
    C["<b>Grafana</b><br/>• Dashboards<br/>• Alerts<br/>• Visualizations"] -- Triggers --> D;
    D["<b>Alerting</b><br/>(e.g., Slack, PagerDuty)"];
```

### Data Flow

1. **Instrumentation**: Application code emits metrics using Prometheus client libraries
2. **Collection**: Prometheus scrapes metrics from application endpoints (`/metrics`)
3. **Storage**: Metrics are stored in Prometheus time-series database
4. **Visualization**: Grafana queries Prometheus and displays dashboards
5. **Alerting**: AlertManager can trigger notifications based on metric thresholds

## Metrics Collection

### Application Metrics

#### 1. Media Processing Metrics

**Location**: `backend/src/core/metrics.py`

```python
# Generation duration and failures
media_generation_duration_seconds
media_generation_failures_total

# Push duration and failures
media_push_duration_seconds
media_push_failures_total
```

**Collection Points**:
- `backend/src/modules/media/processors/` - Media generation and push tasks.

#### 2. Sync Operation Metrics

**Location**: `backend/src/core/metrics.py`

```python
# Sync operations
sync_jobs_total
sync_duration_seconds
sync_job_failures_total
active_sync_jobs

# Airbyte integration
airbyte_api_requests_total
airbyte_api_duration_seconds
```

**Collection Points**:
- `backend/src/servers/worker/tasks/sync_tasks.py` - Sync job tasks.
- `backend/src/modules/sync/airbyte_service.py` - Airbyte API calls.

#### 3. Queue & Worker Metrics

**Location**: `backend/src/core/metrics.py`

```python
# Queue health
celery_active_tasks
celery_queue_length
celery_task_failures_total

# Worker status
celery_worker_status
celery_worker_pool_size
```

**Collection Points**:
- Custom Celery instrumentation.

#### 4. Analytics Metrics

**Location**: `backend/src/core/metrics.py`

```python
# Event processing
analytics_processing_duration_seconds
analytics_processing_failures_total
```

**Collection Points**:
- `backend/src/modules/analytics/processors/analytics_processor.py` - Analytics event processing tasks.

### Custom Metrics Implementation

#### Metric Types

| Type | Use Case | Example |
|------|----------|---------|
| **Counter** | Cumulative values | `sync_jobs_total` |
| **Gauge** | Point-in-time values | `celery_active_tasks` |
| **Histogram** | Duration distributions | `media_generation_duration_seconds` |

#### Labels

```python
# Consistent labeling strategy
{
    "queue": "media-generation",
    "task_name": "generate_media",
    "status": "success",
    "worker": "worker-1",
    "store_domain": "store.com"
}
```

## Monitoring Stack Setup

**Note**: The monitoring services (`prometheus`, `grafana`, `mailhog`) are commented out by default in the main `docker-compose.yml` file. You will need to uncomment them to run the monitoring stack.

### Docker Compose Configuration

**File**: `docker-compose.yml`

```yaml
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3030:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "8025:8025"
      - "1025:1025"
```

### Prometheus Configuration

**File**: `monitoring/prometheus.yml`

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "api"
    static_configs:
      - targets: ["api:8123"]
    metrics_path: "/metrics"
    scrape_interval: 30s

  - job_name: "worker"
    static_configs:
      - targets: ["worker:9091"]
    metrics_path: "/metrics"
    scrape_interval: 30s

  - job_name: "video-worker"
    static_configs:
      - targets: ["video-worker:8000"]
    metrics_path: "/metrics"
    scrape_interval: 30s
```

### Nginx Configuration

**File**: `nginx.conf`

```nginx
# Monitoring endpoints
location /metrics {
    proxy_pass http://backend;
    # Restrict access to monitoring
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;
    deny all;
}
```

## Dashboard Structure

### Main Dashboard Panels

#### 1. Media Processing
- **Success Rate**: `rate(media_generation_failures_total[5m]) / rate(media_generation_duration_seconds_count[5m])`
- **Duration (95th percentile)**: `histogram_quantile(0.95, rate(media_generation_duration_seconds_bucket[5m]))`
- **Request Rate**: `rate(media_generation_duration_seconds_count[5m])`

#### 2. Sync Operations
- **Sync Trigger Rate**: `rate(sync_jobs_total[5m])`
- **Sync Success Rate**: `rate(sync_jobs_total{status="success"}[5m]) / rate(sync_jobs_total[5m])`
- **Webhook Triggers**: `rate(webhook_requests_total[5m])`

#### 3. Queue Health
- **Queue Length**: `celery_queue_length`
- **Active Tasks**: `celery_active_tasks`
- **Worker Status**: `celery_worker_status`
- **Task Failure Rate**: `rate(celery_task_failures_total[5m]) / rate(celery_task_rate_total[5m])`

#### 4. System Health
- **Service Status**: `up{job="api"}`
- **Worker Pool Size**: `celery_worker_pool_size`

## Alerting Rules

### Example Alert Rules

**File**: `monitoring/alert-rules.yml`

```yaml
groups:
  - name: application_alerts
    rules:
      - alert: HighQueueLength
        expr: celery_queue_length > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Queue length is too high"
          description: "Queue {{ $labels.queue }} has {{ $value }} pending tasks"

      - alert: MediaFailureRate
        expr: rate(media_generation_failures_total[5m]) / rate(media_generation_duration_seconds_count[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High media generation failure rate"
          description: "{{ $value }}% of media generation tasks are failing"
```

## Usage Guide

### Starting the Monitoring Stack

```bash
# Uncomment monitoring services in docker-compose.yml, then run:
docker-compose up -d prometheus grafana mailhog nginx

# View services
docker-compose ps
```

### Accessing Services

| Service | URL | Credentials |
|---------|-----|-------------|
| **Grafana** | http://localhost:3030 | admin/admin |
| **Prometheus** | http://localhost:9090 | - |
| **Mailhog** | http://localhost:8025 | - |
| **API Metrics** | http://localhost:8123/metrics | - |

### Dashboard Setup

1. **Import Dashboard**:
   - Go to Grafana → Dashboards → Import
   - Upload `monitoring/grafana-dashboard.json`

2. **Configure Data Source**:
   - Go to Configuration → Data Sources → Add
   - Select Prometheus, URL: `http://prometheus:9090`

### Monitoring API Endpoints

#### Queue Statistics
```bash
curl http://localhost:8123/api/v1/queue/stats
```

#### Application Metrics
```bash
curl http://localhost:8123/metrics
```

#### Prometheus Queries
```bash
# Active tasks
curl "http://localhost:9090/api/v1/query?query=celery_active_tasks"

# Queue length
curl "http://localhost:9090/api/v1/query?query=celery_queue_length"
```

## Troubleshooting

### Common Issues

#### 1. Metrics Not Appearing
```bash
# Check if application is exposing metrics
curl http://localhost:8123/metrics

# Check Prometheus targets
curl http://localhost:9090/api/v1/targets
```

#### 2. Dashboard Shows No Data
```bash
# Verify Prometheus is scraping correctly
curl "http://localhost:9090/api/v1/query?query=up"

# Check Grafana data source configuration
# Go to Configuration → Data Sources → Prometheus
```

#### 3. High Memory Usage
```bash
# Adjust Prometheus retention
# Edit monitoring/prometheus.yml
storage:
  tsdb:
    retention.time: 30d
```

### Performance Tuning

#### Prometheus Configuration
```yaml
global:
  scrape_interval: 15s        # How often to scrape
  evaluation_interval: 15s    # How often to evaluate rules

# Reduce memory usage
storage:
  tsdb:
    retention.time: 7d        # Keep data for 7 days
    retention.size: 1GB       # Limit storage size
```

#### Application Metrics
```python
# Use appropriate metric types
# Counter for cumulative values
# Gauge for point-in-time values
# Histogram for distributions
```

## Best Practices

### 1. Metric Naming
- Use consistent prefixes: `media_`, `sync_`, `celery_`
- Include units in metric names when applicable
- Use snake_case for metric names

### 2. Label Usage
- Keep label cardinality low
- Use meaningful label values
- Avoid high-cardinality labels (e.g., user IDs, timestamps)

### 3. Dashboard Organization
- Group related metrics together
- Use consistent color schemes
- Set appropriate refresh intervals

### 4. Alert Configuration
- Set meaningful thresholds
- Include runbook URLs in alerts
- Use appropriate severity levels

## Security Considerations

### 1. Metrics Endpoint Protection
```nginx
location /metrics {
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    deny all;
}
```

### 2. Grafana Security
- Change default admin password
- Enable authentication
- Configure HTTPS

### 3. Network Security
- Run monitoring stack in isolated network
- Use internal load balancers
- Implement proper firewall rules

## Future Enhancements

### 1. Advanced Alerting
- Integration with Slack, PagerDuty
- Alert correlation and grouping
- Auto-remediation workflows

### 2. Custom Metrics
- Business KPI tracking
- User experience metrics
- Performance profiling

### 3. Distributed Tracing
- Integration with Jaeger/OpenTelemetry
- End-to-end request tracing
- Service mesh integration

### 4. Log Aggregation
- Centralized logging with ELK stack
- Log-based metrics
- Anomaly detection

---

This monitoring architecture provides comprehensive observability for the E-commerce Platform, enabling proactive issue detection, performance optimization, and reliable operation.