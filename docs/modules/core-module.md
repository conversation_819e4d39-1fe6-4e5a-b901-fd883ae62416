# Core Module Documentation

## 1. Overview

The `core` module is the foundation of the backend application. It provides essential, cross-cutting functionalities that are used by all other modules. It is not a business domain module itself, but rather a collection of shared utilities and configurations.

**Key Responsibilities:**

-   Application-wide configuration management.
-   Database connection and session management.
-   Base classes for models, schemas, and services.
-   Core middleware for handling requests.
-   Application-wide metrics and logging.

## 2. Directory Structure

```
core/
├── __init__.py
├── configs/                # JSON configuration files
│   ├── airbyte_service_config.json
│   └── providers_config.json
├── db/                     # Database-related components
│   ├── __init__.py
│   ├── base_declarative.py # Declarative base for SQLAlchemy models
│   ├── database.py         # Database engine and session management
│   └── models.py           # Central import for all SQLAlchemy models
├── middleware/             # FastAPI middleware
│   ├── __init__.py
│   ├── request_context_middleware.py
│   └── request_logging_middleware.py
├── schemas/                # Base Pydantic schemas
│   ├── __init__.py
│   └── base_schemas.py
├── services/               # Base service class and core services
│   ├── __init__.py
│   ├── base_service.py
│   └── redis_lock.py
└── utils/                  # Utility functions
    ├── __init__.py
    ├── logging.py
    └── request_context.py
```

## 3. Key Components

### a. Configuration (`config.py`)

-   **`Settings` class**: A Pydantic `BaseSettings` class that loads configuration from a `.env` file and environment variables.
-   **`get_settings()` function**: A cached function to provide a singleton `Settings` instance throughout the application.
-   It manages all environment-specific variables, such as database URLs, API keys, and other secrets.

### b. Database (`db/`)

-   **`database.py`**: This is the central point for database interaction.
    -   It creates an `async_engine` for SQLAlchemy.
    -   It provides `async_session_factory` for creating new database sessions.
    -   `get_db` and `get_fresh_db` are FastAPI dependencies that provide a database session to the API endpoints, managing the session lifecycle automatically.
-   **`base_declarative.py`**: Provides the `declarative_base()` for all SQLAlchemy models to inherit from.
-   **`models.py`**: This file is crucial as it imports all SQLAlchemy models from all other modules. This ensures that SQLAlchemy's metadata is aware of all tables and relationships when the application starts.

### c. Middleware (`middleware/`)

-   **`request_context_middleware.py`**: Injects a unique `X-Request-ID` into every request, which is then available for logging and tracing across the application.
-   **`request_logging_middleware.py`**: Provides detailed, structured logging for every incoming request and its corresponding response, including request/response bodies.

### d. Schemas (`schemas/`)

-   **`base_schemas.py`**: Defines base Pydantic schemas (`BaseSchema`, `BaseCreateSchema`, `BaseUpdateSchema`, `BaseResponseSchema`) that other module schemas inherit from. This promotes consistency in schema definitions.

### e. Services (`services/`)

-   **`base_service.py`**: A generic `BaseService` class that provides common CRUD (Create, Read, Update, Delete) operations. Services for other modules (like `ProductService`) inherit from this class to reduce boilerplate code.
-   **`redis_lock.py`**: A distributed locking mechanism using Redis. This is critical for preventing race conditions in concurrent operations, such as multiple sync jobs for the same store.

### f. Utilities (`utils/`)

-   **`logging.py`**: Sets up application-wide structured logging. It configures dual logging: colorful, pretty-printed JSON for the console (for development) and JSON format for files (for production).
-   **`request_context.py`**: Defines the `ContextVar` for the request ID, making it accessible in any part of the code that handles a request.

## 4. How It Works

When the application starts (in `servers/api/main.py`), the `core` module's components are among the first to be initialized:

1.  `setup_logging()` is called to configure the logger.
2.  `get_settings()` is called to load the application configuration.
3.  The FastAPI application is created, and the middleware from `core/middleware` is added.
4.  When a request comes in, the middleware adds the request ID and logs the request.
5.  The request is routed to a specific module's router.
6.  The endpoint in the router uses a `Depends(get_db)` to get a database session from `core/db/database.py`.
7.  The endpoint calls a service (which inherits from `core/services/base_service.py`) to perform business logic.
8.  Throughout this process, all components can access the application settings via `get_settings()`.

In essence, the `core` module acts as the central nervous system of the backend, providing the essential infrastructure and shared functionality that enables all other modules to work together cohesively.
