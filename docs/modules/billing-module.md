# Billing Module Documentation

## 1. Overview

The `billing` module is responsible for managing all aspects of payments, subscriptions, and usage-based billing. It integrates with Stripe to handle payment processing and subscription management, and it includes a flexible credit system for usage-based billing.

**Key Responsibilities:**

-   Managing tenants and their subscription plans.
-   Integrating with Stripe for creating customers, subscriptions, and processing payments.
-   Handling Stripe webhooks to keep the application's billing state in sync with Stripe.
-   Implementing a credit system for metered usage (e.g., per video generation).
-   Enforcing usage quotas based on the user's subscription plan and credit balance.

## 2. Directory Structure

```
billing/
├── __init__.py
├── credit_service.py       # Manages credit balance, grants, and deductions
├── models.py               # SQLAlchemy models for billing (Subscription, Invoice, etc.)
├── quota_service.py        # Enforces usage quotas and budgets
├── router.py               # API endpoints for billing and subscriptions
├── schemas.py              # Pydantic schemas for billing data
├── service.py              # High-level billing logic and tenant management
└── stripe_service.py       # Handles all interactions with the Stripe API
```

## 3. Key Components

### a. Stripe Integration (`stripe_service.py`)

-   **`StripeService`**: This class is the central point of interaction with the Stripe API.
    -   `create_customer`: Creates a new customer in Stripe when a new tenant is created.
    -   `create_subscription`: Creates a new subscription for a tenant.
    -   `record_usage`: Records usage for metered billing items.
    -   `process_webhook`: Handles incoming webhooks from Stripe to update subscription statuses, process invoices, etc.

### b. Billing Service (`service.py`)

-   **`BillingService`**: Manages the core business logic for billing.
    -   `create_tenant`: Creates a new tenant and, by default, a corresponding Stripe customer.
    -   `get_billing_dashboard_data`: Gathers all necessary data for the user's billing dashboard, including current subscription, usage, and recent invoices.
    -   `check_usage_limits`: Checks if a tenant has sufficient quota to perform an action (e.g., generate a video).

### c. Credit System (`credit_service.py`)

-   **`CreditService`**: Manages the credit balance for each tenant.
    -   `grant_subscription_credits`: Grants a monthly credit allowance based on the tenant's subscription plan.
    -   `purchase_credits`: Adds credits to a tenant's balance after a successful purchase.
    -   `deduct_credits`: Deducts credits when a user consumes a metered resource.
    -   `get_balance`: Retrieves the current credit balance for a tenant.

### d. Quota Enforcement (`quota_service.py`)

-   **`QuotaService`**: Responsible for checking if a user has enough quota or credits to perform an action.
    -   `check_quota`: Checks if a user has sufficient quota (from their plan) or credits to cover the cost of a requested action.
    -   `check_budget`: A more specific check for credit balance.
    -   `deduct_quota_and_budget`: Deducts credits after a successful operation.

### e. Data Models (`models.py`)

-   **`Subscription`**: Stores details about a tenant's Stripe subscription.
-   **`BillingUsage`**: Tracks usage of metered resources.
-   **`Invoice`**: Stores information about invoices from Stripe.
-   **`PaymentMethod`**: Stores customer payment methods.
-   **`CreditTransaction`**: A ledger of all credit grants, purchases, and deductions.

## 4. Billing and Subscription Flow

### a. New Subscription Flow

1.  A user selects a plan on the frontend, which calls the `/api/billing/subscriptions` endpoint.
2.  `StripeService.create_subscription` is called, which creates a new subscription in Stripe.
3.  If the payment requires authentication (like 3D Secure), a `client_secret` is returned to the frontend to confirm the payment.
4.  Stripe sends a `customer.subscription.created` webhook to the backend.
5.  `StripeService.process_webhook` handles the event and updates the `Subscription` record in the database.
6.  `CreditService.grant_subscription_credits` is called to grant the initial credits for the new plan.

### b. Usage-Based Billing Flow

1.  A user initiates an action that consumes resources (e.g., generating a video).
2.  Before the action is performed, `QuotaService.check_quota` is called.
3.  The service first checks if the user has enough credits. If so, the action is allowed.
4.  If credits are insufficient, it checks the user's plan-based quotas (e.g., 10 free video generations per month).
5.  If the user is within their limits, the action proceeds.
6.  After the action is successfully completed, `QuotaService.deduct_quota_and_budget` is called to deduct the corresponding credits from the user's balance.

This dual system of plan-based quotas and a credit system provides a flexible billing model that can accommodate both free-tier users and paying customers with varying usage needs.
