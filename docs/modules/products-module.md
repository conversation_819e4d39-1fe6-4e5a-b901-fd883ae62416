# Products Module Documentation

## 1. Overview

The `products` module is a central part of the application, responsible for managing the product data that is synchronized from connected e-commerce stores. It serves as the local source of truth for product information used in media generation and other features.

**Key Responsibilities:**

-   Storing detailed product information, including variants, images, and inventory levels.
-   Providing API endpoints for clients (like the frontend) to read and manage product data.
-   Ensuring that product data is kept up-to-date through the data synchronization process.

## 2. Directory Structure

```
products/
├── __init__.py
├── models.py         # SQLAlchemy models for Product, ProductVariant, etc.
├── router.py         # API endpoints for product management
├── schemas.py        # Pydantic schemas for product data
└── service.py        # Business logic for product operations
```

## 3. Key Components

### a. Data Models (`models.py`)

-   **`Product`**: The core model representing a single product. It stores essential information like title, description, vendor, and status.
-   **`ProductVariant`**: Represents a specific version of a product (e.g., a particular size or color). It holds data like SKU, price, and inventory quantity.
-   **`ProductImage`**: Stores information about product images, including their source URL and alt text. Images can be associated with a product as a whole or with specific variants.
-   **`InventoryLevel`**: Tracks the inventory quantity of a product variant at a specific location.

These models are designed to mirror the data structure of platforms like Shopify, allowing for a faithful local representation of the store's catalog.

### b. Product Service (`service.py`)

-   **`ProductService`**: Inherits from `BaseService` and provides the core logic for product data management.
    -   `get_by_store_external_id_paginated`: Retrieves a paginated list of products for a specific store.
    -   `create_product_with_variants`: A transactional method to create a new product along with all its variants.
    -   `get_product_with_full_details_by_external_id`: Fetches a single product along with its variants and images.

### c. API Router (`router.py`)

-   Provides RESTful endpoints for product data:
    -   `GET /`: Lists all products for the current user's stores, with support for pagination and search.
    -   `POST /`: Creates a new product.
    -   `GET /{product_id}`: Retrieves a single product by its external ID.
    -   `PUT /{product_id}`: Updates a product.
    -   `DELETE /{product_id}`: Deletes a product.
-   Crucially, all endpoints perform ownership checks to ensure a user can only access products from stores they own.

### d. Schemas (`schemas.py`)

-   Defines the Pydantic models for validating and serializing product data in API requests and responses.
-   **`ProductListResponse`** and **`ProductResponse`** are key schemas that define how product data, including its variants and images, is presented to the API consumer.

## 4. How It Works

The `products` module is at the intersection of data synchronization and the user-facing API.

-   **Data Ingestion**: The `sync` module is responsible for populating the product-related tables. When a sync job runs, it fetches product data from a platform like Shopify and uses the `ProductService` to create or update the corresponding records in the local database.
-   **Data Presentation**: The frontend application uses the endpoints in the `products` router to display the product catalog to the user. When a user wants to generate media for a product, the frontend can pass the product's information, obtained from these endpoints, to the media generation API.

This architecture ensures that the application has a local, readily accessible copy of the product data, which is essential for the performance of features like media generation, as it avoids the need to make real-time API calls to the e-commerce platform for product information.
