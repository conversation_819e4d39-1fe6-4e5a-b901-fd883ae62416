# Sync Module Documentation

## 1. Overview

The `sync` module is the backbone of the data synchronization process. It is responsible for orchestrating the ETL (Extract, Transform, Load) pipeline that moves data from external e-commerce platforms like Shopify into the application's local database. It uses Airbyte for the "Extract" and "Load" parts and contains the "Transform" logic within its processors.

**Key Responsibilities:**

-   Managing the Airbyte infrastructure (sources, destinations, connections).
-   Triggering and monitoring Airbyte sync jobs.
-   Processing the raw data loaded by Airbyte into a structured, relational format in the production database.
-   Handling real-time updates via webhooks.

## 2. Directory Structure

```
sync/
├── __init__.py
├── airbyte_service.py      # Service for interacting with the Airbyte API
├── base/                   # Base classes for sync processors and syncers
│   ├── __init__.py
│   ├── sync_processor.py
│   └── syncer.py
├── models.py               # SQLAlchemy models for sync-related data
├── platforms/              # Platform-specific sync logic
│   └── shopify/
│       ├── __init__.py
│       ├── shopify_processor.py
│       └── syncers/        # Entity-specific syncers for Shopify
├── processors/             # High-level processors for sync tasks
│   └── bulk_sync_processor.py
├── router.py               # API endpoints for sync management
└── service.py              # Generic sync service (delegates to plugins)
```

## 3. Key Components

### a. Airbyte Service (`airbyte_service.py`)

-   **`AirbyteService`**: This is a critical component that encapsulates all interactions with the Airbyte API.
    -   `setup_shop_sync`: A key method that automates the entire process of setting up a new sync pipeline for a store. It creates an Airbyte source, destination, and connection.
    -   `trigger_sync`: Initiates a new sync job in Airbyte.
    -   `get_job_details`: Polls Airbyte for the status of a running sync job.
    -   It also includes methods for managing the lifecycle of Airbyte resources (e.g., deleting connections and sources when a store is removed).

### b. Sync Processors and Syncers (`processors/`, `platforms/`, `base/`)

-   **`BulkSyncProcessor`**: The main entry point for processing synced data. It acts as a factory, creating the appropriate platform-specific processor (e.g., `ShopifyProcessor`).
-   **`BaseSyncProcessor`**: A base class that defines the common structure for platform processors.
-   **`ShopifyProcessor`**: The implementation for Shopify. It knows which entity syncers to use for each Shopify data type.
-   **`BaseSyncer`**: A base class for entity-specific syncers (e.g., `ProductsSyncer`). It provides common functionality like batch processing and checkpoint management.
-   **Entity Syncers** (e.g., `ProductsSyncer`, `ProductVariantsSyncer`): Each syncer is responsible for a single entity type. It contains the SQL queries and transformation logic to move data from the raw Airbyte tables to the structured production tables.

### c. Data Models (`models.py`)

-   **`WebhookEvent`**: Stores incoming webhook payloads for reliable, asynchronous processing.
-   **`SyncCheckpoint`**: Tracks the progress of synchronization for each entity type and store. This is crucial for incremental syncs, as it stores the timestamp of the last synced record.
-   **`SyncJob`**: Records the history and status of individual sync jobs.
-   **`DeadLetterQueue`**: A table for storing events that failed to process after multiple retries, allowing for manual inspection and reprocessing.

## 4. Data Synchronization Flow

1.  **Trigger**: A sync can be triggered in two ways:
    -   **Automatically**: A Shopify webhook (e.g., for a product update) is received. The `webhook_tasks` enqueues a task to process it, which in turn may trigger an Airbyte sync.
    -   **Manually**: A user initiates a sync from the frontend, which calls the `/api/stores/{store_id}/sync/products` endpoint.
2.  **Airbyte Job**: The `AirbyteService` triggers a sync job on the Airbyte server.
3.  **Extract & Load**: Airbyte pulls the latest data from the Shopify API and loads it into the corresponding raw tables in the `airbyte` database (e.g., `public.products`).
4.  **Processing Task**: Once the Airbyte job is complete, a Celery task (`bulk_sync_store`) is enqueued.
5.  **Transformation**: The `BulkSyncProcessor` is called. It selects the `ShopifyProcessor`, which then iterates through the entity syncers.
6.  **Upsert**: Each syncer (e.g., `ProductsSyncer`) runs in batches. It reads data from the raw Airbyte tables, transforms it into the production schema, and bulk-upserts it into the production tables (e.g., `products`).
7.  **Checkpoint**: After a successful sync for an entity, the `SyncCheckpoint` for that entity and store is updated with the current timestamp.

This robust, multi-stage process ensures that data is synced reliably and efficiently, with clear separation between the ETL process (handled by Airbyte) and the data transformation logic (handled by the sync processors).
