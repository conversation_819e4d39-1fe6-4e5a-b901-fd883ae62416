# Processing Module Documentation

## 1. Overview

The `processing` module is responsible for handling media processing tasks, particularly video transcoding. After a video is generated by an AI provider, this module can be used to convert it into different formats and generate assets like thumbnails.

**Key Responsibilities:**

-   Transcoding videos into standard formats like MP4 and HLS (HTTP Live Streaming).
-   Generating thumbnails from videos.
-   Creating subtitles for videos.

## 2. Directory Structure

```
processing/
├── __init__.py
└── transcoding_service.py  # Service for video transcoding and processing
```

## 3. Key Components

### a. Video Transcoding Service (`transcoding_service.py`)

-   **`VideoTranscodingService`**: The core component of this module.
    -   It relies on the command-line tools `ffmpeg` and `ffprobe` to be installed on the system where the worker is running.
    -   `process_video`: The main method that orchestrates the transcoding process. It downloads the source video, and then calls other methods to perform the requested processing steps.
    -   `_transcode_to_mp4`: Converts a video to the MP4 format, which is widely compatible.
    -   `_transcode_to_hls`: Converts a video into an HLS stream (a series of small video segments and a playlist file). This is ideal for adaptive bitrate streaming on the web.
    -   `_generate_thumbnails`: Extracts a specified number of frames from the video to be used as thumbnails.
    -   `_generate_subtitles`: A mock implementation that demonstrates how subtitles could be generated from a video's audio track.

## 4. How It Works

The `VideoTranscodingService` is typically used within the media generation pipeline after a video has been generated by an AI provider.

1.  A media generation task in `media_tasks.py` receives a URL for a newly generated video.
2.  It can then call `video_transcoding_service.process_video` with the video URL.
3.  The service downloads the video to a temporary local file.
4.  It then uses `ffmpeg` and `ffprobe` subprocesses to perform the transcoding, thumbnail generation, etc.
5.  The processed files (e.g., the new MP4 file, HLS playlist and segments, thumbnails) are saved to temporary local files.
6.  The paths to these files are returned to the media generation task.
7.  The media generation task then uploads these processed files to the main object storage (e.g., S3) and updates the `MediaVariant` record with the new URLs.

This module provides a powerful and flexible way to process videos, ensuring they are in the optimal format for web delivery and have all the necessary accompanying assets.
