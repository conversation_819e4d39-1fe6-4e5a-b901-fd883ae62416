# Queue Module Documentation

## 1. Overview

The `queue` module is responsible for managing the distributed task queue system, which is a critical component for handling asynchronous, long-running tasks. It uses Celery as the task queue framework with Redis as the message broker.

**Key Responsibilities:**

-   Providing a simple, high-level interface for enqueuing different types of background jobs.
-   Defining task priorities to ensure that important tasks are processed first.
-   Offering a way to check the status of enqueued tasks.

## 2. Directory Structure

```
queue/
├── __init__.py
└── queue_service.py  # Service for interacting with the Celery task queue
```

## 3. Key Components

### a. Celery Service (`queue_service.py`)

-   **`CeleryService`**: The main service class that abstracts away the complexities of Celery.
    -   It initializes the Celery app with the Redis broker URL from the application's settings.
    -   `enqueue_media_generation`: A specialized method for adding a new media generation job to the queue. It takes relevant parameters like `user_id`, `job_id`, and `product_ids` and passes them to the corresponding Celery task.
    -   `enqueue_media_push`: Enqueues a task to push a generated media variant to a platform.
    -   `enqueue_webhook_processing`: Enqueues a task to process an incoming webhook.
    -   `get_task_status`: Allows checking the status of a task by its ID.
-   **`TaskPriority` Enum**: Defines different priority levels (LOW, NORMAL, HIGH, URGENT) that can be assigned to tasks.

## 4. How It Works

The queue module acts as a bridge between the main API server and the background Celery workers.

### Task Enqueueing Flow

1.  A request that requires background processing is received by an API endpoint (e.g., `/api/media/generate`).
2.  The service for that module (e.g., `MediaService`) performs its initial synchronous work, such as creating a `MediaJob` record in the database.
3.  It then calls one of the `enqueue_*` methods on the `celery_service` (e.g., `enqueue_media_generation`).
4.  The `CeleryService` creates a new task message with the provided data and sends it to the Redis message broker.
5.  The API request can then immediately return a response to the user (e.g., with the `job_id`), without waiting for the background task to complete.

### Task Consumption Flow

1.  A Celery worker, running as a separate process, is constantly listening for new tasks on the Redis queue.
2.  When a new task message appears, the worker consumes it.
3.  The worker then executes the corresponding task function (defined in `servers/worker/tasks/`).
4.  The task function performs the long-running work (e.g., calling an AI provider to generate a video).
5.  Once the task is complete, its result and status are stored in the Celery backend (also Redis).

This architecture decouples the API server from the heavy lifting of background processing, making the application more responsive, scalable, and resilient.
