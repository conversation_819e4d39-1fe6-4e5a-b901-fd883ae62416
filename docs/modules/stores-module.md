# Stores Module Documentation

## 1. Overview

The `stores` module is responsible for managing the e-commerce stores connected to the platform. It handles the creation, configuration, and lifecycle of each store, and it is the central point for initiating data synchronization tasks.

**Key Responsibilities:**

-   Storing connection details for each store (e.g., Shopify domain, API tokens).
-   Linking stores to their owners (users) and tenants.
-   Providing API endpoints for managing stores (create, read, update, delete, connect, disconnect).
-   Initiating and tracking data synchronization jobs for stores.

## 2. Directory Structure

```
stores/
├── __init__.py
├── models.py             # SQLAlchemy model for Store
├── progress_service.py   # Service for tracking sync progress
├── router.py             # API endpoints for store management and sync
├── schemas.py            # Pydantic schemas for store data
├── service.py            # Business logic for store operations
└── sync_service.py       # High-level service for coordinating sync operations
```

## 3. Key Components

### a. Data Models (`models.py`)

-   **`Store`**: The core model representing a connected e-commerce store. It stores the platform type (e.g., `shopify`), domain, and API credentials.
    -   It also holds the Airbyte-related IDs (`airbyte_source_id`, `airbyte_connection_id`) which are essential for the data synchronization process.
-   **`SyncProgress`**: A model to track the real-time progress of a synchronization job for a store.

### b. Store Service (`service.py`)

-   **`StoreService`**: Handles the core business logic for store management.
    -   `create_store`: Creates a new store record and, crucially, triggers the setup of the Airbyte infrastructure for that store by calling `airbyte_service.setup_shop_sync`.
    -   `test_connection`: Tests the connection to a store's API to ensure the provided credentials are valid.

### c. Sync Service (`sync_service.py`)

-   **`StoreSyncService`**: A high-level service that coordinates the synchronization process. It acts as a bridge between the platform-specific API service (e.g., `ShopifyGraphQLService`) and the generic database service (`DBService`).
-   It uses a `DBService` to handle the actual database operations, making the sync logic independent of the database schema.

### d. Progress Service (`progress_service.py`)

-   **`ProgressService`**: Manages the `SyncProgress` records, providing a way to create, update, and retrieve the progress of sync jobs. This is used to provide real-time feedback to the user in the frontend.

### e. API Router (`router.py`)

-   Provides a rich set of endpoints for managing stores and synchronization:
    -   `/stores`: Standard CRUD endpoints for stores.
    -   `/stores/{store_id}/connect`, `/stores/{store_id}/disconnect`: To activate or deactivate a store's integration.
    -   `/stores/{store_id}/sync/products`: To manually trigger a product sync for a store.
    -   `/stores/{store_id}/sync-progress`: To get the real-time progress of a sync job.

## 4. Store Connection and Sync Flow

1.  A user adds a new store through the frontend.
2.  The `POST /api/stores` endpoint is called.
3.  `StoreService.create_store` is invoked. It saves the store's details and then calls `airbyte_service.setup_shop_sync`.
4.  The `AirbyteService` communicates with the Airbyte API to create a new source, a destination (or use a shared one), and a connection to link them. The IDs for these Airbyte resources are saved in the `Store` record.
5.  Once the Airbyte connection is established, an initial sync is automatically triggered.
6.  Subsequent syncs can be triggered manually via the API or automatically by webhooks.
7.  When a sync is in progress, the frontend can poll the `/api/stores/{store_id}/sync-progress` endpoint to display a real-time progress bar to the user.

This module provides a robust and user-friendly way to manage store connections and data synchronization, which is fundamental to the platform's operation.