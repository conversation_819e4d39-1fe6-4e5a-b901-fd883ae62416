# Storage Module Documentation

## 1. Overview

The `storage` module provides a crucial abstraction layer for handling file storage. It is designed to be a simple, centralized service for uploading and managing media files, with support for different storage backends like local disk, Amazon S3, or Cloudflare R2.

**Key Responsibilities:**

-   Providing a unified interface for file uploads.
-   Handling different storage providers based on the application's configuration.
-   Serving stored files via a public URL.

## 2. Directory Structure

```
storage/
├── __init__.py
├── local_storage.py  # Implementation for storing files on the local disk
├── router.py         # API endpoints for file uploads and access
└── storage_service.py  # Main service that delegates to the configured storage provider
```

## 3. Key Components

### a. Storage Service (`storage_service.py`)

-   **`MediaStorageService`**: The main service that acts as a facade for the actual storage implementation.
    -   It reads the `STORAGE_PROVIDER` setting from the application configuration to determine which backend to use (e.g., `local`, `s3`).
    -   `upload_media`: The primary method for uploading a file. It takes the file content and metadata, and then calls the corresponding method on the configured storage provider.
    -   `delete_media`: Deletes a file from storage.
    -   `get_public_url`: Returns the publicly accessible URL for a stored file.

### b. Local Storage (`local_storage.py`)

-   **`LocalStorage`**: An implementation of the storage service that saves files to the local filesystem.
    -   This is the default provider and is useful for development and testing environments.
    -   It saves files to a directory specified by the `LOCAL_STORAGE_PATH` setting.
    -   It constructs a public URL based on the `LOCAL_STORAGE_BASE_URL`.

### c. API Router (`router.py`)

-   **`POST /upload`**: An endpoint that allows for direct file uploads. This can be used for features like uploading a reference image.
-   **`GET /files/{file_path:path}`**: An endpoint to serve files directly from local storage. This is mainly for development and would typically be handled by a dedicated file server or CDN in production.

## 4. How It Works

The storage module is used by other parts of the application, especially the `media` module, to store generated content.

### File Upload Flow

1.  A media generation task in `media_tasks.py` has a generated media file (e.g., an image or video) in memory or as a temporary local file.
2.  It calls `media_storage_service.upload_media`, passing the file content and metadata (like filename and content type).
3.  The `MediaStorageService` checks the configured `STORAGE_PROVIDER`.
4.  If the provider is `local`, it calls the `LocalStorage` implementation to save the file to the local disk.
5.  If the provider were `s3` (not yet implemented, but the architecture supports it), it would use a library like `boto3` to upload the file to an S3 bucket.
6.  The storage service returns a `MediaFile` object containing the public URL of the uploaded file.
7.  The media generation task then saves this URL to the `MediaVariant` record in the database.

By abstracting the storage implementation, this module makes it easy to switch between different storage backends (e.g., from local storage in development to S3 in production) with minimal changes to the rest of the application code.
