# Customers Module Documentation

## 1. Overview

The `customers` module is responsible for managing customer data that is synchronized from connected e-commerce platforms. It provides a centralized repository for customer information, which can then be used for analytics, personalization, and other features.

**Key Responsibilities:**

-   Storing customer profile information, including contact details and addresses.
-   Linking customers to their respective stores.
-   Providing API endpoints for CRUD (Create, Read, Update, Delete) operations on customer data.

## 2. Directory Structure

```
customers/
├── __init__.py
├── models.py         # SQLAlchemy models for Customer and CustomerAddress
├── router.py         # API endpoints for customer management
├── schemas.py        # Pydantic schemas for customer data
└── service.py        # Business logic for customer operations
```

## 3. Key Components

### a. Data Models (`models.py`)

-   **`Customer`**: The main model for storing customer information. It includes fields for email, name, address, and marketing preferences.
    -   It has a relationship to the `Store` model, linking each customer to the store they belong to.
    -   It also has a relationship to the `Order` model, allowing for easy access to a customer's order history.
-   **`CustomerAddress`**: A separate model for storing multiple addresses for a single customer.

### b. Customer Service (`service.py`)

-   **`CustomerService`**: Inherits from the `BaseService` and provides the core logic for customer data management.
    -   `get_by_store_external_id`: Retrieves all customers for a given store.
    -   `create_customer_with_addresses`: A transactional method that creates a new customer along with their associated addresses.

### c. API Router (`router.py`)

-   Provides standard RESTful endpoints for managing customers:
    -   `GET /`: List all customers for the current user's stores.
    -   `POST /`: Create a new customer.
    -   `GET /{customer_id}`: Retrieve a specific customer.
    -   `PUT /{customer_id}`: Update a customer.
    -   `DELETE /{customer_id}`: Delete a customer.
-   All endpoints include ownership checks to ensure that a user can only access customers belonging to their own stores.

## 4. How It Works

The `customers` module primarily serves as a data store for customer information that is synced from external platforms. The typical flow is as follows:

1.  A data synchronization task (in the `sync` module) fetches customer data from a platform like Shopify.
2.  The sync task then calls the `CustomerService` to create or update customer records in the local database.
3.  The `CustomerService` uses the `Customer` and `CustomerAddress` models to store the data.
4.  Once the data is in the local database, it can be accessed via the `/api/customers` endpoints for display in the frontend or for use in other services (like analytics).

This module provides a clean separation between the raw customer data synced from external platforms and the application's internal representation of that data, making it easier to manage and use customer information throughout the system.
