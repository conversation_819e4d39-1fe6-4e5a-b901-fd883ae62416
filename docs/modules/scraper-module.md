# Scraper Module Documentation

## 1. Overview

The `scraper` module provides the functionality to scrape product and collection data from e-commerce websites. This is particularly useful for importing data from platforms that are not yet supported by the main data synchronization system (which uses Airbyte).

**Key Responsibilities:**

-   Validating URLs to check if they can be scraped.
-   Managing scraping jobs.
-   Storing scraped data (products, collections) in dedicated database tables.
-   Providing API endpoints for initiating and monitoring scraping jobs.

## 2. Directory Structure

```
scraper/
├── __init__.py
├── models.py         # SQLAlchemy models for scraped data and jobs
├── router.py         # API endpoints for the scraper
├── schemas.py        # Pydantic schemas for scraper-related data
└── service.py        # Business logic for scraping operations
```

## 3. Key Components

### a. Data Models (`models.py`)

-   **`ScrapedDocument`**: Represents a scraping session for a particular domain or URL. It tracks the overall status and progress of the scraping process for that site.
-   **`ScrapingJob`**: Represents a single scraping task within a document (e.g., scraping a specific page).
-   **`ScrapedProduct`** and **`ScrapedCollection`**: These models store the actual data that has been extracted from a website. They are designed to hold the key information about products and collections in a structured way.
-   **`ScrapingPlatform`**: A model to store information about supported scraping platforms and their configurations.

### b. Scraper Service (`service.py`)

-   **`ScraperService`**: The core service for the scraper module.
    -   `validate_url`: Checks if a given URL is valid and attempts to detect the e-commerce platform (e.g., Shopify, WooCommerce).
    -   `start_scraping`: Initiates a new scraping job by creating a `ScrapedDocument` and a `ScrapingJob` record.
    -   `get_stats`, `get_documents`, `get_active_jobs`: Methods for retrieving statistics and information about scraping activities.

### c. API Router (`router.py`)

-   Provides endpoints for interacting with the scraper:
    -   `/validate`: To check if a URL is scrapable.
    -   `/scrape`: To start a new scraping job.
    -   `/stats`, `/documents`, `/jobs`: To monitor the progress and results of scraping.
    -   `/products`, `/collections`: To retrieve the data that has been scraped.
    -   `/import`: A placeholder endpoint for a future feature to import scraped data into the main product catalog.

## 4. How It Works

The scraper module is designed to work as follows:

1.  A user provides a URL to the `/api/scraper/scrape` endpoint.
2.  The `ScraperService` creates a `ScrapedDocument` and a `ScrapingJob` to track the task.
3.  A background task (which would be a Celery task in a full implementation) is enqueued to perform the actual scraping.
4.  The background task would then use a web scraping library (like BeautifulSoup or Scrapy) to navigate the website, extract product and collection data, and save it to the `scraped_products` and `scraped_collections` tables.
5.  The user can monitor the progress of the job via the `/jobs/{job_id}` endpoint and view the scraped data through the `/products` and `/collections` endpoints.
6.  In the future, the `/import` endpoint would allow the user to select scraped products and import them into their main product catalog, making them available for media generation.

This module provides a powerful way to quickly onboard products from a wide range of e-commerce sites, complementing the more structured data synchronization provided by the `sync` module.
