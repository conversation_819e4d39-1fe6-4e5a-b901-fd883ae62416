# Assets Module Documentation

## 1. Overview

The `assets` module is responsible for managing and providing access to all media assets within the system. This includes images and videos that are part of the original product data from e-commerce platforms, as well as AI-generated media.

**Key Responsibilities:**

-   Providing a unified API to access all media assets, regardless of their source (product data or AI generation).
-   Paginating and searching through a potentially large library of assets.
-   Handling asset-related operations, such as reassigning an asset to a different product.

## 2. Directory Structure

```
assets/
├── __init__.py
├── models.py         # SQLAlchemy model for assets (generic media items)
├── router.py         # API endpoints for assets
├── schemas.py        # Pydantic schemas for asset data
└── service.py        # Business logic for asset management
```

## 3. Key Components

### a. Asset Model (`models.py`)

-   The `Asset` model is a generic representation of a media item. It can store information about images, videos, etc.
-   It includes fields for the asset `type`, `src` (URL), `alt` text, and source information (`product_id`, `store_id`).
-   The `source_type` field is important for distinguishing between assets that came from the original product (`product`) and those generated by AI (`ai_generated`).

### b. Asset Service (`service.py`)

-   **`get_assets_for_user`**: This is the core method of the service. It gathers all media from two main sources:
    1.  **Product Images**: It queries the `products` and `product_images` tables to get all images associated with the user's stores.
    2.  **AI-Generated Assets**: It queries the `generated_assets` table to get all media created by the user.
-   It then combines these two sources into a single list of `MediaItem` objects, which is a standardized format for the frontend.
-   It also handles pagination and search functionality.
-   **`reassign_asset_to_product`**: This method allows an asset to be moved from one product to another, providing flexibility in media management.

### c. API Router (`router.py`)

-   **`GET /`**: The main endpoint for fetching assets. It supports pagination (`page`, `limit`) and `search` query parameters.
-   **`POST /reassign`**: The endpoint for reassigning an asset to a new product.

### d. Schemas (`schemas.py`)

-   **`MediaItem`**: A key schema that defines the structure of an asset as it is presented to the frontend. This provides a consistent interface for different types of media.
-   **`PaginatedAssetResponse`**: The response schema for the `GET /` endpoint, including the list of assets and pagination details.

## 4. How It Works

The assets module acts as an aggregation and presentation layer for all media in the system. When the frontend requests the asset library, the following happens:

1.  The `GET /api/assets/` endpoint is called.
2.  The `asset_service.get_assets_for_user` method is invoked.
3.  The service queries the database to get all of the user's stores.
4.  It then fetches all products and their associated images for those stores.
5.  Simultaneously, it fetches all AI-generated assets for the user.
6.  Both lists of media are transformed into the standardized `MediaItem` format.
7.  The combined list is paginated and returned to the frontend.

This approach provides a unified and scalable way to manage a growing library of media assets from different sources.
