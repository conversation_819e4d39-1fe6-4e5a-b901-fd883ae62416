# Notifications Module Documentation

## 1. Overview

The `notifications` module is responsible for handling all outgoing communications from the application to the users, primarily through email.

**Key Responsibilities:**

-   Sending transactional emails, such as password reset instructions.
-   Providing a simple, reusable service for sending emails.

## 2. Directory Structure

```
notifications/
├── __init__.py
└── email_service.py  # Service for sending emails
```

## 3. Key Components

### a. Email Service (`email_service.py`)

-   **`EmailService`**: The core component of the module.
    -   It is configured with SMTP server settings from the application's main configuration (`core/config.py`).
    -   `send_email`: A general-purpose method for sending HTML and plain text emails.
    -   `send_password_reset_email`: A specific method that formats and sends the password reset email, including a unique reset link.
-   In a development environment, if SMTP settings are not provided, the service will simply print the email content to the console instead of sending it. This is useful for debugging email content without needing a live SMTP server.

## 4. How It Works

The `EmailService` is used by other modules to send emails. The most common use case is the password reset flow in the `auth` module:

1.  A user requests a password reset via the `/api/auth/forgot-password` endpoint.
2.  The `AuthService` generates a password reset token and saves it to the database.
3.  It then calls `email_service.send_password_reset_email`, passing the user's email and the reset token.
4.  The `EmailService` constructs the email content, including the password reset URL, and sends the email using the configured SMTP server.

This module is designed to be simple and extensible. New email-sending methods for other types of notifications (e.g., welcome emails, billing notifications) can be easily added to the `EmailService`.
