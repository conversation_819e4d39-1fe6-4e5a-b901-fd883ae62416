# Media Module Documentation

## 1. Overview

The `media` module is the heart of the AI content generation capabilities of the platform. It orchestrates the entire process of creating images, videos, and text, from analyzing product context to generating content with AI providers and ensuring its quality.

**Key Responsibilities:**

-   Managing media generation jobs and their variants.
-   Integrating with a pluggable system of AI providers.
-   Using specialized "engines" for context analysis, prompt engineering, and quality assurance.
-   Handling the full lifecycle of media generation, from request to final asset.

## 2. Directory Structure

```
media/
├── __init__.py
├── common/                 # Shared utilities for the media module
├── engines/                # Core AI-related logic engines
│   ├── context_engine.py
│   ├── prompt_engine.py
│   └── quality_engine.py
├── models.py               # SQLAlchemy models for media jobs, variants, etc.
├── processors/             # Logic for background task processing
│   ├── media_processor.py
│   └── media_push_processor.py
├── providers/              # Pluggable AI provider integrations
│   ├── base.py
│   ├── manager.py
│   ├── image/
│   ├── text/
│   └── video/
├── router.py               # API endpoints for media generation
├── schemas.py              # Pydantic schemas for media data
└── service.py              # Core business logic for media generation
```

## 3. Key Components

### a. Media Service (`service.py`)

-   **`MediaGenerationService`**: The main service that orchestrates the media generation process.
    -   `create_generation_jobs`: Creates `MediaJob` and `MediaVariant` records in the database when a new generation request is received.
    -   `generate_media_with_provider`: The core method that takes a request and routes it to the appropriate AI provider for generation.
    -   It uses a provider manager (`providers/manager.py`) to dynamically select and initialize AI providers.

### b. AI Engines (`engines/`)

-   **`ContextEngine`**: Analyzes product data to create a rich, structured context for generation. It understands product categories, styles, features, and target audiences.
-   **`PromptEngine`**: Uses the context from the `ContextEngine` to generate sophisticated, professional-grade prompts for the AI providers. It has a template-based system to create different styles of content.
-   **`QualityEngine`**: A placeholder for a future component that will be responsible for assessing the quality of generated media, checking for brand compliance, and flagging content for review.

### c. AI Providers (`providers/`)

-   This is a pluggable system that allows for easy integration of different AI services.
-   **`manager.py`**: A simple, type-safe provider manager that auto-initializes providers on first access. It replaces a more complex registry system.
-   **`base.py`**: Defines the abstract base classes (`ImageProvider`, `VideoProvider`, `TextProvider`) that all provider plugins must implement.
-   Each provider (e.g., `BananaProvider`, `Veo3Provider`, `GeminiProvider`) is implemented in its own file and handles the specific API interactions for that service.

### d. Processors (`processors/`)

-   **`MediaProcessor`**: Contains the logic that is executed by the Celery worker for a media generation task. It calls the `MediaGenerationService` to do the actual work.
-   **`MediaPushProcessor`**: Handles the task of pushing a generated media variant to an e-commerce platform like Shopify.

### e. API Router (`router.py`)

-   `/generate`: The main endpoint to initiate a media generation job. It creates the job and enqueues a background task.
-   `/jobs/{job_id}`: An endpoint to poll for the status of a generation job.
-   `/push`: An endpoint to push a completed media variant to a store.

## 4. Media Generation Flow

1.  A user initiates a media generation request from the frontend, which hits the `/api/media/generate` endpoint.
2.  The `media_router` calls `media_service.create_generation_jobs` to create `MediaJob` and `MediaVariant` records in the database with a `pending` status.
3.  A Celery task (`generate_media`) is enqueued with the job ID.
4.  A Celery worker picks up the task and executes the `MediaProcessor`.
5.  The `MediaProcessor` calls `media_service.generate_media_with_provider`.
6.  The `media_service` uses the `ContextEngine` and `PromptEngine` to create a detailed prompt.
7.  It then uses the `providers.manager` to get the appropriate AI provider (e.g., `BananaProvider` for images).
8.  The provider's `generate_media` method is called, which makes the API request to the external AI service.
9.  Once the AI service returns the generated content, the `MediaProcessor` uploads the media to the application's object storage (e.g., S3).
10. The `MediaVariant` records are updated with the URLs of the stored media and their status is set to `completed`.
11. The `MediaJob` status is updated to `completed`.
12. The user can then view the generated media in the frontend by polling the `/api/media/jobs/{job_id}` endpoint.
