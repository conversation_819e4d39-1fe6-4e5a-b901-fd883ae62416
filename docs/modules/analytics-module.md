# Analytics Module Documentation

## 1. Overview

The `analytics` module is responsible for tracking, processing, and analyzing user interactions and system events. It provides insights into media performance, user engagement, and conversion funnels. The module also includes an A/B testing framework to experiment with different content strategies.

**Key Responsibilities:**

-   Ingesting analytics events (e.g., media views, clicks, purchases).
-   Processing events to generate aggregated metrics.
-   Providing API endpoints for retrieving analytics data.
-   Managing A/B testing experiments and feature flags.
-   Tracking conversion funnels to attribute purchases to media variants.

## 2. Directory Structure

```
analytics/
├── __init__.py
├── ab_testing.py           # A/B testing service and feature flag management
├── event_models.py         # SQLAlchemy models for analytics events and aggregates
├── event_schemas.py        # Pydantic schemas for analytics data
├── event_service.py        # Service for ingesting and querying analytics events
├── router.py               # API endpoints for analytics
├── service.py              # Main analytics service for high-level metrics
└── processors/
    ├── __init__.py
    └── analytics_processor.py  # Celery task processor for analytics events
```

## 3. Key Components

### a. Event Ingestion (`event_service.py`, `router.py`)

-   The `/api/analytics/events/ingest` and `/api/analytics/events/batch` endpoints receive analytics events from the frontend or other services.
-   The `AnalyticsEventService` handles the ingestion logic, including deduplication of events using a `dedup_token`.
-   Events are stored in the `analytics_events` table.

### b. Event Processing (`processors/analytics_processor.py`)

-   Analytics events are processed asynchronously by a Celery worker.
-   The `AnalyticsProcessor` handles batch processing of events, which is more efficient for high-volume data.
-   During processing, it can enrich events (e.g., with geolocation data from an IP address) and update aggregated metrics tables like `media_analytics`.

### c. Data Models (`event_models.py`)

-   **`AnalyticsEvent`**: Stores raw event data for every tracked interaction. This provides a detailed log for granular analysis.
-   **`ConversionFunnel`**: Tracks a user's journey from viewing media to making a purchase, allowing for conversion attribution.
-   **`MediaAnalytics`**: Stores pre-aggregated daily metrics for each media variant (views, plays, CTR, etc.). This powers fast dashboard queries.
-   **`ABTestExperiment`**: Defines the configuration for A/B tests.

### d. Analytics Service (`service.py`)

-   The `AnalyticsService` provides high-level methods for querying aggregated data, such as dashboard metrics (`get_dashboard_metrics`).
-   It can perform more complex analyses, like A/B test analysis and generating heatmaps (though some of these are mock implementations for now).

### e. A/B Testing (`ab_testing.py`)

-   The `ABTestingService` provides a framework for running A/B tests and managing feature flags.
-   **Feature Flags**: `is_feature_enabled()` allows for gradual rollouts or enabling/disabling features for specific user segments.
-   **Experiments**: `get_experiment_variant()` assigns users to different experiment groups (e.g., control vs. treatment) based on defined traffic splits.

## 4. Data Flow

### Event Tracking Flow

1.  A user action on the frontend (e.g., playing a video) triggers an API call to `/api/analytics/events/ingest`.
2.  The `AnalyticsEventService` validates the event and saves it to the `analytics_events` table with a `pending` status.
3.  A Celery task (`process_analytics`) is enqueued.
4.  The `AnalyticsProcessor` in a Celery worker picks up the task.
5.  The processor updates aggregated tables (`media_analytics`, `conversion_funnels`) based on the event data.
6.  The event in `analytics_events` is marked as `processed`.

### Analytics Query Flow

1.  The frontend requests data from an endpoint like `/api/analytics/dashboard`.
2.  The `analytics_router` calls the `AnalyticsService`.
3.  The `AnalyticsService` queries the pre-aggregated tables (e.g., `media_analytics`) to quickly retrieve the requested metrics.
4.  The data is returned to the frontend for visualization.

This separation of ingestion and processing, and the use of aggregated tables, ensures that the system can handle a high volume of analytics events without slowing down the user-facing API.
