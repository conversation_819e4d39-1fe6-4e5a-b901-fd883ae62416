# Auth Module Documentation

## 1. Overview

The `auth` module is responsible for all aspects of user authentication, authorization, and session management. It provides a secure entry point to the system and ensures that users can only access the resources they are permitted to.

**Key Responsibilities:**

-   User registration and password-based login.
-   OAuth 2.0 integration for social logins (Google, GitHub) and Shopify.
-   JWT (JSON Web Token) generation and validation for session management.
-   Password reset and email verification flows.
-   Multi-tenancy support through `User` and `Tenant` models.

## 2. Directory Structure

```
auth/
├── __init__.py
├── models.py         # SQLAlchemy models for User, Tenant, OAuthAccount, etc.
├── oauth_router.py   # API endpoints for OAuth flows
├── oauth_service.py  # Business logic for OAuth providers
├── router.py         # Main API endpoints for authentication (login, register, etc.)
├── schemas.py        # Pydantic schemas for auth-related data
└── service.py        # Core authentication logic (password hashing, JWT creation, etc.)
```

## 3. Key Components

### a. Core Authentication (`service.py`)

-   **`AuthService`**: The main service class for authentication.
    -   `create_access_token` / `create_refresh_token`: Generates JWTs.
    -   `verify_token`: Decodes and validates JWTs.
    -   `hash_password` / `verify_password`: Handles password hashing and verification using `passlib`.
    -   `authenticate_user`: Validates user credentials for login.
    -   `get_current_user`: A dependency used in API endpoints to get the authenticated user from a JWT.

### b. OAuth 2.0 Integration (`oauth_service.py`, `oauth_router.py`)

-   **`OAuthService`**: Manages the logic for different OAuth providers (Google, GitHub, Shopify).
    -   `get_authorization_url`: Generates the initial URL to redirect the user to for authorization.
    -   `exchange_code_for_token`: Exchanges the authorization code received from the provider for an access token.
    -   `get_user_info`: Fetches user information from the provider using the access token.
    -   `authenticate_or_create_user`: Creates a new user or links the OAuth account to an existing user based on email.
-   **`oauth_router.py`**: Provides the API endpoints for the OAuth flows, including the `/authorize` and `/callback` endpoints.

### c. Data Models (`models.py`)

-   **`User`**: The central model for a user account. It includes basic profile information, password hash, roles, and relationships to other models like `tenants` and `stores`.
-   **`Tenant`**: Represents a workspace or an organization, providing a basis for multi-tenancy. Each user is associated with a tenant.
-   **`OAuthAccount`**: Links a user's account to one or more OAuth providers.
-   **`UserSession`**, **`EmailVerification`**, **`PasswordReset`**: Models for managing sessions and token-based flows.

### d. API Endpoints (`router.py`)

-   `/register`: Creates a new user and a corresponding tenant.
-   `/login`: Authenticates a user and returns JWTs, typically set as secure, HTTP-only cookies.
-   `/logout`: Clears the session cookies.
-   `/refresh`: Allows obtaining a new access token using a refresh token.
-   `/me`: Returns the information of the currently authenticated user.
-   `/forgot-password` & `/reset-password`: Handle the password reset flow.

## 4. Authentication Flow

### a. Password-Based Login

1.  The user submits their email and password to the `/api/auth/login` endpoint.
2.  `AuthService.authenticate_user` verifies the credentials.
3.  If successful, `AuthService.create_access_token` and `create_refresh_token` generate JWTs.
4.  The API response sets these tokens as secure, HTTP-only cookies in the user's browser.
5.  For subsequent requests, the browser automatically sends the access token in the `Authorization` header.
6.  The `get_current_user` dependency in protected endpoints validates the token and retrieves the user.

### b. OAuth 2.0 Flow

1.  The frontend requests an authorization URL from `/api/auth/authorize` for a specific provider (e.g., Google).
2.  The user is redirected to the provider's authorization page.
3.  After authorization, the provider redirects the user back to the application's callback URL (`/api/auth/callback/{provider}`).
4.  The `oauth_callback` endpoint in `oauth_router.py` receives the authorization code.
5.  `OAuthService` exchanges the code for an access token and then fetches the user's profile information.
6.  `OAuthService.authenticate_or_create_user` either finds an existing user by email or creates a new one, and links the OAuth account.
7.  A new session is created for the user, and JWTs are issued, just as in the password-based flow.

This robust authentication system provides both security and flexibility, accommodating different authentication methods while maintaining a consistent session management mechanism using JWTs.
