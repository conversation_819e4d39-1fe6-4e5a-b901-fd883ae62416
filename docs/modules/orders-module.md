# Orders Module Documentation

## 1. Overview

The `orders` module is responsible for managing order data that is synchronized from connected e-commerce platforms. It serves as a local replica of the order information from stores like Shopify.

**Key Responsibilities:**

-   Storing order and line item data.
-   Linking orders to the corresponding store and customer.

## 2. Directory Structure

```
orders/
├── __init__.py
└── models.py  # SQLAlchemy models for Order and OrderLineItem
```

## 3. Key Components

### a. Data Models (`models.py`)

-   **`Order`**: The main model for storing order information. It includes details such as the order number, total price, financial status, and fulfillment status.
    -   It has relationships to the `Store` and `Customer` models.
    -   It also contains the `full_json` field, which stores the complete, raw JSON payload from the e-commerce platform. This is useful for preserving all original data and for future enhancements without requiring database schema changes.
-   **`OrderLineItem`**: Represents a single line item within an order, linking to a specific product and variant.

## 4. How It Works

The `orders` module is primarily a data-centric module. It does not have its own services or API endpoints for direct manipulation. Instead, its models are populated by the data synchronization process:

1.  The `sync` module fetches order data from a platform like Shopify via Airbyte.
2.  The `StoreSyncService` processes this data.
3.  It then uses the `Order` and `OrderLineItem` models to create or update order records in the local database.

This data can then be used by other modules, such as `analytics`, to calculate metrics like conversion rates and average order value.
