# Compliance Module Documentation

## 1. Overview

The `compliance` module is responsible for ensuring that all generated content adheres to brand guidelines, legal standards, and content safety policies. It acts as a validation layer to prevent the generation and distribution of inappropriate or non-compliant content.

**Key Responsibilities:**

-   Filtering out competitor brand names from generated text.
-   Detecting and flagging the use of trademarked or copyrighted terms.
-   Scanning for and flagging inappropriate or unsafe content.
-   Validating the rights for media assets used in generation (e.g., music, stock photos).

## 2. Directory Structure

```
compliance/
├── __init__.py
└── content_safety.py  # Service for content safety and legal compliance checks
```

## 3. Key Components

### a. Content Safety Service (`content_safety.py`)

-   **`ContentSafetyService`**: The core of the compliance module.
    -   `check_content_safety`: A comprehensive method that runs a series of checks on a piece of content (text or image).
    -   `_check_competitor_brands`: Scans text for mentions of a predefined list of competitor brands.
    -   `_check_trademark_violations`: Looks for trademark symbols (®, ™, ©) and keywords.
    -   `_check_medical_claims`: Flags potentially problematic medical claims in text.
    -   `_detect_logos`: A placeholder for a future feature that would use a vision AI to detect logos in images.
    -   `filter_content`: Removes or redacts content that has been flagged.
    -   `validate_rights`: A placeholder for checking the licensing of media assets.

-   **`ContentFlag` Enum**: Defines the different types of content safety flags that can be raised.

## 4. How It Works

The `ContentSafetyService` is designed to be integrated into the media generation pipeline. After a piece of content is generated by an AI provider, but before it is saved and presented to the user, it can be passed through the `check_content_safety` method.

### Content Safety Check Flow

1.  A media generation task in `media_tasks.py` successfully receives a generated asset (e.g., a product description) from an AI provider.
2.  Before saving the asset, it calls `content_safety_service.check_content_safety` with the generated text.
3.  The service runs its various checks:
    -   It compares the text against its list of competitor brands.
    -   It uses regular expressions to search for trademark patterns and medical claim keywords.
4.  The service returns a result indicating whether the content is safe, along with a list of any flags that were raised and details about the issues.
5.  Based on the result, the media generation task can decide on the next steps:
    -   If the content is safe, it can be saved as is.
    -   If flags are raised, the content can be automatically filtered using `filter_content`.
    -   For more serious issues, the content can be flagged for manual review by a human moderator.

This proactive approach to content safety helps maintain the quality and integrity of the content generated by the platform, protecting both the users and the platform itself from potential legal and brand-related issues.
