# Templates Module Documentation

## 1. Overview

The `templates` module provides a system for managing and using pre-designed templates for media generation. These templates allow users to create high-quality, stylized content without needing to write complex prompts from scratch.

**Key Responsibilities:**

-   Providing a list of available media templates.
-   Filtering templates based on user's subscription plan and other criteria.
-   Providing customization options for each template.

## 2. Directory Structure

```
templates/
├── __init__.py
├── router.py         # API endpoints for templates
├── schemas.py        # Pydantic schemas for template data
└── template_service.py # Business logic for template management
```

## 3. Key Components

### a. Template Service (`template_service.py`)

-   **`TemplateService`**: The core of the module, responsible for all template-related logic.
    -   It currently uses a hardcoded list of mock templates (`MOCK_TEMPLATES`). In a production system, this data would be loaded from a database.
    -   `get_templates`: Retrieves a list of templates, with filtering capabilities for category, aspect ratio, and premium status. It also ensures that users on a free plan cannot access premium templates.
    -   `get_template_by_id`: Fetches a single template by its ID.
    -   `get_template_categories`: Returns a list of all available template categories and the number of templates in each.
    -   `get_customization_options`: Provides the customizable parameters for a specific template.

### b. API Router (`router.py`)

-   Provides endpoints for interacting with the template system:
    -   `GET /`: Lists all available templates with filtering and pagination.
    -   `GET /{template_id}`: Retrieves the details of a specific template.
    -   `GET /categories/list`: Lists all template categories.
    -   `GET /recommendations/get`: A placeholder for a future feature to provide personalized template recommendations.
    -   `GET /{template_id}/customization`: Returns the customization options for a template.

## 4. How It Works

The template system is primarily used by the frontend to provide users with a selection of styles for their media generation jobs.

1.  When a user is setting up a new media generation job, the frontend calls the `GET /api/templates` endpoint to display a list of available templates.
2.  The user can then select a template from the list.
3.  The frontend can optionally call `GET /api/templates/{template_id}/customization` to get the specific options that can be customized for the selected template (e.g., background color, music track).
4.  When the user initiates the generation, the `template_id` and any customization options are included in the request to `POST /api/media/generate`.
5.  The `PromptEngine` in the `media` module then uses this `template_id` to apply the corresponding styles and structure to the generated prompt, ensuring that the final output matches the look and feel of the selected template.

This system provides a user-friendly way to create professional-looking content while still allowing for a degree of customization.
