# Database Schema Documentation

This document provides a comprehensive overview of the database schema for the E-commerce Platform backend. The schema is organized by modules, reflecting the application's service-oriented architecture.

## Core Models (Auth & Billing)

These tables form the foundation of user management, multi-tenancy, and billing.

### `users`
- **Purpose**: Stores user account information. This is the central table for user identity.
- **Key Columns**:
    - `id`, `external_id`: Primary keys.
    - `email`, `username`: Unique identifiers for login.
    - `password_hash`: Hashed password for local authentication.
    - `role`: User role (e.g., `admin`, `user`).
    - `is_active`, `is_verified`: Flags for account status.
- **Relationships**: Has one-to-many relationships with `oauth_accounts`, `user_sessions`, `tenants`, `stores`, `media_jobs`, etc.

### `oauth_accounts`
- **Purpose**: Links user accounts to external OAuth providers (Google, Shopify) for social logins.
- **Key Columns**:
    - `user_id`: Foreign key to the `users` table.
    - `provider`, `provider_user_id`: Unique identifier for the user on the external platform.
    - `access_token`, `refresh_token`: Tokens for interacting with the provider's API.
- **Relationships**: Belongs to a `User`.

### `user_sessions`
- **Purpose**: Manages active user sessions and JWT refresh tokens.
- **Key Columns**:
    - `user_id`: Foreign key to the `users` table.
    - `session_token`, `refresh_token`: Secure tokens for session management.
    - `expires_at`: Expiration timestamp for the session.
- **Relationships**: Belongs to a `User`.

### `tenants`
- **Purpose**: Represents a workspace or organization, forming the basis of multi-tenancy. All of a user's resources are scoped to a tenant.
- **Key Columns**:
    - `owner_id`: Foreign key to the `users` table, identifying the tenant's owner.
    - `name`, `slug`: Tenant identifiers.
    - `stripe_customer_id`: Links to the customer record in Stripe for billing.
    - `credits`: Current credit balance for usage-based billing.
- **Relationships**: Belongs to a `User` (owner). Has one-to-many relationships with `stores`, `subscriptions`, etc.

### `subscriptions`
- **Purpose**: Tracks a tenant's subscription status with Stripe.
- **Key Columns**:
    - `tenant_id`: Foreign key to the `tenants` table.
    - `stripe_subscription_id`: The unique ID from Stripe.
    - `status`: The current status of the subscription (e.g., `active`, `trialing`, `canceled`).
    - `current_period_start`, `current_period_end`: Billing cycle dates.
- **Relationships**: Belongs to a `Tenant`.

### `billing_usage`
- **Purpose**: Records usage of metered resources (e.g., video generations) for billing purposes.
- **Key Columns**:
    - `tenant_id`: Foreign key to the `tenants` table.
    - `usage_type`: The type of resource consumed (e.g., `video_generation`).
    - `quantity`: The amount of the resource used.
- **Relationships**: Belongs to a `Tenant`.

### `invoices`
- **Purpose**: Stores a record of invoices from Stripe.
- **Key Columns**:
    - `tenant_id`: Foreign key to the `tenants` table.
    - `stripe_invoice_id`: The unique ID from Stripe.
    - `status`: The invoice status (e.g., `paid`, `open`, `void`).
    - `amount_due`, `amount_paid`: Invoice amounts.
- **Relationships**: Belongs to a `Tenant`.

### `payment_methods`
- **Purpose**: Stores customer payment methods from Stripe.
- **Key Columns**:
    - `tenant_id`: Foreign key to the `tenants` table.
    - `stripe_payment_method_id`: The unique ID from Stripe.
    - `type`, `card_brand`, `card_last4`: Details of the payment method.
- **Relationships**: Belongs to a `Tenant`.

### `credit_transactions`
- **Purpose**: Acts as a ledger for all credit-based transactions, including grants, purchases, and deductions.
- **Key Columns**:
    - `tenant_id`: Foreign key to the `tenants` table.
    - `transaction_type`: The type of transaction (e.g., `subscription_grant`, `usage`).
    - `amount`: The number of credits added or deducted.
    - `balance_after`: The remaining balance after the transaction.
- **Relationships**: Belongs to a `Tenant`.

## E-commerce Models (Stores, Products, Orders, Customers)

These tables store the data synchronized from connected e-commerce platforms like Shopify.

### `stores`
- **Purpose**: Represents a connected e-commerce store. It holds the necessary credentials and configuration for API interaction and data synchronization.
- **Key Columns**:
    - `owner_id`, `tenant_id`: Foreign keys linking the store to its owner and tenant.
    - `platform`: The e-commerce platform (e.g., `shopify`).
    - `admin_access_token`: Encrypted API token for the store.
    - `airbyte_source_id`, `airbyte_connection_id`: IDs for the Airbyte ETL pipeline.
- **Relationships**: Belongs to a `User` and a `Tenant`. Has many `products`, `orders`, `customers`, etc.

### `products`
- **Purpose**: The core table for product data.
- **Key Columns**:
    - `store_id`: Foreign key to the `stores` table.
    - `platform_product_id`: The product's unique ID on the source platform.
    - `title`, `description`, `vendor`, `product_type`: Core product attributes.
    - `full_json`: The complete raw JSON data from the platform, for completeness.
- **Relationships**: Belongs to a `Store`. Has many `product_variants` and `product_images`.

### `product_variants`
- **Purpose**: Represents a specific version of a product (e.g., a certain size or color).
- **Key Columns**:
    - `product_id`: Foreign key to the `products` table.
    - `platform_variant_id`: The variant's unique ID on the source platform.
    - `sku`, `price`, `quantity`: Key variant attributes.
- **Relationships**: Belongs to a `Product`.

### `product_images`
- **Purpose**: Stores product images.
- **Key Columns**:
    - `product_id`: Foreign key to the `products` table.
    - `variant_id`: Optional foreign key to `product_variants` if the image is specific to a variant.
    - `src`: The URL of the image.
- **Relationships**: Belongs to a `Product` and optionally to a `ProductVariant`.

### `inventory_levels`
- **Purpose**: Tracks the inventory quantity of a product variant at a specific location.
- **Key Columns**:
    - `inventory_item_id`: Links to a `ProductVariant`.
    - `location_id`: The ID of the physical or virtual location.
    - `available`: The quantity available at that location.
- **Relationships**: Conceptually linked to `ProductVariant`.

### `orders`
- **Purpose**: Stores order data from the e-commerce platform.
- **Key Columns**:
    - `store_id`, `customer_id`: Foreign keys.
    - `platform_order_id`: The order's unique ID on the source platform.
    - `total_price`, `financial_status`, `fulfillment_status`: Key order details.
    - `full_json`: The complete raw JSON data from the platform.
- **Relationships**: Belongs to a `Store` and a `Customer`. Has many `order_line_items`.

### `order_line_items`
- **Purpose**: Represents a single line item within an order.
- **Key Columns**:
    - `order_id`: Foreign key to the `orders` table.
    - `product_id`, `variant_id`: Links to the specific product/variant purchased.
    - `quantity`, `price`: Item details.
- **Relationships**: Belongs to an `Order`.

### `customers`
- **Purpose**: Stores customer information from the e-commerce platform.
- **Key Columns**:
    - `store_id`: Foreign key to the `stores` table.
    - `shopify_customer_id`: The customer's unique ID on the source platform.
    - `email`, `first_name`, `last_name`: Customer contact information.
- **Relationships**: Belongs to a `Store`. Has many `orders` and `customer_addresses`.

### `customer_addresses`
- **Purpose**: Stores multiple shipping/billing addresses for a customer.
- **Key Columns**:
    - `customer_id`: Foreign key to the `customers` table.
- **Relationships**: Belongs to a `Customer`.

## Media & Generation Models (Media, Assets, Templates)

These tables manage the AI media generation process, from jobs and variants to the final assets.

### `media_jobs`
- **Purpose**: Tracks a request to generate media for a product. Contains all generated variants as JSON data.
- **Key Columns**:
    - `user_id`, `product_external_id`: Links to the user and the target product.
    - `status`: The overall status of the job (e.g., `pending`, `processing`, `completed`).
    - `media_type`, `provider`: The type of media requested and the AI provider used.
    - `variants`: JSON array containing all generated media assets with their URLs and metadata.
    - `celery_task_id`: The ID of the background task processing this job.
- **Relationships**: Belongs to a `User`.

### `templates`
- **Purpose**: Stores pre-defined templates that users can select to style their generated media.
- **Key Columns**:
    - `name`, `description`: Template details.
    - `provider`: The AI provider this template is designed for.
    - `template_config`: The JSON configuration that defines the template's style.
- **Relationships**: None.

### `generated_assets`
- **Purpose**: Stores references to all AI-generated media assets, providing a unified library for the user.
- **Key Columns**:
    - `user_id`, `product_id`: Links to the user and product.
    - `type`: The type of asset (`image` or `video`).
    - `file_uri`, `preview_uri`: URLs to the stored asset.
- **Relationships**: Belongs to a `User`.

### `assets`
- **Purpose**: A generic representation of a media item, used to aggregate both original product media and AI-generated media into a single view for the asset library.
- **Key Columns**:
    - `product_id`, `store_id`: Links to the source product and store.
    - `src`: The URL of the asset.
    - `source_type`: Distinguishes between `product` and `ai_generated` assets.
- **Relationships**: None.

## Sync & Scraper Models (Sync, Scraper)

These tables support the data synchronization and web scraping functionalities.

### `webhook_events`
- **Purpose**: Reliably stores incoming webhook payloads from platforms like Shopify for asynchronous processing.
- **Key Columns**:
    - `store_id`: Foreign key to the `stores` table.
    - `topic`: The webhook topic (e.g., `products/update`).
    - `payload`: The full JSON payload of the webhook.
    - `status`: The processing status (`pending`, `processed`, `failed`).
- **Relationships**: Belongs to a `Store`.

### `sync_checkpoints`
- **Purpose**: Tracks the progress of data synchronization for each store and entity type to enable incremental updates.
- **Key Columns**:
    - `store_id`: Foreign key to the `stores` table.
    - `entity_type`: The type of data being synced (e.g., `products`, `orders`).
    - `last_updated_at`: The timestamp of the last successfully synced record.
- **Relationships**: Belongs to a `Store`.

### `sync_jobs`
- **Purpose**: Records the history and status of individual Airbyte synchronization jobs.
- **Key Columns**:
    - `store_id`: Foreign key to the `stores` table.
    - `airbyte_job_id`: The job ID from the Airbyte API.
    - `status`: The job status (`running`, `succeeded`, `failed`).
- **Relationships**: Belongs to a `Store`.

### `dead_letter_queue`
- **Purpose**: Stores events (like webhooks) that failed to be processed after multiple retries, allowing for manual inspection and intervention.
- **Key Columns**:
    - `source_type`, `source_id`: Identifies the failed event.
    - `failure_reason`, `error_message`: Details about the failure.
- **Relationships**: Belongs to a `Store`.

### `scraped_documents` & `scraping_jobs`
- **Purpose**: Manage and track the process of scraping e-commerce websites. `scraped_documents` represents a session for a domain, and `scraping_jobs` tracks individual page scrapes.
- **Relationships**: A `ScrapedDocument` has many `ScrapingJobs`.

### `scraped_products` & `scraped_collections`
- **Purpose**: Store the structured data extracted from websites by the scraper.
- **Relationships**: Belong to a `ScrapedDocument`.

## Analytics Models

These tables are dedicated to tracking user interactions and system events for analytics and reporting.

### `analytics_events`
- **Purpose**: Stores raw event data for every tracked interaction (e.g., media views, clicks, purchases). This provides a detailed log for granular analysis.
- **Key Columns**:
    - `event_id`: A unique ID for deduplication.
    - `event_type`: The type of event (e.g., `media_view`, `cta_click`).
    - `tenant_id`, `session_id`, `user_id`: Contextual identifiers.
    - `media_job_id`, `product_id`: Links to the relevant media job and product.
- **Relationships**: Belongs to a `Tenant` and `MediaJob`.

### `conversion_funnels`
- **Purpose**: Tracks a user's journey from viewing media to making a purchase, enabling conversion attribution.
- **Key Columns**:
    - `session_id`: Groups events within a single user session.
    - `media_view_event_id`, `purchase_event_id`: Foreign keys to `analytics_events`, marking the start and end of the funnel.
    - `converting_media_job_id`: The specific media job credited with the conversion.
- **Relationships**: Belongs to a `Tenant` and `MediaJob`.

### `media_analytics`
- **Purpose**: Stores pre-aggregated daily metrics for each media job (views, plays, CTR, etc.). This powers fast dashboard queries.
- **Key Columns**:
    - `media_job_id`: Foreign key to the `media_jobs` table.
    - `date`: The day for which the metrics are aggregated.
    - `views`, `plays`, `completions`, `cta_clicks`, `purchases`: Aggregated metrics.
- **Relationships**: Belongs to a `Tenant` and `MediaJob`.

### `ab_test_experiments`
- **Purpose**: Defines the configuration and tracks the results of A/B tests between different media jobs.
- **Key Columns**:
    - `name`: The name of the experiment.
    - `control_job_id`, `test_job_ids`: The jobs being tested.
    - `winner_job_id`: The job that performed best, once statistically significant.
- **Relationships**: Belongs to a `Tenant` and `MediaJob`.
