<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Architecture Documentation</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            display: flex;
            height: 100vh;
            background-color: #f8f9fa;
        }

        #sidebar {
            width: 280px;
            background-color: #fff;
            border-right: 1px solid #dee2e6;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }

        #content {
            flex-grow: 1;
            padding: 30px;
            overflow-y: auto;
        }

        h1,
        h2,
        h3 {
            color: #343a40;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
        }

        ul {
            list-style-type: none;
            padding: 0;
        }

        li a {
            display: block;
            color: #495057;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.2s;
        }

        li a:hover,
        li a.active {
            background-color: #e9ecef;
            color: #007bff;
        }

        .module-group {
            margin-top: 20px;
        }

        .module-group h3 {
            font-size: 1.1em;
            color: #6c757d;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Style for mermaid diagrams */
        .mermaid {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 1.5rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div id="sidebar">
        <h2>Documentation</h2>
        <div class="module-group">
            <h3>Architecture</h3>
            <ul>
                <li><a href="#" onclick="loadContent('architecture/system-architecture.md')">System Architecture</a>
                </li>
                <li><a href="#" onclick="loadContent('architecture/media-module-overview.md')">Media Module Overview</a>
                </li>
                <li><a href="#" onclick="loadContent('architecture/adding-new-media-providers.md')">Adding New Media
                        Providers</a></li>
                <li><a href="#" onclick="loadContent('architecture/monitoring-architecture.md')">Monitoring
                        Architecture</a></li>
                <li><a href="#" onclick="loadContent('architecture/sync-flow-documentation.md')">Sync Flow
                        Documentation</a></li>
            </ul>
        </div>
        <div class="module-group">
            <h3>Database</h3>
            <ul>
                <li><a href="#" onclick="loadContent('database/database-schema.md')">Database Schema</a></li>
            </ul>
        </div>
        <div class="module-group">
            <h3>Modules</h3>
            <ul>
                <li><a href="#" onclick="loadContent('modules/core-module.md')">Core Module</a></li>
                <li><a href="#" onclick="loadContent('modules/analytics-module.md')">Analytics</a></li>
                <li><a href="#" onclick="loadContent('modules/assets-module.md')">Assets</a></li>
                <li><a href="#" onclick="loadContent('modules/auth-module.md')">Auth</a></li>
                <li><a href="#" onclick="loadContent('modules/billing-module.md')">Billing</a></li>
                <li><a href="#" onclick="loadContent('modules/compliance-module.md')">Compliance</a></li>
                <li><a href="#" onclick="loadContent('modules/customers-module.md')">Customers</a></li>
                <li><a href="#" onclick="loadContent('modules/media-module.md')">Media</a></li>
                <li><a href="#" onclick="loadContent('modules/notifications-module.md')">Notifications</a></li>
                <li><a href="#" onclick="loadContent('modules/orders-module.md')">Orders</a></li>
                <li><a href="#" onclick="loadContent('modules/processing-module.md')">Processing</a></li>
                <li><a href="#" onclick="loadContent('modules/products-module.md')">Products</a></li>
                <li><a href="#" onclick="loadContent('modules/queue-module.md')">Queue</a></li>
                <li><a href="#" onclick="loadContent('modules/scraper-module.md')">Scraper</a></li>
                <li><a href="#" onclick="loadContent('modules/storage-module.md')">Storage</a></li>
                <li><a href="#" onclick="loadContent('modules/stores-module.md')">Stores</a></li>
                <li><a href="#" onclick="loadContent('modules/sync-module.md')">Sync</a></li>
                <li><a href="#" onclick="loadContent('modules/templates-module.md')">Templates</a></li>
            </ul>
        </div>
        <div class="module-group">
            <h3>Misc</h3>
            <ul>
                <li><a href="#" onclick="loadContent('misc/prompting_best_practices.md')">Prompting Best Practices</a></li>
                <li><a href="#" onclick="loadContent('schema/shopify-schema.json')">Shopify Schema</a></li>
            </ul>
        </div>
    </div>
    <div id="content"></div>

    <script>
        mermaid.initialize({ startOnLoad: false, theme: 'base', themeVariables: { 'primaryColor': '#f8f9fa', 'lineColor': '#343a40', 'textColor': '#343a40' } });

        function loadContent(file) {
            const contentDiv = document.getElementById('content');
            const links = document.querySelectorAll('#sidebar a');

            fetch(file)
                .then(response => response.text())
                .then(text => {
                    if (file.endsWith('.json')) {
                        try {
                            const jsonContent = JSON.parse(text);
                            contentDiv.innerHTML = `<h1>${file.split('/').pop()}</h1><pre><code>${JSON.stringify(jsonContent, null, 2)}</code></pre>`;
                        } catch (e) {
                            contentDiv.innerHTML = `<h1>Error parsing JSON</h1><p>${e}</p><pre><code>${text}</code></pre>`;
                        }
                    } else {
                        // Convert markdown to HTML
                        contentDiv.innerHTML = marked.parse(text);

                        // Find all mermaid code blocks and render them
                        const mermaidElements = contentDiv.querySelectorAll('code.language-mermaid');
                        mermaidElements.forEach((element, index) => {
                            const diagramId = `mermaid-diagram-${Date.now()}-${index}`;
                            const diagramSource = element.textContent;

                            const mermaidContainer = document.createElement('div');
                            mermaidContainer.classList.add('mermaid');
                            mermaidContainer.textContent = diagramSource;

                            // Replace the <pre> element with our new container
                            element.parentElement.replaceWith(mermaidContainer);
                        });

                        // Re-run mermaid to render the newly added diagrams
                        mermaid.run();
                    }
                })
                .catch(err => {
                    contentDiv.innerHTML = `<h1>Error loading content</h1><p>${err}</p>`;
                });

            // Update active link in sidebar
            links.forEach(link => {
                if (link.getAttribute('onclick').includes(file)) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
        }

        // Load the initial document
        window.onload = () => {
            loadContent('architecture/system-architecture.md');
        };
    </script>
</body>
</html>